<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<%- include('./template/head.ejs') %>
<body ontouchstart="">
<%- include('./template/header.ejs') %>
  <article id="article">
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="article-title-0">资源文章</h2>
            <h3 data-i18n="article-title-1">SVGA 推出后已经得到了大量动画开发者的支持和肯定，阅读这些社区文章能帮助你了解更多 SVGA 的方方面面。</h3>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <ul>
              <li>
                <a target="_blank" href="https://www.jianshu.com/p/dfa16d9d67cd">
                  <h3>SVGA 背后的故事</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://jfson.github.io/2018/06/21/49-svga/">
                  <h3>SVGA 源码剖析</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://www.jianshu.com/p/8298015a02ff">
                  <h3>一种完美的动画实现方案</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://juejin.im/entry/59f1648f5188254115700ee6">
                  <h3>直播App中Android酷炫礼物动画实现方案（上篇）</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://juejin.im/entry/59f6c0ec6fb9a0451d40bfbf">
                  <h3>直播App中Android酷炫礼物动画实现方案（下篇）：SVGA由来与Lottie的对比</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://www.jianshu.com/p/18f7bc63945a">
                  <h3>iOS开发-SVGA Animation实现直播间礼物特效</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://zhuanlan.zhihu.com/p/32744810">
                  <h3>使用SVGA优化礼物动画</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://jfson.github.io/2018/01/08/41-anim/">
                  <h3>Android 动画库对比(Lottie 和 SVGA)</h3>
                </a>
              </li>
              <li>
                <a target="_blank" href="https://blog.csdn.net/gutaocslg/article/details/79908414">
                  <h3>GIF、Lottie、SVGA</h3>
                </a>
              </li>
              <li></li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </article>
<%- include('./template/footer.ejs') %>
<%- include('./template/script.ejs') %>
</body>
</html>
