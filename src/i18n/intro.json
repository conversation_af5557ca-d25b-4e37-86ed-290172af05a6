{"intro-title-0": {"en": "Introduction", "zh": "介绍"}, "intro-content-0": {"en": "SVGA is a cross platform animation format. Furthermore, SVGA is also a perfect solution for creating animation in cooperation with designers and developers. SVGA includes SVGAConverter and SVGAPlayer. Designers aim at the design of excellent visual effect and animation content, then export SVGA file by SVGAConverter. Developers use SVGAPlayer to play animation without extra coding. SVGAPlayer is available in different platform including iOS, Android, Web. ", "zh": "SVGA 是一种跨平台的开源动画格式，同时兼容 iOS / Android / Web。SVGA 除了使用简单，性能卓越，同时让动画开发分工明确，各自专注各自的领域，大大减少动画交互的沟通成本，提升开发效率。动画设计师专注动画设计，通过工具输出 svga 动画文件，提供给开发工程师在集成 svga player 之后直接使用。动画开发从未如此简单！"}, "intro-button": {"en": ">> Getting Started", "zh": ">> 立即使用"}}