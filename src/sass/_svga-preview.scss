@mixin preview {
  .center {
    padding: 50px;
  }
  .preview {
    margin: 0;
  }
  .qrcode {
    cursor: pointer;
    position: relative;
    user-select: none;
    &.active,
    &:hover {
      a {
        color: #fff;
        text-decoration: none;
        background-color: #a457d4;
      }
    }
    b {
      display: inline;
      font-weight: normal;
      & + b {
        display: none;
      }
    }
    &.active {
      b {
        display: none;
        & + b {
          display: inline;
        }
      }
    }
    a {
      pointer-events: none;
    }
    i {
      display: inline-block;
      margin-right: 5px;
      position: relative;
      vertical-align: top;
      top: 10px;
      width: 16px;
      height: 16px;
    }
    svg {
      width: 100%;
      height: 100%;
      fill: currentColor;
      color: currentColor;
      display: block;
    }
    &.active {
      ul {
        display: block;
      }
    }
    ul {
      top: -160px;
      left: 50%;
      margin-left: -75px;
      width: 150px;
      height: 150px;
      position: absolute;
      padding: 10px;
      background-color: #fff;
      box-shadow: 0 0 15px -1px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      pointer-events: none;
      display: none;
      &::before {
        bottom: -4px;
        left: 50%;
        width: 0;
        height: 0;
        margin-left: -5px;
        content: "";
        border-width: 5px;
        border-style: solid;
        border-color: transparent #fff #fff transparent;
        position: absolute;
        transform: rotate(45deg);
        border-radius: 3px;
        pointer-events: none;
        box-shadow: 0 0 15px -1px transparentize($color: #000, $amount: .9);
      }
      &::after {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 8px;
        content: "";
        background-color: #fff;
        position: absolute;
        pointer-events: none;
        border-radius: 4px;
      }
    }
    li {
      width: 130px;
      height: 130px;
      background-color: #eee;
      position: relative;
    }
    img,
    canvas {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      position: absolute;
    }
    p {
      top: 75px;
      left: 0;
      width: 100%;
      display: block;
      position: absolute;
      font-size: 14px;
      text-align: center;
    }
    em {
      top: 22px;
      left: 50%;
      margin-left: -20px;
      width: 40px;
      height: 40px;
      position: absolute;
      animation: rotate 2s linear both infinite;
      @keyframes rotate {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    }
  }
  // @media (min-height: 1010px) and (min-width: 1200px) {
  //   & + footer {
  //     left: 0;
  //     bottom: 0;
  //     width: 100%;
  //     position: fixed;
  //   }
  // }
}
