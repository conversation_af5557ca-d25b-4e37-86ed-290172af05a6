<!DOCTYPE html>
<html lang="zh-cmn-Hans" prefix="og: http://ogp.me/ns#">
<%- include('./template/head.ejs') %>
<body ontouchstart="">
<%- include('./template/header.ejs') %>
  <main role="main">
    <article id="index" itemscope itemtype="https://schema.org/SoftwareApplication">
      <section aria-labelledby="hero-title">
        <div class="center">
          <div class="container">
            <div class="col left">
              <h1 id="hero-title" data-i18n="index-title-0" itemprop="name">SVGA动画格式</h1>
              <h2 data-i18n="index-title-1" itemprop="headline">高性能动画播放体验</h2>
              <p data-i18n="index-title-2" itemprop="description">SVGA 是一种同时兼容 iOS / Android / Flutter / Web / HarmonyOS 的跨平台动画解决方案。</p>
            <p>
              <a href="./svga-preview.html">
                <button data-i18n="index-title-button" class="demo">立即体验</button>
              </a>
              <a target="_blank" href="https://github.com/svga">
                <button class="github"><svg aria-labelledby="simpleicons-github-dark-icon" lang="" role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title id="simpleicons-github-icon" lang="en">GitHub Dark icon</title><path fill="#7F8C8D" d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"></path></svg>GitHub</button>
              </a>
            </p>
            <p>
              <br>
            </p>
          </div>
          <div class="col right">
            <figure>
              <div id="index-response" class="response">
                <div class="web">
                  <div class="active"></div>
                  <canvas id="index-web" width="400" height="400"></canvas>
                </div>
                <div class="pad">
                  <div class="active"></div>
                  <canvas id="index-pad" width="250" height="250"></canvas>
                </div>
                <div class="phone">
                  <div class="active"></div>
                  <canvas id="index-phone" width="130" height="130"></canvas>
                </div>
              </div>
            </figure>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col left"><br></div>
          <div class="col right">
              <h2 data-i18n="index-reason-0">为什么选 SVGA？</h2>
          </div>
          <div class="clear"></div>
          <div class="col left">
            <figure itemscope itemtype="https://schema.org/ImageObject">
              <img src="/img/index-example.jpg" 
                   alt="SVGA动画效果展示 - 跨平台动画解决方案实际运行效果" 
                   itemprop="contentUrl"
                   loading="lazy"
                   width="360" 
                   height="650">
              <div class="canvas">
                <canvas id="index-example" width="360" height="360" 
                        aria-label="SVGA动画播放演示区域"></canvas>
              </div>
            </figure>
          </div>
          <div class="col right">
            <h3 data-i18n="index-reason-1">对开发者友好</h3>
            <p data-i18n="index-reason-2">便捷的 SDK 使得 SVGA 可运行在不同平台上，集成步骤轻松简单。</p>
            <h3 data-i18n="index-reason-3">对设计师友好</h3>
            <p data-i18n="index-reason-4">可以使用 After Effects 或是 Animate(Flash) 进行动画设计，SVGA 可以支持其中的大部分效果，设计师使用导出工具即可生成动画文件。</p>
            <h3 data-i18n="index-reason-5">性价比更高</h3>
            <p data-i18n="index-reason-6">动画文件体积更小，播放资源占用更优，动画还原效果更好。</p>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container align">
          <h2 data-i18n="index-about-0">关于 SVGA</h2>
          <div class="col">
            <div class="icon icon0"><i></i></div>
            <h3 data-i18n="index-about-1">集成指南</h3>
            <p data-i18n="index-about-2">通过集成指南，可以轻松的在 iOS / Android / Flutter / Web / HarmonyOS 中引入 SVGA 播放器并播放资源文件。</p>
          </div>
          <div class="col">
            <div class="icon icon1"><i></i></div>
            <h3 data-i18n="index-about-3">设计师工具</h3>
            <p data-i18n="index-about-4">使用设计师工具，可以轻松的将 Aep 或 Fla 格式的动画文件导出成 SVGA 格式资源文件。</p>
          </div>
          <div class="col">
            <div class="icon icon2"><i></i></div>
            <h3 data-i18n="index-about-5">开源代码</h3>
            <p data-i18n="index-about-6">各平台的播放器都是开源的，欢迎提交 PR 贡献代码。</p>
          </div>
          <div class="col">
            <div class="icon icon3"><i></i></div>
            <h3 data-i18n="index-about-7">技术支持</h3>
            <p><span data-i18n="index-about-8">在使用过程中，如有任何问题欢迎提交</span><a target="_blank" href="https://github.com/svga/docs/issues" data-i18n="index-about-9">issue</a><!-- <span data-i18n="index-about-10">或者加入我们的 QQ 群参与讨论：</span><a href="//shang.qq.com/wpa/qunwpa?idkey=f795fb4b48f0797dbd6baca5a1597da1bb2caa2a21ee0273ea7bc1173c489cc0">576461005</a><span data-i18n="index-about-11">。</span>--></p>
          </div>
        </div>
      </div>
    </section>
  </article>
</main>
<%- include('./template/footer.ejs') %>
<%- include('./template/script.ejs') %>
</body>
</html>
