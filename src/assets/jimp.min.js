/*
Jimp v0.6.3
https://github.com/oliver-moran/jimp
Ported for the Web by Phil Seaton
MIT License

Copyright (c) 2018 Oliver Moran

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/
if((void 0===window||!window)&&"undefined"!=typeof self)var window=self;!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{(void 0!==window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).jimp=t()}}(function(){return function o(a,s,u){function f(e,t){if(!s[e]){if(!a[e]){var r="function"==typeof require&&require;if(!t&&r)return r(e,!0);if(l)return l(e,!0);var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}var i=s[e]={exports:{}};a[e][0].call(i.exports,function(t){return f(a[e][1][t]||t)},i,i.exports,o,a,s,u)}return s[e].exports}for(var l="function"==typeof require&&require,t=0;t<u.length;t++)f(u[t]);return f}({1:[function(t,e,r){var n=t("./src/converter");function i(t,e){var r=new n(t,e);return function(t){return r.convert(t)}}i.BIN="01",i.OCT="01234567",i.DEC="0123456789",i.HEX="0123456789abcdef",e.exports=i},{"./src/converter":2}],2:[function(t,e,r){"use strict";function n(t,e){if(!(t&&e&&t.length&&e.length))throw new Error("Bad alphabet");this.srcAlphabet=t,this.dstAlphabet=e}n.prototype.convert=function(t){var e,r,n,i={},o=this.srcAlphabet.length,a=this.dstAlphabet.length,s=t.length,u="string"==typeof t?"":[];if(!this.isValid(t))throw new Error('Number "'+t+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return t;for(e=0;e<s;e++)i[e]=this.srcAlphabet.indexOf(t[e]);do{for(e=n=r=0;e<s;e++)a<=(r=r*o+i[e])?(i[n++]=parseInt(r/a,10),r%=a):0<n&&(i[n++]=0);s=n,u=this.dstAlphabet.slice(r,r+1).concat(u)}while(0!==n);return u},n.prototype.isValid=function(t){for(var e=0;e<t.length;++e)if(-1===this.srcAlphabet.indexOf(t[e]))return!1;return!0},e.exports=n},{}],3:[function(w,E,t){(function(e){"use strict";function o(t,e){if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0}function a(t){return e.Buffer&&"function"==typeof e.Buffer.isBuffer?e.Buffer.isBuffer(t):!(null==t||!t._isBuffer)}var l=w("util/"),n=Object.prototype.hasOwnProperty,h=Array.prototype.slice,r="foo"===function(){}.name;function s(t){return Object.prototype.toString.call(t)}function u(t){return!a(t)&&("function"==typeof e.ArrayBuffer&&("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):!!t&&(t instanceof DataView||!!(t.buffer&&t.buffer instanceof ArrayBuffer))))}var f=E.exports=t,i=/\s*function\s+([^\(\s]*)\s*/;function c(t){if(l.isFunction(t)){if(r)return t.name;var e=t.toString().match(i);return e&&e[1]}}function d(t,e){return"string"==typeof t?t.length<e?t:t.slice(0,e):t}function p(t){if(r||!l.isFunction(t))return l.inspect(t);var e=c(t);return"[Function"+(e?": "+e:"")+"]"}function m(t,e,r,n,i){throw new f.AssertionError({message:r,actual:t,expected:e,operator:n,stackStartFunction:i})}function t(t,e){t||m(t,!0,e,"==",f.ok)}function b(t,e,r,n){if(t===e)return!0;if(a(t)&&a(e))return 0===o(t,e);if(l.isDate(t)&&l.isDate(e))return t.getTime()===e.getTime();if(l.isRegExp(t)&&l.isRegExp(e))return t.source===e.source&&t.global===e.global&&t.multiline===e.multiline&&t.lastIndex===e.lastIndex&&t.ignoreCase===e.ignoreCase;if(null!==t&&"object"==typeof t||null!==e&&"object"==typeof e){if(u(t)&&u(e)&&s(t)===s(e)&&!(t instanceof Float32Array||t instanceof Float64Array))return 0===o(new Uint8Array(t.buffer),new Uint8Array(e.buffer));if(a(t)!==a(e))return!1;var i=(n=n||{actual:[],expected:[]}).actual.indexOf(t);return-1!==i&&i===n.expected.indexOf(e)||(n.actual.push(t),n.expected.push(e),function(t,e,r,n){if(null==t||null==e)return!1;if(l.isPrimitive(t)||l.isPrimitive(e))return t===e;if(r&&Object.getPrototypeOf(t)!==Object.getPrototypeOf(e))return!1;var i=g(t),o=g(e);if(i&&!o||!i&&o)return!1;if(i)return t=h.call(t),e=h.call(e),b(t,e,r);var a,s,u=v(t),f=v(e);if(u.length!==f.length)return!1;for(u.sort(),f.sort(),s=u.length-1;0<=s;s--)if(u[s]!==f[s])return!1;for(s=u.length-1;0<=s;s--)if(a=u[s],!b(t[a],e[a],r,n))return!1;return!0}(t,e,r,n))}return r?t===e:t==e}function g(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function _(t,e){if(!t||!e)return!1;if("[object RegExp]"==Object.prototype.toString.call(e))return e.test(t);try{if(t instanceof e)return!0}catch(t){}return!Error.isPrototypeOf(e)&&!0===e.call({},t)}function y(t,e,r,n){var i;if("function"!=typeof e)throw new TypeError('"block" argument must be a function');"string"==typeof r&&(n=r,r=null),i=function(t){var e;try{t()}catch(t){e=t}return e}(e),n=(r&&r.name?" ("+r.name+").":".")+(n?" "+n:"."),t&&!i&&m(i,r,"Missing expected exception"+n);var o="string"==typeof n,a=!t&&i&&!r;if((!t&&l.isError(i)&&o&&_(i,r)||a)&&m(i,r,"Got unwanted exception"+n),t&&i&&r&&!_(i,r)||!t&&i)throw i}f.AssertionError=function(t){var e;this.name="AssertionError",this.actual=t.actual,this.expected=t.expected,this.operator=t.operator,t.message?(this.message=t.message,this.generatedMessage=!1):(this.message=d(p((e=this).actual),128)+" "+e.operator+" "+d(p(e.expected),128),this.generatedMessage=!0);var r=t.stackStartFunction||m;if(Error.captureStackTrace)Error.captureStackTrace(this,r);else{var n=new Error;if(n.stack){var i=n.stack,o=c(r),a=i.indexOf("\n"+o);if(0<=a){var s=i.indexOf("\n",a+1);i=i.substring(s+1)}this.stack=i}}},l.inherits(f.AssertionError,Error),f.fail=m,f.ok=t,f.equal=function(t,e,r){t!=e&&m(t,e,r,"==",f.equal)},f.notEqual=function(t,e,r){t==e&&m(t,e,r,"!=",f.notEqual)},f.deepEqual=function(t,e,r){b(t,e,!1)||m(t,e,r,"deepEqual",f.deepEqual)},f.deepStrictEqual=function(t,e,r){b(t,e,!0)||m(t,e,r,"deepStrictEqual",f.deepStrictEqual)},f.notDeepEqual=function(t,e,r){b(t,e,!1)&&m(t,e,r,"notDeepEqual",f.notDeepEqual)},f.notDeepStrictEqual=function t(e,r,n){b(e,r,!0)&&m(e,r,n,"notDeepStrictEqual",t)},f.strictEqual=function(t,e,r){t!==e&&m(t,e,r,"===",f.strictEqual)},f.notStrictEqual=function(t,e,r){t===e&&m(t,e,r,"!==",f.notStrictEqual)},f.throws=function(t,e,r){y(!0,t,e,r)},f.doesNotThrow=function(t,e,r){y(!1,t,e,r)},f.ifError=function(t){if(t)throw t};var v=Object.keys||function(t){var e=[];for(var r in t)n.call(t,r)&&e.push(r);return e}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{"util/":6}],4:[function(t,e,r){"function"==typeof Object.create?e.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}},{}],5:[function(t,e,r){e.exports=function(t){return t&&"object"==typeof t&&"function"==typeof t.copy&&"function"==typeof t.fill&&"function"==typeof t.readUInt8}},{}],6:[function(c,t,I){(function(n,i){var s=/%[sdj%]/g;I.format=function(t){if(!v(t)){for(var e=[],r=0;r<arguments.length;r++)e.push(u(arguments[r]));return e.join(" ")}r=1;for(var n=arguments,i=n.length,o=String(t).replace(s,function(t){if("%%"===t)return"%";if(i<=r)return t;switch(t){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(t){return"[Circular]"}default:return t}}),a=n[r];r<i;a=n[++r])_(a)||!l(a)?o+=" "+a:o+=" "+u(a);return o},I.deprecate=function(t,e){if(w(i.process))return function(){return I.deprecate(t,e).apply(this,arguments)};if(!0===n.noDeprecation)return t;var r=!1;return function(){if(!r){if(n.throwDeprecation)throw new Error(e);n.traceDeprecation?console.trace(e):console.error(e),r=!0}return t.apply(this,arguments)}};var t,o={};function u(t,e){var r={seen:[],stylize:f};return 3<=arguments.length&&(r.depth=arguments[2]),4<=arguments.length&&(r.colors=arguments[3]),g(e)?r.showHidden=e:e&&I._extend(r,e),w(r.showHidden)&&(r.showHidden=!1),w(r.depth)&&(r.depth=2),w(r.colors)&&(r.colors=!1),w(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=a),d(r,t,r.depth)}function a(t,e){var r=u.styles[e];return r?"["+u.colors[r][0]+"m"+t+"["+u.colors[r][1]+"m":t}function f(t,e){return t}function d(e,r,n){if(e.customInspect&&r&&k(r.inspect)&&r.inspect!==I.inspect&&(!r.constructor||r.constructor.prototype!==r)){var t=r.inspect(n,e);return v(t)||(t=d(e,t,n)),t}var i=function(t,e){if(w(e))return t.stylize("undefined","undefined");if(v(e)){var r="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(r,"string")}if(y(e))return t.stylize(""+e,"number");if(g(e))return t.stylize(""+e,"boolean");if(_(e))return t.stylize("null","null")}(e,r);if(i)return i;var o,a=Object.keys(r),s=(o={},a.forEach(function(t,e){o[t]=!0}),o);if(e.showHidden&&(a=Object.getOwnPropertyNames(r)),j(r)&&(0<=a.indexOf("message")||0<=a.indexOf("description")))return p(r);if(0===a.length){if(k(r)){var u=r.name?": "+r.name:"";return e.stylize("[Function"+u+"]","special")}if(E(r))return e.stylize(RegExp.prototype.toString.call(r),"regexp");if(x(r))return e.stylize(Date.prototype.toString.call(r),"date");if(j(r))return p(r)}var f,l="",h=!1,c=["{","}"];(b(r)&&(h=!0,c=["[","]"]),k(r))&&(l=" [Function"+(r.name?": "+r.name:"")+"]");return E(r)&&(l=" "+RegExp.prototype.toString.call(r)),x(r)&&(l=" "+Date.prototype.toUTCString.call(r)),j(r)&&(l=" "+p(r)),0!==a.length||h&&0!=r.length?n<0?E(r)?e.stylize(RegExp.prototype.toString.call(r),"regexp"):e.stylize("[Object]","special"):(e.seen.push(r),f=h?function(e,r,n,i,t){for(var o=[],a=0,s=r.length;a<s;++a)S(r,String(a))?o.push(m(e,r,n,i,String(a),!0)):o.push("");return t.forEach(function(t){t.match(/^\d+$/)||o.push(m(e,r,n,i,t,!0))}),o}(e,r,n,s,a):a.map(function(t){return m(e,r,n,s,t,h)}),e.seen.pop(),function(t,e,r){if(60<t.reduce(function(t,e){return 0,0<=e.indexOf("\n")&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1},0))return r[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+r[1];return r[0]+e+" "+t.join(", ")+" "+r[1]}(f,l,c)):c[0]+l+c[1]}function p(t){return"["+Error.prototype.toString.call(t)+"]"}function m(t,e,r,n,i,o){var a,s,u;if((u=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]}).get?s=u.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):u.set&&(s=t.stylize("[Setter]","special")),S(n,i)||(a="["+i+"]"),s||(t.seen.indexOf(u.value)<0?-1<(s=_(r)?d(t,u.value,null):d(t,u.value,r-1)).indexOf("\n")&&(s=o?s.split("\n").map(function(t){return"  "+t}).join("\n").substr(2):"\n"+s.split("\n").map(function(t){return"   "+t}).join("\n")):s=t.stylize("[Circular]","special")),w(a)){if(o&&i.match(/^\d+$/))return s;a=(a=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),t.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),t.stylize(a,"string"))}return a+": "+s}function b(t){return Array.isArray(t)}function g(t){return"boolean"==typeof t}function _(t){return null===t}function y(t){return"number"==typeof t}function v(t){return"string"==typeof t}function w(t){return void 0===t}function E(t){return l(t)&&"[object RegExp]"===e(t)}function l(t){return"object"==typeof t&&null!==t}function x(t){return l(t)&&"[object Date]"===e(t)}function j(t){return l(t)&&("[object Error]"===e(t)||t instanceof Error)}function k(t){return"function"==typeof t}function e(t){return Object.prototype.toString.call(t)}function r(t){return t<10?"0"+t.toString(10):t.toString(10)}I.debuglog=function(e){if(w(t)&&(t=n.env.NODE_DEBUG||""),e=e.toUpperCase(),!o[e])if(new RegExp("\\b"+e+"\\b","i").test(t)){var r=n.pid;o[e]=function(){var t=I.format.apply(I,arguments);console.error("%s %d: %s",e,r,t)}}else o[e]=function(){};return o[e]},(I.inspect=u).colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},u.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},I.isArray=b,I.isBoolean=g,I.isNull=_,I.isNullOrUndefined=function(t){return null==t},I.isNumber=y,I.isString=v,I.isSymbol=function(t){return"symbol"==typeof t},I.isUndefined=w,I.isRegExp=E,I.isObject=l,I.isDate=x,I.isError=j,I.isFunction=k,I.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},I.isBuffer=c("./support/isBuffer");var h=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function S(t,e){return Object.prototype.hasOwnProperty.call(t,e)}I.log=function(){var t,e;console.log("%s - %s",(t=new Date,e=[r(t.getHours()),r(t.getMinutes()),r(t.getSeconds())].join(":"),[t.getDate(),h[t.getMonth()],e].join(" ")),I.format.apply(I,arguments))},I.inherits=c("inherits"),I._extend=function(t,e){if(!e||!l(e))return t;for(var r=Object.keys(e),n=r.length;n--;)t[r[n]]=e[r[n]];return t}}).call(this,c("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{"./support/isBuffer":5,_process:223,inherits:4}],7:[function(t,e,r){"use strict";r.byteLength=function(t){var e=d(t),r=e[0],n=e[1];return 3*(r+n)/4-n},r.toByteArray=function(t){for(var e,r=d(t),n=r[0],i=r[1],o=new c((f=n,l=i,3*(f+l)/4-l)),a=0,s=0<i?n-4:n,u=0;u<s;u+=4)e=h[t.charCodeAt(u)]<<18|h[t.charCodeAt(u+1)]<<12|h[t.charCodeAt(u+2)]<<6|h[t.charCodeAt(u+3)],o[a++]=e>>16&255,o[a++]=e>>8&255,o[a++]=255&e;var f,l;2===i&&(e=h[t.charCodeAt(u)]<<2|h[t.charCodeAt(u+1)]>>4,o[a++]=255&e);1===i&&(e=h[t.charCodeAt(u)]<<10|h[t.charCodeAt(u+1)]<<4|h[t.charCodeAt(u+2)]>>2,o[a++]=e>>8&255,o[a++]=255&e);return o},r.fromByteArray=function(t){for(var e,r=t.length,n=r%3,i=[],o=0,a=r-n;o<a;o+=16383)i.push(u(t,o,a<o+16383?a:o+16383));1===n?(e=t[r-1],i.push(s[e>>2]+s[e<<4&63]+"==")):2===n&&(e=(t[r-2]<<8)+t[r-1],i.push(s[e>>10]+s[e>>4&63]+s[e<<2&63]+"="));return i.join("")};for(var s=[],h=[],c="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0,o=n.length;i<o;++i)s[i]=n[i],h[n.charCodeAt(i)]=i;function d(t){var e=t.length;if(0<e%4)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function u(t,e,r){for(var n,i,o=[],a=e;a<r;a+=3)n=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),o.push(s[(i=n)>>18&63]+s[i>>12&63]+s[i>>6&63]+s[63&i]);return o.join("")}h["-".charCodeAt(0)]=62,h["_".charCodeAt(0)]=63},{}],8:[function(t,e,r){var n=t("./lib/encoder"),i=t("./lib/decoder");e.exports={encode:n,decode:i}},{"./lib/decoder":9,"./lib/encoder":10}],9:[function(t,n,e){(function(r){function e(t,e){if(this.pos=0,this.buffer=t,this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=this.buffer.toString("utf-8",0,this.pos+=2),"BM"!=this.flag)throw new Error("Invalid BMP File");this.parseHeader(),this.parseRGBA()}e.prototype.parseHeader=function(){if(this.fileSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.reserved=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.offset=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.headerSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.width=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.height=this.buffer.readInt32LE(this.pos),this.pos+=4,this.planes=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.bitPP=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.compress=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.rawSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.hr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.vr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.colors=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.importantColors=this.buffer.readUInt32LE(this.pos),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var r=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);this.palette[e]={red:i,green:n,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},e.prototype.parseRGBA=function(){var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new r(e),this[t]()},e.prototype.bit1=function(){var t=Math.ceil(this.width/8),e=t%4,r=0<=this.height?this.height-1:-this.height;for(r=this.height-1;0<=r;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<t;i++)for(var o=this.buffer.readUInt8(this.pos++),a=n*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var u=this.palette[o>>7-s&1];this.data[a+4*s]=0,this.data[a+4*s+1]=u.blue,this.data[a+4*s+2]=u.green,this.data[a+4*s+3]=u.red}0!=e&&(this.pos+=4-e)}},e.prototype.bit4=function(){if(2==this.compress){this.data.fill(255);for(var r=0,t=this.bottom_up?this.height-1:0,e=!1;r<this.data.length;){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++);if(0==n){if(0==i){this.bottom_up?t--:t++,r=t*this.width*4,e=!1;continue}if(1==i)break;if(2==i){var o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++);this.bottom_up?t-=a:t+=a,r+=a*this.width*4+4*o}else{for(var s=this.buffer.readUInt8(this.pos++),u=0;u<i;u++)f.call(this,e?15&s:(240&s)>>4),1&u&&u+1<i&&(s=this.buffer.readUInt8(this.pos++)),e=!e;1==(i+1>>1&1)&&this.pos++}}else for(u=0;u<n;u++)f.call(this,e?15&i:(240&i)>>4),e=!e}function f(t){var e=this.palette[t];this.data[r]=0,this.data[r+1]=e.blue,this.data[r+2]=e.green,this.data[r+3]=e.red,r+=4}}else{var l=Math.ceil(this.width/2),h=l%4;for(a=this.height-1;0<=a;a--){var c=this.bottom_up?a:this.height-1-a;for(o=0;o<l;o++){i=this.buffer.readUInt8(this.pos++),r=c*this.width*4+2*o*4;var d=i>>4,p=15&i,m=this.palette[d];if(this.data[r]=0,this.data[r+1]=m.blue,this.data[r+2]=m.green,this.data[r+3]=m.red,2*o+1>=this.width)break;m=this.palette[p],this.data[r+4]=0,this.data[r+4+1]=m.blue,this.data[r+4+2]=m.green,this.data[r+4+3]=m.red}0!=h&&(this.pos+=4-h)}}},e.prototype.bit8=function(){if(1==this.compress){this.data.fill(255);for(var r=0,t=this.bottom_up?this.height-1:0;r<this.data.length;){var e=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++);if(0==e){if(0==n){this.bottom_up?t--:t++,r=t*this.width*4;continue}if(1==n)break;if(2==n){var i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);this.bottom_up?t-=o:t+=o,r+=o*this.width*4+4*i}else{for(var a=0;a<n;a++){var s=this.buffer.readUInt8(this.pos++);u.call(this,s)}!0&n&&this.pos++}}else for(a=0;a<e;a++)u.call(this,n)}function u(t){var e=this.palette[t];this.data[r]=0,this.data[r+1]=e.blue,this.data[r+2]=e.green,this.data[r+3]=e.red,r+=4}}else{var f=this.width%4;for(o=this.height-1;0<=o;o--){var l=this.bottom_up?o:this.height-1-o;for(i=0;i<this.width;i++){n=this.buffer.readUInt8(this.pos++),r=l*this.width*4+4*i;if(n<this.palette.length){var h=this.palette[n];this.data[r]=0,this.data[r+1]=h.blue,this.data[r+2]=h.green,this.data[r+3]=h.red}else this.data[r]=0,this.data[r+1]=255,this.data[r+2]=255,this.data[r+3]=255}0!=f&&(this.pos+=4-f)}}},e.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),r=this.height-1;0<=r;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var o=this.buffer.readUInt16LE(this.pos);this.pos+=2;var a=(o&e)/e*255|0,s=(o>>5&e)/e*255|0,u=(o>>10&e)/e*255|0,f=o>>15?255:0,l=n*this.width*4+4*i;this.data[l]=f,this.data[l+1]=a,this.data[l+2]=s,this.data[l+3]=u}this.pos+=t}},e.prototype.bit16=function(){var t=this.width%2*2;this.maskRed=31744,this.maskGreen=992,this.maskBlue=31,this.mask0=0,3==this.compress&&(this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4);for(var e=[0,0,0],r=0;r<16;r++)this.maskRed>>r&1&&e[0]++,this.maskGreen>>r&1&&e[1]++,this.maskBlue>>r&1&&e[2]++;e[1]+=e[0],e[2]+=e[1],e[0]=8-e[0],e[1]-=8,e[2]-=8;for(var n=this.height-1;0<=n;n--){for(var i=this.bottom_up?n:this.height-1-n,o=0;o<this.width;o++){var a=this.buffer.readUInt16LE(this.pos);this.pos+=2;var s=(a&this.maskBlue)<<e[0],u=(a&this.maskGreen)>>e[1],f=(a&this.maskRed)>>e[2],l=i*this.width*4+4*o;this.data[l]=0,this.data[l+1]=s,this.data[l+2]=u,this.data[l+3]=f}this.pos+=t}},e.prototype.bit24=function(){for(var t=this.height-1;0<=t;t--){for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=e*this.width*4+4*r;this.data[a]=0,this.data[a+1]=n,this.data[a+2]=i,this.data[a+3]=o}this.pos+=this.width%4}},e.prototype.bit32=function(){if(3==this.compress){this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4;for(var t=this.height-1;0<=t;t--)for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=n,this.data[s+1]=i,this.data[s+2]=o,this.data[s+3]=a}}else for(t=this.height-1;0<=t;t--)for(e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){i=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=n,this.data[s+1]=i,this.data[s+2]=o,this.data[s+3]=a}},e.prototype.getData=function(){return this.data},n.exports=function(t){return new e(t)}}).call(this,t("buffer").Buffer)},{buffer:16}],10:[function(t,e,r){(function(s){function r(t){this.buffer=t.data,this.width=t.width,this.height=t.height,this.extraBytes=this.width%4,this.rgbSize=this.height*(3*this.width+this.extraBytes),this.headerInfoSize=40,this.data=[],this.flag="BM",this.reserved=0,this.offset=54,this.fileSize=this.rgbSize+this.offset,this.planes=1,this.bitPP=24,this.compress=0,this.hr=0,this.vr=0,this.colors=0,this.importantColors=0}r.prototype.encode=function(){var t=new s(this.offset+this.rgbSize);this.pos=0,t.write(this.flag,this.pos,2),this.pos+=2,t.writeUInt32LE(this.fileSize,this.pos),this.pos+=4,t.writeUInt32LE(this.reserved,this.pos),this.pos+=4,t.writeUInt32LE(this.offset,this.pos),this.pos+=4,t.writeUInt32LE(this.headerInfoSize,this.pos),this.pos+=4,t.writeUInt32LE(this.width,this.pos),this.pos+=4,t.writeInt32LE(-this.height,this.pos),this.pos+=4,t.writeUInt16LE(this.planes,this.pos),this.pos+=2,t.writeUInt16LE(this.bitPP,this.pos),this.pos+=2,t.writeUInt32LE(this.compress,this.pos),this.pos+=4,t.writeUInt32LE(this.rgbSize,this.pos),this.pos+=4,t.writeUInt32LE(this.hr,this.pos),this.pos+=4,t.writeUInt32LE(this.vr,this.pos),this.pos+=4,t.writeUInt32LE(this.colors,this.pos),this.pos+=4,t.writeUInt32LE(this.importantColors,this.pos),this.pos+=4;for(var e=0,r=3*this.width+this.extraBytes,n=0;n<this.height;n++){for(var i=0;i<this.width;i++){var o=this.pos+n*r+3*i;e++,t[o]=this.buffer[e++],t[o+1]=this.buffer[e++],t[o+2]=this.buffer[e++]}if(0<this.extraBytes){var a=this.pos+n*r+3*this.width;t.fill(0,a,a+this.extraBytes)}}return t},e.exports=function(t,e){return void 0===e&&(e=100),{data:new r(t).encode(),width:t.width,height:t.height}}}).call(this,t("buffer").Buffer)},{buffer:16}],11:[function(t,e,r){},{}],12:[function(n,t,c){(function(f,l){"use strict";var h=n("assert"),o=n("pako/lib/zlib/zstream"),a=n("pako/lib/zlib/deflate.js"),s=n("pako/lib/zlib/inflate.js"),t=n("pako/lib/zlib/constants");for(var e in t)c[e]=t[e];c.NONE=0,c.DEFLATE=1,c.INFLATE=2,c.GZIP=3,c.GUNZIP=4,c.DEFLATERAW=5,c.INFLATERAW=6,c.UNZIP=7;function r(t){if("number"!=typeof t||t<c.DEFLATE||t>c.UNZIP)throw new TypeError("Bad argument");this.dictionary=null,this.err=0,this.flush=0,this.init_done=!1,this.level=0,this.memLevel=0,this.mode=t,this.strategy=0,this.windowBits=0,this.write_in_progress=!1,this.pending_close=!1,this.gzip_id_bytes_read=0}r.prototype.close=function(){this.write_in_progress?this.pending_close=!0:(this.pending_close=!1,h(this.init_done,"close before init"),h(this.mode<=c.UNZIP),this.mode===c.DEFLATE||this.mode===c.GZIP||this.mode===c.DEFLATERAW?a.deflateEnd(this.strm):this.mode!==c.INFLATE&&this.mode!==c.GUNZIP&&this.mode!==c.INFLATERAW&&this.mode!==c.UNZIP||s.inflateEnd(this.strm),this.mode=c.NONE,this.dictionary=null)},r.prototype.write=function(t,e,r,n,i,o,a){return this._write(!0,t,e,r,n,i,o,a)},r.prototype.writeSync=function(t,e,r,n,i,o,a){return this._write(!1,t,e,r,n,i,o,a)},r.prototype._write=function(t,e,r,n,i,o,a,s){if(h.equal(arguments.length,8),h(this.init_done,"write before init"),h(this.mode!==c.NONE,"already finalized"),h.equal(!1,this.write_in_progress,"write already in progress"),h.equal(!1,this.pending_close,"close is pending"),this.write_in_progress=!0,h.equal(!1,void 0===e,"must provide flush value"),this.write_in_progress=!0,e!==c.Z_NO_FLUSH&&e!==c.Z_PARTIAL_FLUSH&&e!==c.Z_SYNC_FLUSH&&e!==c.Z_FULL_FLUSH&&e!==c.Z_FINISH&&e!==c.Z_BLOCK)throw new Error("Invalid flush value");if(null==r&&(r=l.alloc(0),n=i=0),this.strm.avail_in=i,this.strm.input=r,this.strm.next_in=n,this.strm.avail_out=s,this.strm.output=o,this.strm.next_out=a,this.flush=e,!t)return this._process(),this._checkError()?this._afterSync():void 0;var u=this;return f.nextTick(function(){u._process(),u._after()}),this},r.prototype._afterSync=function(){var t=this.strm.avail_out,e=this.strm.avail_in;return this.write_in_progress=!1,[e,t]},r.prototype._process=function(){var t=null;switch(this.mode){case c.DEFLATE:case c.GZIP:case c.DEFLATERAW:this.err=a.deflate(this.strm,this.flush);break;case c.UNZIP:switch(0<this.strm.avail_in&&(t=this.strm.next_in),this.gzip_id_bytes_read){case 0:if(null===t)break;if(31!==this.strm.input[t]){this.mode=c.INFLATE;break}if(t++,(this.gzip_id_bytes_read=1)===this.strm.avail_in)break;case 1:if(null===t)break;139===this.strm.input[t]?(this.gzip_id_bytes_read=2,this.mode=c.GUNZIP):this.mode=c.INFLATE;break;default:throw new Error("invalid number of gzip magic number bytes read")}case c.INFLATE:case c.GUNZIP:case c.INFLATERAW:for(this.err=s.inflate(this.strm,this.flush),this.err===c.Z_NEED_DICT&&this.dictionary&&(this.err=s.inflateSetDictionary(this.strm,this.dictionary),this.err===c.Z_OK?this.err=s.inflate(this.strm,this.flush):this.err===c.Z_DATA_ERROR&&(this.err=c.Z_NEED_DICT));0<this.strm.avail_in&&this.mode===c.GUNZIP&&this.err===c.Z_STREAM_END&&0!==this.strm.next_in[0];)this.reset(),this.err=s.inflate(this.strm,this.flush);break;default:throw new Error("Unknown mode "+this.mode)}},r.prototype._checkError=function(){switch(this.err){case c.Z_OK:case c.Z_BUF_ERROR:if(0!==this.strm.avail_out&&this.flush===c.Z_FINISH)return this._error("unexpected end of file"),!1;break;case c.Z_STREAM_END:break;case c.Z_NEED_DICT:return null==this.dictionary?this._error("Missing dictionary"):this._error("Bad dictionary"),!1;default:return this._error("Zlib error"),!1}return!0},r.prototype._after=function(){if(this._checkError()){var t=this.strm.avail_out,e=this.strm.avail_in;this.write_in_progress=!1,this.callback(e,t),this.pending_close&&this.close()}},r.prototype._error=function(t){this.strm.msg&&(t=this.strm.msg),this.onerror(t,this.err),this.write_in_progress=!1,this.pending_close&&this.close()},r.prototype.init=function(t,e,r,n,i){h(4===arguments.length||5===arguments.length,"init(windowBits, level, memLevel, strategy, [dictionary])"),h(8<=t&&t<=15,"invalid windowBits"),h(-1<=e&&e<=9,"invalid compression level"),h(1<=r&&r<=9,"invalid memlevel"),h(n===c.Z_FILTERED||n===c.Z_HUFFMAN_ONLY||n===c.Z_RLE||n===c.Z_FIXED||n===c.Z_DEFAULT_STRATEGY,"invalid strategy"),this._init(e,t,r,n,i),this._setDictionary()},r.prototype.params=function(){throw new Error("deflateParams Not supported")},r.prototype.reset=function(){this._reset(),this._setDictionary()},r.prototype._init=function(t,e,r,n,i){switch(this.level=t,this.windowBits=e,this.memLevel=r,this.strategy=n,this.flush=c.Z_NO_FLUSH,this.err=c.Z_OK,this.mode!==c.GZIP&&this.mode!==c.GUNZIP||(this.windowBits+=16),this.mode===c.UNZIP&&(this.windowBits+=32),this.mode!==c.DEFLATERAW&&this.mode!==c.INFLATERAW||(this.windowBits=-1*this.windowBits),this.strm=new o,this.mode){case c.DEFLATE:case c.GZIP:case c.DEFLATERAW:this.err=a.deflateInit2(this.strm,this.level,c.Z_DEFLATED,this.windowBits,this.memLevel,this.strategy);break;case c.INFLATE:case c.GUNZIP:case c.INFLATERAW:case c.UNZIP:this.err=s.inflateInit2(this.strm,this.windowBits);break;default:throw new Error("Unknown mode "+this.mode)}this.err!==c.Z_OK&&this._error("Init error"),this.dictionary=i,this.write_in_progress=!1,this.init_done=!0},r.prototype._setDictionary=function(){if(null!=this.dictionary){switch(this.err=c.Z_OK,this.mode){case c.DEFLATE:case c.DEFLATERAW:this.err=a.deflateSetDictionary(this.strm,this.dictionary)}this.err!==c.Z_OK&&this._error("Failed to set dictionary")}},r.prototype._reset=function(){switch(this.err=c.Z_OK,this.mode){case c.DEFLATE:case c.DEFLATERAW:case c.GZIP:this.err=a.deflateReset(this.strm);break;case c.INFLATE:case c.INFLATERAW:case c.GUNZIP:this.err=s.inflateReset(this.strm)}this.err!==c.Z_OK&&this._error("Failed to reset stream")},c.Zlib=r}).call(this,n("_process"),n("buffer").Buffer)},{_process:223,assert:3,buffer:16,"pako/lib/zlib/constants":182,"pako/lib/zlib/deflate.js":184,"pako/lib/zlib/inflate.js":187,"pako/lib/zlib/zstream":191}],13:[function(A,t,T){(function(i){"use strict";var b=A("buffer").Buffer,a=A("stream").Transform,s=A("./binding"),t=A("util"),g=A("assert").ok,_=A("buffer").kMaxLength,y="Cannot create final Buffer. It would be larger than 0x"+_.toString(16)+" bytes";s.Z_MIN_WINDOWBITS=8,s.Z_MAX_WINDOWBITS=15,s.Z_DEFAULT_WINDOWBITS=15,s.Z_MIN_CHUNK=64,s.Z_MAX_CHUNK=1/0,s.Z_DEFAULT_CHUNK=16384,s.Z_MIN_MEMLEVEL=1,s.Z_MAX_MEMLEVEL=9,s.Z_DEFAULT_MEMLEVEL=8,s.Z_MIN_LEVEL=-1,s.Z_MAX_LEVEL=9,s.Z_DEFAULT_LEVEL=s.Z_DEFAULT_COMPRESSION;for(var e=Object.keys(s),r=0;r<e.length;r++){var n=e[r];n.match(/^Z/)&&Object.defineProperty(T,n,{enumerable:!0,value:s[n],writable:!1})}for(var o={Z_OK:s.Z_OK,Z_STREAM_END:s.Z_STREAM_END,Z_NEED_DICT:s.Z_NEED_DICT,Z_ERRNO:s.Z_ERRNO,Z_STREAM_ERROR:s.Z_STREAM_ERROR,Z_DATA_ERROR:s.Z_DATA_ERROR,Z_MEM_ERROR:s.Z_MEM_ERROR,Z_BUF_ERROR:s.Z_BUF_ERROR,Z_VERSION_ERROR:s.Z_VERSION_ERROR},u=Object.keys(o),f=0;f<u.length;f++){var l=u[f];o[o[l]]=l}function h(r,t,n){var i=[],o=0;function e(){for(var t;null!==(t=r.read());)i.push(t),o+=t.length;r.once("readable",e)}function a(){var t,e=null;_<=o?e=new RangeError(y):t=b.concat(i,o),i=[],r.close(),n(e,t)}r.on("error",function(t){r.removeListener("end",a),r.removeListener("readable",e),n(t)}),r.on("end",a),r.end(t),e()}function c(t,e){if("string"==typeof e&&(e=b.from(e)),!b.isBuffer(e))throw new TypeError("Not a string or buffer");var r=t._finishFlushFlag;return t._processChunk(e,r)}function d(t){if(!(this instanceof d))return new d(t);k.call(this,t,s.DEFLATE)}function p(t){if(!(this instanceof p))return new p(t);k.call(this,t,s.INFLATE)}function m(t){if(!(this instanceof m))return new m(t);k.call(this,t,s.GZIP)}function v(t){if(!(this instanceof v))return new v(t);k.call(this,t,s.GUNZIP)}function w(t){if(!(this instanceof w))return new w(t);k.call(this,t,s.DEFLATERAW)}function E(t){if(!(this instanceof E))return new E(t);k.call(this,t,s.INFLATERAW)}function x(t){if(!(this instanceof x))return new x(t);k.call(this,t,s.UNZIP)}function j(t){return t===s.Z_NO_FLUSH||t===s.Z_PARTIAL_FLUSH||t===s.Z_SYNC_FLUSH||t===s.Z_FULL_FLUSH||t===s.Z_FINISH||t===s.Z_BLOCK}function k(t,e){var r=this;if(this._opts=t=t||{},this._chunkSize=t.chunkSize||T.Z_DEFAULT_CHUNK,a.call(this,t),t.flush&&!j(t.flush))throw new Error("Invalid flush flag: "+t.flush);if(t.finishFlush&&!j(t.finishFlush))throw new Error("Invalid flush flag: "+t.finishFlush);if(this._flushFlag=t.flush||s.Z_NO_FLUSH,this._finishFlushFlag=void 0!==t.finishFlush?t.finishFlush:s.Z_FINISH,t.chunkSize&&(t.chunkSize<T.Z_MIN_CHUNK||t.chunkSize>T.Z_MAX_CHUNK))throw new Error("Invalid chunk size: "+t.chunkSize);if(t.windowBits&&(t.windowBits<T.Z_MIN_WINDOWBITS||t.windowBits>T.Z_MAX_WINDOWBITS))throw new Error("Invalid windowBits: "+t.windowBits);if(t.level&&(t.level<T.Z_MIN_LEVEL||t.level>T.Z_MAX_LEVEL))throw new Error("Invalid compression level: "+t.level);if(t.memLevel&&(t.memLevel<T.Z_MIN_MEMLEVEL||t.memLevel>T.Z_MAX_MEMLEVEL))throw new Error("Invalid memLevel: "+t.memLevel);if(t.strategy&&t.strategy!=T.Z_FILTERED&&t.strategy!=T.Z_HUFFMAN_ONLY&&t.strategy!=T.Z_RLE&&t.strategy!=T.Z_FIXED&&t.strategy!=T.Z_DEFAULT_STRATEGY)throw new Error("Invalid strategy: "+t.strategy);if(t.dictionary&&!b.isBuffer(t.dictionary))throw new Error("Invalid dictionary: it should be a Buffer instance");this._handle=new s.Zlib(e);var n=this;this._hadError=!1,this._handle.onerror=function(t,e){S(n),n._hadError=!0;var r=new Error(t);r.errno=e,r.code=T.codes[e],n.emit("error",r)};var i=T.Z_DEFAULT_COMPRESSION;"number"==typeof t.level&&(i=t.level);var o=T.Z_DEFAULT_STRATEGY;"number"==typeof t.strategy&&(o=t.strategy),this._handle.init(t.windowBits||T.Z_DEFAULT_WINDOWBITS,i,t.memLevel||T.Z_DEFAULT_MEMLEVEL,o,t.dictionary),this._buffer=b.allocUnsafe(this._chunkSize),this._offset=0,this._level=i,this._strategy=o,this.once("end",this.close),Object.defineProperty(this,"_closed",{get:function(){return!r._handle},configurable:!0,enumerable:!0})}function S(t,e){e&&i.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function I(t){t.emit("close")}Object.defineProperty(T,"codes",{enumerable:!0,value:Object.freeze(o),writable:!1}),T.Deflate=d,T.Inflate=p,T.Gzip=m,T.Gunzip=v,T.DeflateRaw=w,T.InflateRaw=E,T.Unzip=x,T.createDeflate=function(t){return new d(t)},T.createInflate=function(t){return new p(t)},T.createDeflateRaw=function(t){return new w(t)},T.createInflateRaw=function(t){return new E(t)},T.createGzip=function(t){return new m(t)},T.createGunzip=function(t){return new v(t)},T.createUnzip=function(t){return new x(t)},T.deflate=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new d(e),t,r)},T.deflateSync=function(t,e){return c(new d(e),t)},T.gzip=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new m(e),t,r)},T.gzipSync=function(t,e){return c(new m(e),t)},T.deflateRaw=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new w(e),t,r)},T.deflateRawSync=function(t,e){return c(new w(e),t)},T.unzip=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new x(e),t,r)},T.unzipSync=function(t,e){return c(new x(e),t)},T.inflate=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new p(e),t,r)},T.inflateSync=function(t,e){return c(new p(e),t)},T.gunzip=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new v(e),t,r)},T.gunzipSync=function(t,e){return c(new v(e),t)},T.inflateRaw=function(t,e,r){return"function"==typeof e&&(r=e,e={}),h(new E(e),t,r)},T.inflateRawSync=function(t,e){return c(new E(e),t)},t.inherits(k,a),k.prototype.params=function(t,e,r){if(t<T.Z_MIN_LEVEL||t>T.Z_MAX_LEVEL)throw new RangeError("Invalid compression level: "+t);if(e!=T.Z_FILTERED&&e!=T.Z_HUFFMAN_ONLY&&e!=T.Z_RLE&&e!=T.Z_FIXED&&e!=T.Z_DEFAULT_STRATEGY)throw new TypeError("Invalid strategy: "+e);if(this._level!==t||this._strategy!==e){var n=this;this.flush(s.Z_SYNC_FLUSH,function(){g(n._handle,"zlib binding closed"),n._handle.params(t,e),n._hadError||(n._level=t,n._strategy=e,r&&r())})}else i.nextTick(r)},k.prototype.reset=function(){return g(this._handle,"zlib binding closed"),this._handle.reset()},k.prototype._flush=function(t){this._transform(b.alloc(0),"",t)},k.prototype.flush=function(t,e){var r=this,n=this._writableState;("function"==typeof t||void 0===t&&!e)&&(e=t,t=s.Z_FULL_FLUSH),n.ended?e&&i.nextTick(e):n.ending?e&&this.once("end",e):n.needDrain?e&&this.once("drain",function(){return r.flush(t,e)}):(this._flushFlag=t,this.write(b.alloc(0),"",e))},k.prototype.close=function(t){S(this,t),i.nextTick(I,this)},k.prototype._transform=function(t,e,r){var n,i=this._writableState,o=(i.ending||i.ended)&&(!t||i.length===t.length);return null===t||b.isBuffer(t)?this._handle?(o?n=this._finishFlushFlag:(n=this._flushFlag,t.length>=i.length&&(this._flushFlag=this._opts.flush||s.Z_NO_FLUSH)),void this._processChunk(t,n,r)):r(new Error("zlib binding closed")):r(new Error("invalid input"))},k.prototype._processChunk=function(o,a,s){var u=o&&o.length,f=this._chunkSize-this._offset,l=0,h=this,c="function"==typeof s;if(!c){var e,d=[],p=0;this.on("error",function(t){e=t}),g(this._handle,"zlib binding closed");do{var t=this._handle.writeSync(a,o,l,u,this._buffer,this._offset,f)}while(!this._hadError&&m(t[0],t[1]));if(this._hadError)throw e;if(_<=p)throw S(this),new RangeError(y);var r=b.concat(d,p);return S(this),r}g(this._handle,"zlib binding closed");var n=this._handle.write(a,o,l,u,this._buffer,this._offset,f);function m(t,e){if(this&&(this.buffer=null,this.callback=null),!h._hadError){var r=f-e;if(g(0<=r,"have should not go down"),0<r){var n=h._buffer.slice(h._offset,h._offset+r);h._offset+=r,c?h.push(n):(d.push(n),p+=n.length)}if((0===e||h._offset>=h._chunkSize)&&(f=h._chunkSize,h._offset=0,h._buffer=b.allocUnsafe(h._chunkSize)),0===e){if(l+=u-t,u=t,!c)return!0;var i=h._handle.write(a,o,l,u,h._buffer,h._offset,h._chunkSize);return i.callback=m,void(i.buffer=o)}if(!c)return!1;s()}}n.buffer=o,n.callback=m},t.inherits(d,k),t.inherits(p,k),t.inherits(m,k),t.inherits(v,k),t.inherits(w,k),t.inherits(E,k),t.inherits(x,k)}).call(this,A("_process"))},{"./binding":12,_process:223,assert:3,buffer:16,stream:242,util:258}],14:[function(t,e,r){arguments[4][11][0].apply(r,arguments)},{dup:11}],15:[function(t,e,r){var n=t("buffer").Buffer;e.exports=function(t,e){if(n.isBuffer(t)&&n.isBuffer(e)){if("function"==typeof t.equals)return t.equals(e);if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0}}},{buffer:16}],16:[function(t,e,r){"use strict";var n=t("base64-js"),o=t("ieee754");r.Buffer=h,r.SlowBuffer=function(t){+t!=t&&(t=0);return h.alloc(+t)},r.INSPECT_MAX_BYTES=50;var i=2147483647;function a(t){if(i<t)throw new RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return e.__proto__=h.prototype,e}function h(t,e,r){if("number"!=typeof t)return s(t,e,r);if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return f(t)}function s(t,e,r){if("string"==typeof t)return function(t,e){"string"==typeof e&&""!==e||(e="utf8");if(!h.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var r=0|d(t,e),n=a(r),i=n.write(t,e);i!==r&&(n=n.slice(0,i));return n}(t,e);if(ArrayBuffer.isView(t))return l(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(B(t,ArrayBuffer)||t&&B(t.buffer,ArrayBuffer))return function(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r);return n.__proto__=h.prototype,n}(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return h.from(n,e,r);var i=function(t){if(h.isBuffer(t)){var e=0|c(t.length),r=a(e);return 0===r.length||t.copy(r,0,0,e),r}if(void 0!==t.length)return"number"!=typeof t.length||N(t.length)?a(0):l(t);if("Buffer"===t.type&&Array.isArray(t.data))return l(t.data)}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return h.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function f(t){return u(t),a(t<0?0:0|c(t))}function l(t){for(var e=t.length<0?0:0|c(t.length),r=a(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function c(t){if(i<=t)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|t}function d(t,e){if(h.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||B(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=2<arguments.length&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return L(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return C(t).length;default:if(i)return n?-1:L(t).length;e=(""+e).toLowerCase(),i=!0}}function p(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function m(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):2147483647<r?r=2147483647:r<-2147483648&&(r=-2147483648),N(r=+r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=h.from(e,n)),h.isBuffer(e))return 0===e.length?-1:b(t,e,r,n,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):b(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function b(t,e,r,n,i){var o,a=1,s=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s/=a=2,u/=2,r/=2}function f(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var l=-1;for(o=r;o<s;o++)if(f(t,o)===f(e,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*a}else-1!==l&&(o-=o-l),l=-1}else for(s<r+u&&(r=s-u),o=r;0<=o;o--){for(var h=!0,c=0;c<u;c++)if(f(t,o+c)!==f(e,c)){h=!1;break}if(h)return o}return-1}function g(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?i<(n=Number(n))&&(n=i):n=i;var o=e.length;o/2<n&&(n=o/2);for(var a=0;a<n;++a){var s=parseInt(e.substr(2*a,2),16);if(N(s))return a;t[r+a]=s}return a}function _(t,e,r,n){return R(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function y(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function v(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,a,s,u,f=t[i],l=null,h=239<f?4:223<f?3:191<f?2:1;if(i+h<=r)switch(h){case 1:f<128&&(l=f);break;case 2:128==(192&(o=t[i+1]))&&127<(u=(31&f)<<6|63&o)&&(l=u);break;case 3:o=t[i+1],a=t[i+2],128==(192&o)&&128==(192&a)&&2047<(u=(15&f)<<12|(63&o)<<6|63&a)&&(u<55296||57343<u)&&(l=u);break;case 4:o=t[i+1],a=t[i+2],s=t[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&65535<(u=(15&f)<<18|(63&o)<<12|(63&a)<<6|63&s)&&u<1114112&&(l=u)}null===l?(l=65533,h=1):65535<l&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=h}return function(t){var e=t.length;if(e<=w)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=w));return r}(n)}r.kMaxLength=i,(h.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(t){return!1}}())||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(h.prototype,"parent",{enumerable:!0,get:function(){if(h.isBuffer(this))return this.buffer}}),Object.defineProperty(h.prototype,"offset",{enumerable:!0,get:function(){if(h.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&h[Symbol.species]===h&&Object.defineProperty(h,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),h.poolSize=8192,h.from=function(t,e,r){return s(t,e,r)},h.prototype.__proto__=Uint8Array.prototype,h.__proto__=Uint8Array,h.alloc=function(t,e,r){return i=e,o=r,u(n=t),n<=0?a(n):void 0!==i?"string"==typeof o?a(n).fill(i,o):a(n).fill(i):a(n);var n,i,o},h.allocUnsafe=function(t){return f(t)},h.allocUnsafeSlow=function(t){return f(t)},h.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==h.prototype},h.compare=function(t,e){if(B(t,Uint8Array)&&(t=h.from(t,t.offset,t.byteLength)),B(e,Uint8Array)&&(e=h.from(e,e.offset,e.byteLength)),!h.isBuffer(t)||!h.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},h.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},h.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return h.alloc(0);var r;if(void 0===e)for(r=e=0;r<t.length;++r)e+=t[r].length;var n=h.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(B(o,Uint8Array)&&(o=h.from(o)),!h.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},h.byteLength=d,h.prototype._isBuffer=!0,h.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)p(this,e,e+1);return this},h.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)p(this,e,e+3),p(this,e+1,e+2);return this},h.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)p(this,e,e+7),p(this,e+1,e+6),p(this,e+2,e+5),p(this,e+3,e+4);return this},h.prototype.toLocaleString=h.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?v(this,0,t):function(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return j(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return E(this,e,r);case"latin1":case"binary":return x(this,e,r);case"base64":return y(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}.apply(this,arguments)},h.prototype.equals=function(t){if(!h.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===h.compare(this,t)},h.prototype.inspect=function(){var t="",e=r.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},h.prototype.compare=function(t,e,r,n,i){if(B(t,Uint8Array)&&(t=h.from(t,t.offset,t.byteLength)),!h.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(i<=n&&r<=e)return 0;if(i<=n)return-1;if(r<=e)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(e>>>=0),s=Math.min(o,a),u=this.slice(n,i),f=t.slice(e,r),l=0;l<s;++l)if(u[l]!==f[l]){o=u[l],a=f[l];break}return o<a?-1:a<o?1:0},h.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},h.prototype.indexOf=function(t,e,r){return m(this,t,e,r,!0)},h.prototype.lastIndexOf=function(t,e,r){return m(this,t,e,r,!1)},h.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||i<r)&&(r=i),0<t.length&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o,a,s,u,f,l,h,c,d,p=!1;;)switch(n){case"hex":return g(this,t,e,r);case"utf8":case"utf-8":return c=e,d=r,R(L(t,(h=this).length-c),h,c,d);case"ascii":return _(this,t,e,r);case"latin1":case"binary":return _(this,t,e,r);case"base64":return u=this,f=e,l=r,R(C(t),u,f,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return a=e,s=r,R(function(t,e){for(var r,n,i,o=[],a=0;a<t.length&&!((e-=2)<0);++a)r=t.charCodeAt(a),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,(o=this).length-a),o,a,s);default:if(p)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},h.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var w=4096;function E(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function x(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function j(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||n<r)&&(r=n);for(var i="",o=e;o<r;++o)i+=M(t[o]);return i}function k(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function S(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(r<t+e)throw new RangeError("Trying to access beyond buffer length")}function I(t,e,r,n,i,o){if(!h.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(i<e||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function A(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function T(t,e,r,n,i){return e=+e,r>>>=0,i||A(t,0,r,4),o.write(t,e,r,n,23,4),r+4}function O(t,e,r,n,i){return e=+e,r>>>=0,i||A(t,0,r,8),o.write(t,e,r,n,52,8),r+8}h.prototype.slice=function(t,e){var r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):r<t&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):r<e&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return n.__proto__=h.prototype,n},h.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||S(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},h.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||S(t,e,this.length);for(var n=this[t+--e],i=1;0<e&&(i*=256);)n+=this[t+--e]*i;return n},h.prototype.readUInt8=function(t,e){return t>>>=0,e||S(t,1,this.length),this[t]},h.prototype.readUInt16LE=function(t,e){return t>>>=0,e||S(t,2,this.length),this[t]|this[t+1]<<8},h.prototype.readUInt16BE=function(t,e){return t>>>=0,e||S(t,2,this.length),this[t]<<8|this[t+1]},h.prototype.readUInt32LE=function(t,e){return t>>>=0,e||S(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},h.prototype.readUInt32BE=function(t,e){return t>>>=0,e||S(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},h.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||S(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return(i*=128)<=n&&(n-=Math.pow(2,8*e)),n},h.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||S(t,e,this.length);for(var n=e,i=1,o=this[t+--n];0<n&&(i*=256);)o+=this[t+--n]*i;return(i*=128)<=o&&(o-=Math.pow(2,8*e)),o},h.prototype.readInt8=function(t,e){return t>>>=0,e||S(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},h.prototype.readInt16LE=function(t,e){t>>>=0,e||S(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt16BE=function(t,e){t>>>=0,e||S(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt32LE=function(t,e){return t>>>=0,e||S(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},h.prototype.readInt32BE=function(t,e){return t>>>=0,e||S(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},h.prototype.readFloatLE=function(t,e){return t>>>=0,e||S(t,4,this.length),o.read(this,t,!0,23,4)},h.prototype.readFloatBE=function(t,e){return t>>>=0,e||S(t,4,this.length),o.read(this,t,!1,23,4)},h.prototype.readDoubleLE=function(t,e){return t>>>=0,e||S(t,8,this.length),o.read(this,t,!0,52,8)},h.prototype.readDoubleBE=function(t,e){return t>>>=0,e||S(t,8,this.length),o.read(this,t,!1,52,8)},h.prototype.writeUIntLE=function(t,e,r,n){(t=+t,e>>>=0,r>>>=0,n)||I(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},h.prototype.writeUIntBE=function(t,e,r,n){(t=+t,e>>>=0,r>>>=0,n)||I(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;0<=--i&&(o*=256);)this[e+i]=t/o&255;return e+r},h.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,1,255,0),this[e]=255&t,e+1},h.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},h.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},h.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},h.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},h.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);I(this,t,e,r,i-1,-i)}var o=0,a=1,s=0;for(this[e]=255&t;++o<r&&(a*=256);)t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+r},h.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);I(this,t,e,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[e+o]=255&t;0<=--o&&(a*=256);)t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+r},h.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},h.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},h.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},h.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},h.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||I(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},h.prototype.writeFloatLE=function(t,e,r){return T(this,t,e,!0,r)},h.prototype.writeFloatBE=function(t,e,r){return T(this,t,e,!1,r)},h.prototype.writeDoubleLE=function(t,e,r){return O(this,t,e,!0,r)},h.prototype.writeDoubleBE=function(t,e,r){return O(this,t,e,!1,r)},h.prototype.copy=function(t,e,r,n){if(!h.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),0<n&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;0<=o;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},h.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!h.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var i=t.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(t=i)}}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var a=h.isBuffer(t)?t:h.from(t,n),s=a.length;if(0===s)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-e;++o)this[o+e]=a[o%s]}return this};var P=/[^+/0-9A-Za-z-_]/g;function M(t){return t<16?"0"+t.toString(16):t.toString(16)}function L(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],a=0;a<n;++a){if(55295<(r=t.charCodeAt(a))&&r<57344){if(!i){if(56319<r){-1<(e-=3)&&o.push(239,191,189);continue}if(a+1===n){-1<(e-=3)&&o.push(239,191,189);continue}i=r;continue}if(r<56320){-1<(e-=3)&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&-1<(e-=3)&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function C(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(P,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function R(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function B(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function N(t){return t!=t}},{"base64-js":7,ieee754:165}],17:[function(t,e,r){e.exports={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Unordered Collection",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}},{}],18:[function(t,e,r){e.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},{}],19:[function(t,e,r){var n=t("./_wks")("unscopables"),i=Array.prototype;null==i[n]&&t("./_hide")(i,n,{}),e.exports=function(t){i[n][t]=!0}},{"./_hide":47,"./_wks":112}],20:[function(t,e,r){e.exports=function(t,e,r,n){if(!(t instanceof e)||void 0!==n&&n in t)throw TypeError(r+": incorrect invocation!");return t}},{}],21:[function(t,e,r){var n=t("./_is-object");e.exports=function(t){if(!n(t))throw TypeError(t+" is not an object!");return t}},{"./_is-object":55}],22:[function(t,e,r){"use strict";var f=t("./_to-object"),l=t("./_to-absolute-index"),h=t("./_to-length");e.exports=[].copyWithin||function(t,e){var r=f(this),n=h(r.length),i=l(t,n),o=l(e,n),a=2<arguments.length?arguments[2]:void 0,s=Math.min((void 0===a?n:l(a,n))-o,n-i),u=1;for(o<i&&i<o+s&&(u=-1,o+=s-1,i+=s-1);0<s--;)o in r?r[i]=r[o]:delete r[i],i+=u,o+=u;return r}},{"./_to-absolute-index":98,"./_to-length":102,"./_to-object":103}],23:[function(t,e,r){"use strict";var s=t("./_to-object"),u=t("./_to-absolute-index"),f=t("./_to-length");e.exports=function(t){for(var e=s(this),r=f(e.length),n=arguments.length,i=u(1<n?arguments[1]:void 0,r),o=2<n?arguments[2]:void 0,a=void 0===o?r:u(o,r);i<a;)e[i++]=t;return e}},{"./_to-absolute-index":98,"./_to-length":102,"./_to-object":103}],24:[function(t,e,r){var u=t("./_to-iobject"),f=t("./_to-length"),l=t("./_to-absolute-index");e.exports=function(s){return function(t,e,r){var n,i=u(t),o=f(i.length),a=l(r,o);if(s&&e!=e){for(;a<o;)if((n=i[a++])!=n)return!0}else for(;a<o;a++)if((s||a in i)&&i[a]===e)return s||a||0;return!s&&-1}}},{"./_to-absolute-index":98,"./_to-iobject":101,"./_to-length":102}],25:[function(t,e,r){var y=t("./_ctx"),v=t("./_iobject"),w=t("./_to-object"),E=t("./_to-length"),n=t("./_array-species-create");e.exports=function(h,t){var c=1==h,d=2==h,p=3==h,m=4==h,b=6==h,g=5==h||b,_=t||n;return function(t,e,r){for(var n,i,o=w(t),a=v(o),s=y(e,r,3),u=E(a.length),f=0,l=c?_(t,u):d?_(t,0):void 0;f<u;f++)if((g||f in a)&&(i=s(n=a[f],f,o),h))if(c)l[f]=i;else if(i)switch(h){case 3:return!0;case 5:return n;case 6:return f;case 2:l.push(n)}else if(m)return!1;return b?-1:p||m?m:l}}},{"./_array-species-create":27,"./_ctx":33,"./_iobject":52,"./_to-length":102,"./_to-object":103}],26:[function(t,e,r){var n=t("./_is-object"),i=t("./_is-array"),o=t("./_wks")("species");e.exports=function(t){var e;return i(t)&&("function"!=typeof(e=t.constructor)||e!==Array&&!i(e.prototype)||(e=void 0),n(e)&&null===(e=e[o])&&(e=void 0)),void 0===e?Array:e}},{"./_is-array":54,"./_is-object":55,"./_wks":112}],27:[function(t,e,r){var n=t("./_array-species-constructor");e.exports=function(t,e){return new(n(t))(e)}},{"./_array-species-constructor":26}],28:[function(t,e,r){"use strict";var o=t("./_a-function"),a=t("./_is-object"),s=t("./_invoke"),u=[].slice,f={};e.exports=Function.bind||function(e){var r=o(this),n=u.call(arguments,1),i=function(){var t=n.concat(u.call(arguments));return this instanceof i?function(t,e,r){if(!(e in f)){for(var n=[],i=0;i<e;i++)n[i]="a["+i+"]";f[e]=Function("F,a","return new F("+n.join(",")+")")}return f[e](t,r)}(r,t.length,t):s(r,t,e)};return a(r.prototype)&&(i.prototype=r.prototype),i}},{"./_a-function":18,"./_invoke":51,"./_is-object":55}],29:[function(t,e,r){var i=t("./_cof"),o=t("./_wks")("toStringTag"),a="Arguments"==i(function(){return arguments}());e.exports=function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),o))?r:a?i(e):"Object"==(n=i(e))&&"function"==typeof e.callee?"Arguments":n}},{"./_cof":30,"./_wks":112}],30:[function(t,e,r){var n={}.toString;e.exports=function(t){return n.call(t).slice(8,-1)}},{}],31:[function(t,e,r){var n=e.exports={version:"2.5.7"};"number"==typeof __e&&(__e=n)},{}],32:[function(t,e,r){"use strict";var n=t("./_object-dp"),i=t("./_property-desc");e.exports=function(t,e,r){e in t?n.f(t,e,i(0,r)):t[e]=r}},{"./_object-dp":69,"./_property-desc":83}],33:[function(t,e,r){var o=t("./_a-function");e.exports=function(n,i,t){if(o(n),void 0===i)return n;switch(t){case 1:return function(t){return n.call(i,t)};case 2:return function(t,e){return n.call(i,t,e)};case 3:return function(t,e,r){return n.call(i,t,e,r)}}return function(){return n.apply(i,arguments)}}},{"./_a-function":18}],34:[function(t,e,r){e.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},{}],35:[function(t,e,r){e.exports=!t("./_fails")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},{"./_fails":41}],36:[function(t,e,r){var n=t("./_is-object"),i=t("./_global").document,o=n(i)&&n(i.createElement);e.exports=function(t){return o?i.createElement(t):{}}},{"./_global":45,"./_is-object":55}],37:[function(t,e,r){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},{}],38:[function(t,e,r){var s=t("./_object-keys"),u=t("./_object-gops"),f=t("./_object-pie");e.exports=function(t){var e=s(t),r=u.f;if(r)for(var n,i=r(t),o=f.f,a=0;i.length>a;)o.call(t,n=i[a++])&&e.push(n);return e}},{"./_object-gops":74,"./_object-keys":77,"./_object-pie":78}],39:[function(t,e,r){var m=t("./_global"),b=t("./_core"),g=t("./_hide"),_=t("./_redefine"),y=t("./_ctx"),v="prototype",w=function(t,e,r){var n,i,o,a,s=t&w.F,u=t&w.G,f=t&w.S,l=t&w.P,h=t&w.B,c=u?m:f?m[e]||(m[e]={}):(m[e]||{})[v],d=u?b:b[e]||(b[e]={}),p=d[v]||(d[v]={});for(n in u&&(r=e),r)o=((i=!s&&c&&void 0!==c[n])?c:r)[n],a=h&&i?y(o,m):l&&"function"==typeof o?y(Function.call,o):o,c&&_(c,n,o,t&w.U),d[n]!=o&&g(d,n,a),l&&p[n]!=o&&(p[n]=o)};m.core=b,w.F=1,w.G=2,w.S=4,w.P=8,w.B=16,w.W=32,w.U=64,w.R=128,e.exports=w},{"./_core":31,"./_ctx":33,"./_global":45,"./_hide":47,"./_redefine":85}],40:[function(t,e,r){var n=t("./_wks")("match");e.exports=function(e){var r=/./;try{"/./"[e](r)}catch(t){try{return r[n]=!1,!"/./"[e](r)}catch(t){}}return!0}},{"./_wks":112}],41:[function(t,e,r){e.exports=function(t){try{return!!t()}catch(t){return!0}}},{}],42:[function(t,e,r){"use strict";var s=t("./_hide"),u=t("./_redefine"),f=t("./_fails"),l=t("./_defined"),h=t("./_wks");e.exports=function(e,t,r){var n=h(e),i=r(l,n,""[e]),o=i[0],a=i[1];f(function(){var t={};return t[n]=function(){return 7},7!=""[e](t)})&&(u(String.prototype,e,o),s(RegExp.prototype,n,2==t?function(t,e){return a.call(t,this,e)}:function(t){return a.call(t,this)}))}},{"./_defined":34,"./_fails":41,"./_hide":47,"./_redefine":85,"./_wks":112}],43:[function(t,e,r){"use strict";var n=t("./_an-object");e.exports=function(){var t=n(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},{"./_an-object":21}],44:[function(t,e,r){var c=t("./_ctx"),d=t("./_iter-call"),p=t("./_is-array-iter"),m=t("./_an-object"),b=t("./_to-length"),g=t("./core.get-iterator-method"),_={},y={};(r=e.exports=function(t,e,r,n,i){var o,a,s,u,f=i?function(){return t}:g(t),l=c(r,n,e?2:1),h=0;if("function"!=typeof f)throw TypeError(t+" is not iterable!");if(p(f)){for(o=b(t.length);h<o;h++)if((u=e?l(m(a=t[h])[0],a[1]):l(t[h]))===_||u===y)return u}else for(s=f.call(t);!(a=s.next()).done;)if((u=d(s,l,a.value,e))===_||u===y)return u}).BREAK=_,r.RETURN=y},{"./_an-object":21,"./_ctx":33,"./_is-array-iter":53,"./_iter-call":57,"./_to-length":102,"./core.get-iterator-method":113}],45:[function(t,e,r){var n=e.exports=void 0!==window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},{}],46:[function(t,e,r){var n={}.hasOwnProperty;e.exports=function(t,e){return n.call(t,e)}},{}],47:[function(t,e,r){var n=t("./_object-dp"),i=t("./_property-desc");e.exports=t("./_descriptors")?function(t,e,r){return n.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},{"./_descriptors":35,"./_object-dp":69,"./_property-desc":83}],48:[function(t,e,r){var n=t("./_global").document;e.exports=n&&n.documentElement},{"./_global":45}],49:[function(t,e,r){e.exports=!t("./_descriptors")&&!t("./_fails")(function(){return 7!=Object.defineProperty(t("./_dom-create")("div"),"a",{get:function(){return 7}}).a})},{"./_descriptors":35,"./_dom-create":36,"./_fails":41}],50:[function(t,e,r){var o=t("./_is-object"),a=t("./_set-proto").set;e.exports=function(t,e,r){var n,i=e.constructor;return i!==r&&"function"==typeof i&&(n=i.prototype)!==r.prototype&&o(n)&&a&&a(t,n),t}},{"./_is-object":55,"./_set-proto":86}],51:[function(t,e,r){e.exports=function(t,e,r){var n=void 0===r;switch(e.length){case 0:return n?t():t.call(r);case 1:return n?t(e[0]):t.call(r,e[0]);case 2:return n?t(e[0],e[1]):t.call(r,e[0],e[1]);case 3:return n?t(e[0],e[1],e[2]):t.call(r,e[0],e[1],e[2]);case 4:return n?t(e[0],e[1],e[2],e[3]):t.call(r,e[0],e[1],e[2],e[3])}return t.apply(r,e)}},{}],52:[function(t,e,r){var n=t("./_cof");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==n(t)?t.split(""):Object(t)}},{"./_cof":30}],53:[function(t,e,r){var n=t("./_iterators"),i=t("./_wks")("iterator"),o=Array.prototype;e.exports=function(t){return void 0!==t&&(n.Array===t||o[i]===t)}},{"./_iterators":62,"./_wks":112}],54:[function(t,e,r){var n=t("./_cof");e.exports=Array.isArray||function(t){return"Array"==n(t)}},{"./_cof":30}],55:[function(t,e,r){e.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},{}],56:[function(t,e,r){var n=t("./_is-object"),i=t("./_cof"),o=t("./_wks")("match");e.exports=function(t){var e;return n(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==i(t))}},{"./_cof":30,"./_is-object":55,"./_wks":112}],57:[function(t,e,r){var o=t("./_an-object");e.exports=function(e,t,r,n){try{return n?t(o(r)[0],r[1]):t(r)}catch(t){var i=e.return;throw void 0!==i&&o(i.call(e)),t}}},{"./_an-object":21}],58:[function(t,e,r){"use strict";var n=t("./_object-create"),i=t("./_property-desc"),o=t("./_set-to-string-tag"),a={};t("./_hide")(a,t("./_wks")("iterator"),function(){return this}),e.exports=function(t,e,r){t.prototype=n(a,{next:i(1,r)}),o(t,e+" Iterator")}},{"./_hide":47,"./_object-create":68,"./_property-desc":83,"./_set-to-string-tag":88,"./_wks":112}],59:[function(t,e,r){"use strict";var y=t("./_library"),v=t("./_export"),w=t("./_redefine"),E=t("./_hide"),x=t("./_iterators"),j=t("./_iter-create"),k=t("./_set-to-string-tag"),S=t("./_object-gpo"),I=t("./_wks")("iterator"),A=!([].keys&&"next"in[].keys()),T="values",O=function(){return this};e.exports=function(t,e,r,n,i,o,a){j(r,e,n);var s,u,f,l=function(t){if(!A&&t in p)return p[t];switch(t){case"keys":case T:return function(){return new r(this,t)}}return function(){return new r(this,t)}},h=e+" Iterator",c=i==T,d=!1,p=t.prototype,m=p[I]||p["@@iterator"]||i&&p[i],b=m||l(i),g=i?c?l("entries"):b:void 0,_="Array"==e&&p.entries||m;if(_&&(f=S(_.call(new t)))!==Object.prototype&&f.next&&(k(f,h,!0),y||"function"==typeof f[I]||E(f,I,O)),c&&m&&m.name!==T&&(d=!0,b=function(){return m.call(this)}),y&&!a||!A&&!d&&p[I]||E(p,I,b),x[e]=b,x[h]=O,i)if(s={values:c?b:l(T),keys:o?b:l("keys"),entries:g},a)for(u in s)u in p||w(p,u,s[u]);else v(v.P+v.F*(A||d),e,s);return s}},{"./_export":39,"./_hide":47,"./_iter-create":58,"./_iterators":62,"./_library":63,"./_object-gpo":75,"./_redefine":85,"./_set-to-string-tag":88,"./_wks":112}],60:[function(t,e,r){var o=t("./_wks")("iterator"),a=!1;try{var n=[7][o]();n.return=function(){a=!0},Array.from(n,function(){throw 2})}catch(t){}e.exports=function(t,e){if(!e&&!a)return!1;var r=!1;try{var n=[7],i=n[o]();i.next=function(){return{done:r=!0}},n[o]=function(){return i},t(n)}catch(t){}return r}},{"./_wks":112}],61:[function(t,e,r){e.exports=function(t,e){return{value:e,done:!!t}}},{}],62:[function(t,e,r){e.exports={}},{}],63:[function(t,e,r){e.exports=!1},{}],64:[function(t,e,r){var n=t("./_uid")("meta"),i=t("./_is-object"),o=t("./_has"),a=t("./_object-dp").f,s=0,u=Object.isExtensible||function(){return!0},f=!t("./_fails")(function(){return u(Object.preventExtensions({}))}),l=function(t){a(t,n,{value:{i:"O"+ ++s,w:{}}})},h=e.exports={KEY:n,NEED:!1,fastKey:function(t,e){if(!i(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,n)){if(!u(t))return"F";if(!e)return"E";l(t)}return t[n].i},getWeak:function(t,e){if(!o(t,n)){if(!u(t))return!0;if(!e)return!1;l(t)}return t[n].w},onFreeze:function(t){return f&&h.NEED&&u(t)&&!o(t,n)&&l(t),t}}},{"./_fails":41,"./_has":46,"./_is-object":55,"./_object-dp":69,"./_uid":108}],65:[function(t,e,r){var s=t("./_global"),u=t("./_task").set,f=s.MutationObserver||s.WebKitMutationObserver,l=s.process,h=s.Promise,c="process"==t("./_cof")(l);e.exports=function(){var r,n,i,t=function(){var t,e;for(c&&(t=l.domain)&&t.exit();r;){e=r.fn,r=r.next;try{e()}catch(t){throw r?i():n=void 0,t}}n=void 0,t&&t.enter()};if(c)i=function(){l.nextTick(t)};else if(!f||s.navigator&&s.navigator.standalone)if(h&&h.resolve){var e=h.resolve(void 0);i=function(){e.then(t)}}else i=function(){u.call(s,t)};else{var o=!0,a=document.createTextNode("");new f(t).observe(a,{characterData:!0}),i=function(){a.data=o=!o}}return function(t){var e={fn:t,next:void 0};n&&(n.next=e),r||(r=e,i()),n=e}}},{"./_cof":30,"./_global":45,"./_task":97}],66:[function(t,e,r){"use strict";var i=t("./_a-function");function n(t){var r,n;this.promise=new t(function(t,e){if(void 0!==r||void 0!==n)throw TypeError("Bad Promise constructor");r=t,n=e}),this.resolve=i(r),this.reject=i(n)}e.exports.f=function(t){return new n(t)}},{"./_a-function":18}],67:[function(t,e,r){"use strict";var c=t("./_object-keys"),d=t("./_object-gops"),p=t("./_object-pie"),m=t("./_to-object"),b=t("./_iobject"),i=Object.assign;e.exports=!i||t("./_fails")(function(){var t={},e={},r=Symbol(),n="abcdefghijklmnopqrst";return t[r]=7,n.split("").forEach(function(t){e[t]=t}),7!=i({},t)[r]||Object.keys(i({},e)).join("")!=n})?function(t,e){for(var r=m(t),n=arguments.length,i=1,o=d.f,a=p.f;i<n;)for(var s,u=b(arguments[i++]),f=o?c(u).concat(o(u)):c(u),l=f.length,h=0;h<l;)a.call(u,s=f[h++])&&(r[s]=u[s]);return r}:i},{"./_fails":41,"./_iobject":52,"./_object-gops":74,"./_object-keys":77,"./_object-pie":78,"./_to-object":103}],68:[function(n,t,e){var i=n("./_an-object"),o=n("./_object-dps"),a=n("./_enum-bug-keys"),s=n("./_shared-key")("IE_PROTO"),u=function(){},f="prototype",l=function(){var t,e=n("./_dom-create")("iframe"),r=a.length;for(e.style.display="none",n("./_html").appendChild(e),e.src="javascript:",(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),l=t.F;r--;)delete l[f][a[r]];return l()};t.exports=Object.create||function(t,e){var r;return null!==t?(u[f]=i(t),r=new u,u[f]=null,r[s]=t):r=l(),void 0===e?r:o(r,e)}},{"./_an-object":21,"./_dom-create":36,"./_enum-bug-keys":37,"./_html":48,"./_object-dps":70,"./_shared-key":89}],69:[function(t,e,r){var n=t("./_an-object"),i=t("./_ie8-dom-define"),o=t("./_to-primitive"),a=Object.defineProperty;r.f=t("./_descriptors")?Object.defineProperty:function(t,e,r){if(n(t),e=o(e,!0),n(r),i)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(t[e]=r.value),t}},{"./_an-object":21,"./_descriptors":35,"./_ie8-dom-define":49,"./_to-primitive":104}],70:[function(t,e,r){var a=t("./_object-dp"),s=t("./_an-object"),u=t("./_object-keys");e.exports=t("./_descriptors")?Object.defineProperties:function(t,e){s(t);for(var r,n=u(e),i=n.length,o=0;o<i;)a.f(t,r=n[o++],e[r]);return t}},{"./_an-object":21,"./_descriptors":35,"./_object-dp":69,"./_object-keys":77}],71:[function(t,e,r){var n=t("./_object-pie"),i=t("./_property-desc"),o=t("./_to-iobject"),a=t("./_to-primitive"),s=t("./_has"),u=t("./_ie8-dom-define"),f=Object.getOwnPropertyDescriptor;r.f=t("./_descriptors")?f:function(t,e){if(t=o(t),e=a(e,!0),u)try{return f(t,e)}catch(t){}if(s(t,e))return i(!n.f.call(t,e),t[e])}},{"./_descriptors":35,"./_has":46,"./_ie8-dom-define":49,"./_object-pie":78,"./_property-desc":83,"./_to-iobject":101,"./_to-primitive":104}],72:[function(t,e,r){var n=t("./_to-iobject"),i=t("./_object-gopn").f,o={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(t){return a&&"[object Window]"==o.call(t)?function(t){try{return i(t)}catch(t){return a.slice()}}(t):i(n(t))}},{"./_object-gopn":73,"./_to-iobject":101}],73:[function(t,e,r){var n=t("./_object-keys-internal"),i=t("./_enum-bug-keys").concat("length","prototype");r.f=Object.getOwnPropertyNames||function(t){return n(t,i)}},{"./_enum-bug-keys":37,"./_object-keys-internal":76}],74:[function(t,e,r){r.f=Object.getOwnPropertySymbols},{}],75:[function(t,e,r){var n=t("./_has"),i=t("./_to-object"),o=t("./_shared-key")("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(t){return t=i(t),n(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?a:null}},{"./_has":46,"./_shared-key":89,"./_to-object":103}],76:[function(t,e,r){var a=t("./_has"),s=t("./_to-iobject"),u=t("./_array-includes")(!1),f=t("./_shared-key")("IE_PROTO");e.exports=function(t,e){var r,n=s(t),i=0,o=[];for(r in n)r!=f&&a(n,r)&&o.push(r);for(;e.length>i;)a(n,r=e[i++])&&(~u(o,r)||o.push(r));return o}},{"./_array-includes":24,"./_has":46,"./_shared-key":89,"./_to-iobject":101}],77:[function(t,e,r){var n=t("./_object-keys-internal"),i=t("./_enum-bug-keys");e.exports=Object.keys||function(t){return n(t,i)}},{"./_enum-bug-keys":37,"./_object-keys-internal":76}],78:[function(t,e,r){r.f={}.propertyIsEnumerable},{}],79:[function(t,e,r){var i=t("./_export"),o=t("./_core"),a=t("./_fails");e.exports=function(t,e){var r=(o.Object||{})[t]||Object[t],n={};n[t]=e(r),i(i.S+i.F*a(function(){r(1)}),"Object",n)}},{"./_core":31,"./_export":39,"./_fails":41}],80:[function(t,e,r){var u=t("./_object-keys"),f=t("./_to-iobject"),l=t("./_object-pie").f;e.exports=function(s){return function(t){for(var e,r=f(t),n=u(r),i=n.length,o=0,a=[];o<i;)l.call(r,e=n[o++])&&a.push(s?[e,r[e]]:r[e]);return a}}},{"./_object-keys":77,"./_object-pie":78,"./_to-iobject":101}],81:[function(t,e,r){e.exports=function(t){try{return{e:!1,v:t()}}catch(t){return{e:!0,v:t}}}},{}],82:[function(t,e,r){var n=t("./_an-object"),i=t("./_is-object"),o=t("./_new-promise-capability");e.exports=function(t,e){if(n(t),i(e)&&e.constructor===t)return e;var r=o.f(t);return(0,r.resolve)(e),r.promise}},{"./_an-object":21,"./_is-object":55,"./_new-promise-capability":66}],83:[function(t,e,r){e.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},{}],84:[function(t,e,r){var i=t("./_redefine");e.exports=function(t,e,r){for(var n in e)i(t,n,e[n],r);return t}},{"./_redefine":85}],85:[function(t,e,r){var o=t("./_global"),a=t("./_hide"),s=t("./_has"),u=t("./_uid")("src"),n="toString",i=Function[n],f=(""+i).split(n);t("./_core").inspectSource=function(t){return i.call(t)},(e.exports=function(t,e,r,n){var i="function"==typeof r;i&&(s(r,"name")||a(r,"name",e)),t[e]!==r&&(i&&(s(r,u)||a(r,u,t[e]?""+t[e]:f.join(String(e)))),t===o?t[e]=r:n?t[e]?t[e]=r:a(t,e,r):(delete t[e],a(t,e,r)))})(Function.prototype,n,function(){return"function"==typeof this&&this[u]||i.call(this)})},{"./_core":31,"./_global":45,"./_has":46,"./_hide":47,"./_uid":108}],86:[function(e,t,r){var n=e("./_is-object"),i=e("./_an-object"),o=function(t,e){if(i(t),!n(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,r,n){try{(n=e("./_ctx")(Function.call,e("./_object-gopd").f(Object.prototype,"__proto__").set,2))(t,[]),r=!(t instanceof Array)}catch(t){r=!0}return function(t,e){return o(t,e),r?t.__proto__=e:n(t,e),t}}({},!1):void 0),check:o}},{"./_an-object":21,"./_ctx":33,"./_is-object":55,"./_object-gopd":71}],87:[function(t,e,r){"use strict";var n=t("./_global"),i=t("./_object-dp"),o=t("./_descriptors"),a=t("./_wks")("species");e.exports=function(t){var e=n[t];o&&e&&!e[a]&&i.f(e,a,{configurable:!0,get:function(){return this}})}},{"./_descriptors":35,"./_global":45,"./_object-dp":69,"./_wks":112}],88:[function(t,e,r){var n=t("./_object-dp").f,i=t("./_has"),o=t("./_wks")("toStringTag");e.exports=function(t,e,r){t&&!i(t=r?t:t.prototype,o)&&n(t,o,{configurable:!0,value:e})}},{"./_has":46,"./_object-dp":69,"./_wks":112}],89:[function(t,e,r){var n=t("./_shared")("keys"),i=t("./_uid");e.exports=function(t){return n[t]||(n[t]=i(t))}},{"./_shared":90,"./_uid":108}],90:[function(t,e,r){var n=t("./_core"),i=t("./_global"),o="__core-js_shared__",a=i[o]||(i[o]={});(e.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:n.version,mode:t("./_library")?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},{"./_core":31,"./_global":45,"./_library":63}],91:[function(t,e,r){var i=t("./_an-object"),o=t("./_a-function"),a=t("./_wks")("species");e.exports=function(t,e){var r,n=i(t).constructor;return void 0===n||null==(r=i(n)[a])?e:o(r)}},{"./_a-function":18,"./_an-object":21,"./_wks":112}],92:[function(t,e,r){"use strict";var n=t("./_fails");e.exports=function(t,e){return!!t&&n(function(){e?t.call(null,function(){},1):t.call(null)})}},{"./_fails":41}],93:[function(t,e,r){var u=t("./_to-integer"),f=t("./_defined");e.exports=function(s){return function(t,e){var r,n,i=String(f(t)),o=u(e),a=i.length;return o<0||a<=o?s?"":void 0:(r=i.charCodeAt(o))<55296||56319<r||o+1===a||(n=i.charCodeAt(o+1))<56320||57343<n?s?i.charAt(o):r:s?i.slice(o,o+2):n-56320+(r-55296<<10)+65536}}},{"./_defined":34,"./_to-integer":100}],94:[function(t,e,r){var n=t("./_is-regexp"),i=t("./_defined");e.exports=function(t,e,r){if(n(e))throw TypeError("String#"+r+" doesn't accept regex!");return String(i(t))}},{"./_defined":34,"./_is-regexp":56}],95:[function(t,e,r){var a=t("./_export"),n=t("./_defined"),s=t("./_fails"),u=t("./_string-ws"),i="["+u+"]",o=RegExp("^"+i+i+"*"),f=RegExp(i+i+"*$"),l=function(t,e,r){var n={},i=s(function(){return!!u[t]()||"​"!="​"[t]()}),o=n[t]=i?e(h):u[t];r&&(n[r]=o),a(a.P+a.F*i,"String",n)},h=l.trim=function(t,e){return t=String(n(t)),1&e&&(t=t.replace(o,"")),2&e&&(t=t.replace(f,"")),t};e.exports=l},{"./_defined":34,"./_export":39,"./_fails":41,"./_string-ws":96}],96:[function(t,e,r){e.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},{}],97:[function(t,e,r){var n,i,o,a=t("./_ctx"),s=t("./_invoke"),u=t("./_html"),f=t("./_dom-create"),l=t("./_global"),h=l.process,c=l.setImmediate,d=l.clearImmediate,p=l.MessageChannel,m=l.Dispatch,b=0,g={},_="onreadystatechange",y=function(){var t=+this;if(g.hasOwnProperty(t)){var e=g[t];delete g[t],e()}},v=function(t){y.call(t.data)};c&&d||(c=function(t){for(var e=[],r=1;arguments.length>r;)e.push(arguments[r++]);return g[++b]=function(){s("function"==typeof t?t:Function(t),e)},n(b),b},d=function(t){delete g[t]},"process"==t("./_cof")(h)?n=function(t){h.nextTick(a(y,t,1))}:m&&m.now?n=function(t){m.now(a(y,t,1))}:p?(o=(i=new p).port2,i.port1.onmessage=v,n=a(o.postMessage,o,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(n=function(t){l.postMessage(t+"","*")},l.addEventListener("message",v,!1)):n=_ in f("script")?function(t){u.appendChild(f("script"))[_]=function(){u.removeChild(this),y.call(t)}}:function(t){setTimeout(a(y,t,1),0)}),e.exports={set:c,clear:d}},{"./_cof":30,"./_ctx":33,"./_dom-create":36,"./_global":45,"./_html":48,"./_invoke":51}],98:[function(t,e,r){var n=t("./_to-integer"),i=Math.max,o=Math.min;e.exports=function(t,e){return(t=n(t))<0?i(t+e,0):o(t,e)}},{"./_to-integer":100}],99:[function(t,e,r){var n=t("./_to-integer"),i=t("./_to-length");e.exports=function(t){if(void 0===t)return 0;var e=n(t),r=i(e);if(e!==r)throw RangeError("Wrong length!");return r}},{"./_to-integer":100,"./_to-length":102}],100:[function(t,e,r){var n=Math.ceil,i=Math.floor;e.exports=function(t){return isNaN(t=+t)?0:(0<t?i:n)(t)}},{}],101:[function(t,e,r){var n=t("./_iobject"),i=t("./_defined");e.exports=function(t){return n(i(t))}},{"./_defined":34,"./_iobject":52}],102:[function(t,e,r){var n=t("./_to-integer"),i=Math.min;e.exports=function(t){return 0<t?i(n(t),9007199254740991):0}},{"./_to-integer":100}],103:[function(t,e,r){var n=t("./_defined");e.exports=function(t){return Object(n(t))}},{"./_defined":34}],104:[function(t,e,r){var i=t("./_is-object");e.exports=function(t,e){if(!i(t))return t;var r,n;if(e&&"function"==typeof(r=t.toString)&&!i(n=r.call(t)))return n;if("function"==typeof(r=t.valueOf)&&!i(n=r.call(t)))return n;if(!e&&"function"==typeof(r=t.toString)&&!i(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")}},{"./_is-object":55}],105:[function(t,e,r){"use strict";if(t("./_descriptors")){var g=t("./_library"),_=t("./_global"),y=t("./_fails"),v=t("./_export"),w=t("./_typed"),n=t("./_typed-buffer"),c=t("./_ctx"),E=t("./_an-instance"),i=t("./_property-desc"),x=t("./_hide"),o=t("./_redefine-all"),a=t("./_to-integer"),j=t("./_to-length"),k=t("./_to-index"),s=t("./_to-absolute-index"),u=t("./_to-primitive"),f=t("./_has"),S=t("./_classof"),I=t("./_is-object"),d=t("./_to-object"),p=t("./_is-array-iter"),A=t("./_object-create"),T=t("./_object-gpo"),O=t("./_object-gopn").f,m=t("./core.get-iterator-method"),l=t("./_uid"),h=t("./_wks"),b=t("./_array-methods"),P=t("./_array-includes"),M=t("./_species-constructor"),L=t("./es6.array.iterator"),C=t("./_iterators"),R=t("./_iter-detect"),B=t("./_set-species"),N=t("./_array-fill"),D=t("./_array-copy-within"),U=t("./_object-dp"),F=t("./_object-gopd"),z=U.f,H=F.f,G=_.RangeError,W=_.TypeError,Z=_.Uint8Array,q="ArrayBuffer",Y="Shared"+q,V="BYTES_PER_ELEMENT",X="prototype",J=Array[X],K=n.ArrayBuffer,Q=n.DataView,$=b(0),tt=b(2),et=b(3),rt=b(4),nt=b(5),it=b(6),ot=P(!0),at=P(!1),st=L.values,ut=L.keys,ft=L.entries,lt=J.lastIndexOf,ht=J.reduce,ct=J.reduceRight,dt=J.join,pt=J.sort,mt=J.slice,bt=J.toString,gt=J.toLocaleString,_t=h("iterator"),yt=h("toStringTag"),vt=l("typed_constructor"),wt=l("def_constructor"),Et=w.CONSTR,xt=w.TYPED,jt=w.VIEW,kt="Wrong length!",St=b(1,function(t,e){return Pt(M(t,t[wt]),e)}),It=y(function(){return 1===new Z(new Uint16Array([1]).buffer)[0]}),At=!!Z&&!!Z[X].set&&y(function(){new Z(1).set({})}),Tt=function(t,e){var r=a(t);if(r<0||r%e)throw G("Wrong offset!");return r},Ot=function(t){if(I(t)&&xt in t)return t;throw W(t+" is not a typed array!")},Pt=function(t,e){if(!(I(t)&&vt in t))throw W("It is not a typed array constructor!");return new t(e)},Mt=function(t,e){return Lt(M(t,t[wt]),e)},Lt=function(t,e){for(var r=0,n=e.length,i=Pt(t,n);r<n;)i[r]=e[r++];return i},Ct=function(t,e,r){z(t,e,{get:function(){return this._d[r]}})},Rt=function(t){var e,r,n,i,o,a,s=d(t),u=arguments.length,f=1<u?arguments[1]:void 0,l=void 0!==f,h=m(s);if(null!=h&&!p(h)){for(a=h.call(s),n=[],e=0;!(o=a.next()).done;e++)n.push(o.value);s=n}for(l&&2<u&&(f=c(f,arguments[2],2)),e=0,r=j(s.length),i=Pt(this,r);e<r;e++)i[e]=l?f(s[e],e):s[e];return i},Bt=function(){for(var t=0,e=arguments.length,r=Pt(this,e);t<e;)r[t]=arguments[t++];return r},Nt=!!Z&&y(function(){gt.call(new Z(1))}),Dt=function(){return gt.apply(Nt?mt.call(Ot(this)):Ot(this),arguments)},Ut={copyWithin:function(t,e){return D.call(Ot(this),t,e,2<arguments.length?arguments[2]:void 0)},every:function(t){return rt(Ot(this),t,1<arguments.length?arguments[1]:void 0)},fill:function(t){return N.apply(Ot(this),arguments)},filter:function(t){return Mt(this,tt(Ot(this),t,1<arguments.length?arguments[1]:void 0))},find:function(t){return nt(Ot(this),t,1<arguments.length?arguments[1]:void 0)},findIndex:function(t){return it(Ot(this),t,1<arguments.length?arguments[1]:void 0)},forEach:function(t){$(Ot(this),t,1<arguments.length?arguments[1]:void 0)},indexOf:function(t){return at(Ot(this),t,1<arguments.length?arguments[1]:void 0)},includes:function(t){return ot(Ot(this),t,1<arguments.length?arguments[1]:void 0)},join:function(t){return dt.apply(Ot(this),arguments)},lastIndexOf:function(t){return lt.apply(Ot(this),arguments)},map:function(t){return St(Ot(this),t,1<arguments.length?arguments[1]:void 0)},reduce:function(t){return ht.apply(Ot(this),arguments)},reduceRight:function(t){return ct.apply(Ot(this),arguments)},reverse:function(){for(var t,e=this,r=Ot(e).length,n=Math.floor(r/2),i=0;i<n;)t=e[i],e[i++]=e[--r],e[r]=t;return e},some:function(t){return et(Ot(this),t,1<arguments.length?arguments[1]:void 0)},sort:function(t){return pt.call(Ot(this),t)},subarray:function(t,e){var r=Ot(this),n=r.length,i=s(t,n);return new(M(r,r[wt]))(r.buffer,r.byteOffset+i*r.BYTES_PER_ELEMENT,j((void 0===e?n:s(e,n))-i))}},Ft=function(t,e){return Mt(this,mt.call(Ot(this),t,e))},zt=function(t){Ot(this);var e=Tt(arguments[1],1),r=this.length,n=d(t),i=j(n.length),o=0;if(r<i+e)throw G(kt);for(;o<i;)this[e+o]=n[o++]},Ht={entries:function(){return ft.call(Ot(this))},keys:function(){return ut.call(Ot(this))},values:function(){return st.call(Ot(this))}},Gt=function(t,e){return I(t)&&t[xt]&&"symbol"!=typeof e&&e in t&&String(+e)==String(e)},Wt=function(t,e){return Gt(t,e=u(e,!0))?i(2,t[e]):H(t,e)},Zt=function(t,e,r){return!(Gt(t,e=u(e,!0))&&I(r)&&f(r,"value"))||f(r,"get")||f(r,"set")||r.configurable||f(r,"writable")&&!r.writable||f(r,"enumerable")&&!r.enumerable?z(t,e,r):(t[e]=r.value,t)};Et||(F.f=Wt,U.f=Zt),v(v.S+v.F*!Et,"Object",{getOwnPropertyDescriptor:Wt,defineProperty:Zt}),y(function(){bt.call({})})&&(bt=gt=function(){return dt.call(this)});var qt=o({},Ut);o(qt,Ht),x(qt,_t,Ht.values),o(qt,{slice:Ft,set:zt,constructor:function(){},toString:bt,toLocaleString:Dt}),Ct(qt,"buffer","b"),Ct(qt,"byteOffset","o"),Ct(qt,"byteLength","l"),Ct(qt,"length","e"),z(qt,yt,{get:function(){return this[xt]}}),e.exports=function(t,h,e,o){var c=t+((o=!!o)?"Clamped":"")+"Array",r="get"+t,a="set"+t,d=_[c],s=d||{},n=d&&T(d),i=!d||!w.ABV,u={},f=d&&d[X],p=function(t,i){z(t,i,{get:function(){return t=i,(e=this._d).v[r](t*h+e.o,It);var t,e},set:function(t){return e=i,r=t,n=this._d,o&&(r=(r=Math.round(r))<0?0:255<r?255:255&r),void n.v[a](e*h+n.o,r,It);var e,r,n},enumerable:!0})};i?(d=e(function(t,e,r,n){E(t,d,c,"_d");var i,o,a,s,u=0,f=0;if(I(e)){if(!(e instanceof K||(s=S(e))==q||s==Y))return xt in e?Lt(d,e):Rt.call(d,e);i=e,f=Tt(r,h);var l=e.byteLength;if(void 0===n){if(l%h)throw G(kt);if((o=l-f)<0)throw G(kt)}else if(l<(o=j(n)*h)+f)throw G(kt);a=o/h}else a=k(e),i=new K(o=a*h);for(x(t,"_d",{b:i,o:f,l:o,e:a,v:new Q(i)});u<a;)p(t,u++)}),f=d[X]=A(qt),x(f,"constructor",d)):y(function(){d(1)})&&y(function(){new d(-1)})&&R(function(t){new d,new d(null),new d(1.5),new d(t)},!0)||(d=e(function(t,e,r,n){var i;return E(t,d,c),I(e)?e instanceof K||(i=S(e))==q||i==Y?void 0!==n?new s(e,Tt(r,h),n):void 0!==r?new s(e,Tt(r,h)):new s(e):xt in e?Lt(d,e):Rt.call(d,e):new s(k(e))}),$(n!==Function.prototype?O(s).concat(O(n)):O(s),function(t){t in d||x(d,t,s[t])}),d[X]=f,g||(f.constructor=d));var l=f[_t],m=!!l&&("values"==l.name||null==l.name),b=Ht.values;x(d,vt,!0),x(f,xt,c),x(f,jt,!0),x(f,wt,d),(o?new d(1)[yt]==c:yt in f)||z(f,yt,{get:function(){return c}}),u[c]=d,v(v.G+v.W+v.F*(d!=s),u),v(v.S,c,{BYTES_PER_ELEMENT:h}),v(v.S+v.F*y(function(){s.of.call(d,1)}),c,{from:Rt,of:Bt}),V in f||x(f,V,h),v(v.P,c,Ut),B(c),v(v.P+v.F*At,c,{set:zt}),v(v.P+v.F*!m,c,Ht),g||f.toString==bt||(f.toString=bt),v(v.P+v.F*y(function(){new d(1).slice()}),c,{slice:Ft}),v(v.P+v.F*(y(function(){return[1,2].toLocaleString()!=new d([1,2]).toLocaleString()})||!y(function(){f.toLocaleString.call([1,2])})),c,{toLocaleString:Dt}),C[c]=m?l:b,g||m||x(f,_t,b)}}else e.exports=function(){}},{"./_an-instance":20,"./_array-copy-within":22,"./_array-fill":23,"./_array-includes":24,"./_array-methods":25,"./_classof":29,"./_ctx":33,"./_descriptors":35,"./_export":39,"./_fails":41,"./_global":45,"./_has":46,"./_hide":47,"./_is-array-iter":53,"./_is-object":55,"./_iter-detect":60,"./_iterators":62,"./_library":63,"./_object-create":68,"./_object-dp":69,"./_object-gopd":71,"./_object-gopn":73,"./_object-gpo":75,"./_property-desc":83,"./_redefine-all":84,"./_set-species":87,"./_species-constructor":91,"./_to-absolute-index":98,"./_to-index":99,"./_to-integer":100,"./_to-length":102,"./_to-object":103,"./_to-primitive":104,"./_typed":107,"./_typed-buffer":106,"./_uid":108,"./_wks":112,"./core.get-iterator-method":113,"./es6.array.iterator":122}],106:[function(t,e,r){"use strict";var n=t("./_global"),i=t("./_descriptors"),o=t("./_library"),a=t("./_typed"),s=t("./_hide"),u=t("./_redefine-all"),f=t("./_fails"),l=t("./_an-instance"),h=t("./_to-integer"),c=t("./_to-length"),d=t("./_to-index"),p=t("./_object-gopn").f,m=t("./_object-dp").f,b=t("./_array-fill"),g=t("./_set-to-string-tag"),_="ArrayBuffer",y="DataView",v="prototype",w="Wrong index!",E=n[_],x=n[y],j=n.Math,k=n.RangeError,S=n.Infinity,I=E,A=j.abs,T=j.pow,O=j.floor,P=j.log,M=j.LN2,L="byteLength",C="byteOffset",R=i?"_b":"buffer",B=i?"_l":L,N=i?"_o":C;function D(t,e,r){var n,i,o,a=new Array(r),s=8*r-e-1,u=(1<<s)-1,f=u>>1,l=23===e?T(2,-24)-T(2,-77):0,h=0,c=t<0||0===t&&1/t<0?1:0;for((t=A(t))!=t||t===S?(i=t!=t?1:0,n=u):(n=O(P(t)/M),t*(o=T(2,-n))<1&&(n--,o*=2),2<=(t+=1<=n+f?l/o:l*T(2,1-f))*o&&(n++,o/=2),u<=n+f?(i=0,n=u):1<=n+f?(i=(t*o-1)*T(2,e),n+=f):(i=t*T(2,f-1)*T(2,e),n=0));8<=e;a[h++]=255&i,i/=256,e-=8);for(n=n<<e|i,s+=e;0<s;a[h++]=255&n,n/=256,s-=8);return a[--h]|=128*c,a}function U(t,e,r){var n,i=8*r-e-1,o=(1<<i)-1,a=o>>1,s=i-7,u=r-1,f=t[u--],l=127&f;for(f>>=7;0<s;l=256*l+t[u],u--,s-=8);for(n=l&(1<<-s)-1,l>>=-s,s+=e;0<s;n=256*n+t[u],u--,s-=8);if(0===l)l=1-a;else{if(l===o)return n?NaN:f?-S:S;n+=T(2,e),l-=a}return(f?-1:1)*n*T(2,l-e)}function F(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]}function z(t){return[255&t]}function H(t){return[255&t,t>>8&255]}function G(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function W(t){return D(t,52,8)}function Z(t){return D(t,23,4)}function q(t,e,r){m(t[v],e,{get:function(){return this[r]}})}function Y(t,e,r,n){var i=d(+r);if(i+e>t[B])throw k(w);var o=t[R]._b,a=i+t[N],s=o.slice(a,a+e);return n?s:s.reverse()}function V(t,e,r,n,i,o){var a=d(+r);if(a+e>t[B])throw k(w);for(var s=t[R]._b,u=a+t[N],f=n(+i),l=0;l<e;l++)s[u+l]=f[o?l:e-l-1]}if(a.ABV){if(!f(function(){E(1)})||!f(function(){new E(-1)})||f(function(){return new E,new E(1.5),new E(NaN),E.name!=_})){for(var X,J=(E=function(t){return l(this,E),new I(d(t))})[v]=I[v],K=p(I),Q=0;K.length>Q;)(X=K[Q++])in E||s(E,X,I[X]);o||(J.constructor=E)}var $=new x(new E(2)),tt=x[v].setInt8;$.setInt8(0,2147483648),$.setInt8(1,2147483649),!$.getInt8(0)&&$.getInt8(1)||u(x[v],{setInt8:function(t,e){tt.call(this,t,e<<24>>24)},setUint8:function(t,e){tt.call(this,t,e<<24>>24)}},!0)}else E=function(t){l(this,E,_);var e=d(t);this._b=b.call(new Array(e),0),this[B]=e},x=function(t,e,r){l(this,x,y),l(t,E,y);var n=t[B],i=h(e);if(i<0||n<i)throw k("Wrong offset!");if(n<i+(r=void 0===r?n-i:c(r)))throw k("Wrong length!");this[R]=t,this[N]=i,this[B]=r},i&&(q(E,L,"_l"),q(x,"buffer","_b"),q(x,L,"_l"),q(x,C,"_o")),u(x[v],{getInt8:function(t){return Y(this,1,t)[0]<<24>>24},getUint8:function(t){return Y(this,1,t)[0]},getInt16:function(t){var e=Y(this,2,t,arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=Y(this,2,t,arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return F(Y(this,4,t,arguments[1]))},getUint32:function(t){return F(Y(this,4,t,arguments[1]))>>>0},getFloat32:function(t){return U(Y(this,4,t,arguments[1]),23,4)},getFloat64:function(t){return U(Y(this,8,t,arguments[1]),52,8)},setInt8:function(t,e){V(this,1,t,z,e)},setUint8:function(t,e){V(this,1,t,z,e)},setInt16:function(t,e){V(this,2,t,H,e,arguments[2])},setUint16:function(t,e){V(this,2,t,H,e,arguments[2])},setInt32:function(t,e){V(this,4,t,G,e,arguments[2])},setUint32:function(t,e){V(this,4,t,G,e,arguments[2])},setFloat32:function(t,e){V(this,4,t,Z,e,arguments[2])},setFloat64:function(t,e){V(this,8,t,W,e,arguments[2])}});g(E,_),g(x,y),s(x[v],a.VIEW,!0),r[_]=E,r[y]=x},{"./_an-instance":20,"./_array-fill":23,"./_descriptors":35,"./_fails":41,"./_global":45,"./_hide":47,"./_library":63,"./_object-dp":69,"./_object-gopn":73,"./_redefine-all":84,"./_set-to-string-tag":88,"./_to-index":99,"./_to-integer":100,"./_to-length":102,"./_typed":107}],107:[function(t,e,r){for(var n,i=t("./_global"),o=t("./_hide"),a=t("./_uid"),s=a("typed_array"),u=a("view"),f=!(!i.ArrayBuffer||!i.DataView),l=f,h=0,c="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");h<9;)(n=i[c[h++]])?(o(n.prototype,s,!0),o(n.prototype,u,!0)):l=!1;e.exports={ABV:f,CONSTR:l,TYPED:s,VIEW:u}},{"./_global":45,"./_hide":47,"./_uid":108}],108:[function(t,e,r){var n=0,i=Math.random();e.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+i).toString(36))}},{}],109:[function(t,e,r){var n=t("./_global").navigator;e.exports=n&&n.userAgent||""},{"./_global":45}],110:[function(t,e,r){var n=t("./_global"),i=t("./_core"),o=t("./_library"),a=t("./_wks-ext"),s=t("./_object-dp").f;e.exports=function(t){var e=i.Symbol||(i.Symbol=o?{}:n.Symbol||{});"_"==t.charAt(0)||t in e||s(e,t,{value:a.f(t)})}},{"./_core":31,"./_global":45,"./_library":63,"./_object-dp":69,"./_wks-ext":111}],111:[function(t,e,r){r.f=t("./_wks")},{"./_wks":112}],112:[function(t,e,r){var n=t("./_shared")("wks"),i=t("./_uid"),o=t("./_global").Symbol,a="function"==typeof o;(e.exports=function(t){return n[t]||(n[t]=a&&o[t]||(a?o:i)("Symbol."+t))}).store=n},{"./_global":45,"./_shared":90,"./_uid":108}],113:[function(t,e,r){var n=t("./_classof"),i=t("./_wks")("iterator"),o=t("./_iterators");e.exports=t("./_core").getIteratorMethod=function(t){if(null!=t)return t[i]||t["@@iterator"]||o[n(t)]}},{"./_classof":29,"./_core":31,"./_iterators":62,"./_wks":112}],114:[function(t,e,r){var n=t("./_export");n(n.P,"Array",{fill:t("./_array-fill")}),t("./_add-to-unscopables")("fill")},{"./_add-to-unscopables":19,"./_array-fill":23,"./_export":39}],115:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-methods")(2);n(n.P+n.F*!t("./_strict-method")([].filter,!0),"Array",{filter:function(t){return i(this,t,arguments[1])}})},{"./_array-methods":25,"./_export":39,"./_strict-method":92}],116:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-methods")(6),o="findIndex",a=!0;o in[]&&Array(1)[o](function(){a=!1}),n(n.P+n.F*a,"Array",{findIndex:function(t){return i(this,t,1<arguments.length?arguments[1]:void 0)}}),t("./_add-to-unscopables")(o)},{"./_add-to-unscopables":19,"./_array-methods":25,"./_export":39}],117:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-methods")(5),o="find",a=!0;o in[]&&Array(1)[o](function(){a=!1}),n(n.P+n.F*a,"Array",{find:function(t){return i(this,t,1<arguments.length?arguments[1]:void 0)}}),t("./_add-to-unscopables")(o)},{"./_add-to-unscopables":19,"./_array-methods":25,"./_export":39}],118:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-methods")(0),o=t("./_strict-method")([].forEach,!0);n(n.P+n.F*!o,"Array",{forEach:function(t){return i(this,t,arguments[1])}})},{"./_array-methods":25,"./_export":39,"./_strict-method":92}],119:[function(t,e,r){"use strict";var c=t("./_ctx"),n=t("./_export"),d=t("./_to-object"),p=t("./_iter-call"),m=t("./_is-array-iter"),b=t("./_to-length"),g=t("./_create-property"),_=t("./core.get-iterator-method");n(n.S+n.F*!t("./_iter-detect")(function(t){Array.from(t)}),"Array",{from:function(t){var e,r,n,i,o=d(t),a="function"==typeof this?this:Array,s=arguments.length,u=1<s?arguments[1]:void 0,f=void 0!==u,l=0,h=_(o);if(f&&(u=c(u,2<s?arguments[2]:void 0,2)),null==h||a==Array&&m(h))for(r=new a(e=b(o.length));l<e;l++)g(r,l,f?u(o[l],l):o[l]);else for(i=h.call(o),r=new a;!(n=i.next()).done;l++)g(r,l,f?p(i,u,[n.value,l],!0):n.value);return r.length=l,r}})},{"./_create-property":32,"./_ctx":33,"./_export":39,"./_is-array-iter":53,"./_iter-call":57,"./_iter-detect":60,"./_to-length":102,"./_to-object":103,"./core.get-iterator-method":113}],120:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-includes")(!1),o=[].indexOf,a=!!o&&1/[1].indexOf(1,-0)<0;n(n.P+n.F*(a||!t("./_strict-method")(o)),"Array",{indexOf:function(t){return a?o.apply(this,arguments)||0:i(this,t,arguments[1])}})},{"./_array-includes":24,"./_export":39,"./_strict-method":92}],121:[function(t,e,r){var n=t("./_export");n(n.S,"Array",{isArray:t("./_is-array")})},{"./_export":39,"./_is-array":54}],122:[function(t,e,r){"use strict";var n=t("./_add-to-unscopables"),i=t("./_iter-step"),o=t("./_iterators"),a=t("./_to-iobject");e.exports=t("./_iter-define")(Array,"Array",function(t,e){this._t=a(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,r=this._i++;return!t||r>=t.length?(this._t=void 0,i(1)):i(0,"keys"==e?r:"values"==e?t[r]:[r,t[r]])},"values"),o.Arguments=o.Array,n("keys"),n("values"),n("entries")},{"./_add-to-unscopables":19,"./_iter-define":59,"./_iter-step":61,"./_iterators":62,"./_to-iobject":101}],123:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-methods")(1);n(n.P+n.F*!t("./_strict-method")([].map,!0),"Array",{map:function(t){return i(this,t,arguments[1])}})},{"./_array-methods":25,"./_export":39,"./_strict-method":92}],124:[function(t,e,r){var n=Date.prototype,i="Invalid Date",o="toString",a=n[o],s=n.getTime;new Date(NaN)+""!=i&&t("./_redefine")(n,o,function(){var t=s.call(this);return t==t?a.call(this):i})},{"./_redefine":85}],125:[function(t,e,r){var n=t("./_export");n(n.P,"Function",{bind:t("./_bind")})},{"./_bind":28,"./_export":39}],126:[function(t,e,r){"use strict";var n=t("./_global"),i=t("./_has"),o=t("./_cof"),a=t("./_inherit-if-required"),l=t("./_to-primitive"),s=t("./_fails"),u=t("./_object-gopn").f,f=t("./_object-gopd").f,h=t("./_object-dp").f,c=t("./_string-trim").trim,d="Number",p=n[d],m=p,b=p.prototype,g=o(t("./_object-create")(b))==d,_="trim"in String.prototype,y=function(t){var e=l(t,!1);if("string"==typeof e&&2<e.length){var r,n,i,o=(e=_?e.trim():c(e,3)).charCodeAt(0);if(43===o||45===o){if(88===(r=e.charCodeAt(2))||120===r)return NaN}else if(48===o){switch(e.charCodeAt(1)){case 66:case 98:n=2,i=49;break;case 79:case 111:n=8,i=55;break;default:return+e}for(var a,s=e.slice(2),u=0,f=s.length;u<f;u++)if((a=s.charCodeAt(u))<48||i<a)return NaN;return parseInt(s,n)}}return+e};if(!p(" 0o1")||!p("0b1")||p("+0x1")){p=function(t){var e=arguments.length<1?0:t,r=this;return r instanceof p&&(g?s(function(){b.valueOf.call(r)}):o(r)!=d)?a(new m(y(e)),r,p):y(e)};for(var v,w=t("./_descriptors")?u(m):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),E=0;w.length>E;E++)i(m,v=w[E])&&!i(p,v)&&h(p,v,f(m,v));(p.prototype=b).constructor=p,t("./_redefine")(n,d,p)}},{"./_cof":30,"./_descriptors":35,"./_fails":41,"./_global":45,"./_has":46,"./_inherit-if-required":50,"./_object-create":68,"./_object-dp":69,"./_object-gopd":71,"./_object-gopn":73,"./_redefine":85,"./_string-trim":95,"./_to-primitive":104}],127:[function(t,e,r){var n=t("./_export");n(n.S+n.F,"Object",{assign:t("./_object-assign")})},{"./_export":39,"./_object-assign":67}],128:[function(t,e,r){var n=t("./_export");n(n.S,"Object",{create:t("./_object-create")})},{"./_export":39,"./_object-create":68}],129:[function(t,e,r){var n=t("./_export");n(n.S+n.F*!t("./_descriptors"),"Object",{defineProperty:t("./_object-dp").f})},{"./_descriptors":35,"./_export":39,"./_object-dp":69}],130:[function(t,e,r){var n=t("./_to-object"),i=t("./_object-keys");t("./_object-sap")("keys",function(){return function(t){return i(n(t))}})},{"./_object-keys":77,"./_object-sap":79,"./_to-object":103}],131:[function(t,e,r){var n=t("./_export");n(n.S,"Object",{setPrototypeOf:t("./_set-proto").set})},{"./_export":39,"./_set-proto":86}],132:[function(r,t,e){"use strict";var n,i,o,a,s=r("./_library"),u=r("./_global"),f=r("./_ctx"),l=r("./_classof"),h=r("./_export"),c=r("./_is-object"),d=r("./_a-function"),p=r("./_an-instance"),m=r("./_for-of"),b=r("./_species-constructor"),g=r("./_task").set,_=r("./_microtask")(),y=r("./_new-promise-capability"),v=r("./_perform"),w=r("./_user-agent"),E=r("./_promise-resolve"),x="Promise",j=u.TypeError,k=u.process,S=k&&k.versions,I=S&&S.v8||"",A=u[x],T="process"==l(k),O=function(){},P=i=y.f,M=!!function(){try{var t=A.resolve(1),e=(t.constructor={})[r("./_wks")("species")]=function(t){t(O,O)};return(T||"function"==typeof PromiseRejectionEvent)&&t.then(O)instanceof e&&0!==I.indexOf("6.6")&&-1===w.indexOf("Chrome/66")}catch(t){}}(),L=function(t){var e;return!(!c(t)||"function"!=typeof(e=t.then))&&e},C=function(l,r){if(!l._n){l._n=!0;var n=l._c;_(function(){for(var u=l._v,f=1==l._s,t=0,e=function(t){var e,r,n,i=f?t.ok:t.fail,o=t.resolve,a=t.reject,s=t.domain;try{i?(f||(2==l._h&&N(l),l._h=1),!0===i?e=u:(s&&s.enter(),e=i(u),s&&(s.exit(),n=!0)),e===t.promise?a(j("Promise-chain cycle")):(r=L(e))?r.call(e,o,a):o(e)):a(u)}catch(t){s&&!n&&s.exit(),a(t)}};n.length>t;)e(n[t++]);l._c=[],l._n=!1,r&&!l._h&&R(l)})}},R=function(o){g.call(u,function(){var t,e,r,n=o._v,i=B(o);if(i&&(t=v(function(){T?k.emit("unhandledRejection",n,o):(e=u.onunhandledrejection)?e({promise:o,reason:n}):(r=u.console)&&r.error&&r.error("Unhandled promise rejection",n)}),o._h=T||B(o)?2:1),o._a=void 0,i&&t.e)throw t.v})},B=function(t){return 1!==t._h&&0===(t._a||t._c).length},N=function(e){g.call(u,function(){var t;T?k.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},D=function(t){var e=this;e._d||(e._d=!0,(e=e._w||e)._v=t,e._s=2,e._a||(e._a=e._c.slice()),C(e,!0))},U=function(t){var r,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw j("Promise can't be resolved itself");(r=L(t))?_(function(){var e={_w:n,_d:!1};try{r.call(t,f(U,e,1),f(D,e,1))}catch(t){D.call(e,t)}}):(n._v=t,n._s=1,C(n,!1))}catch(t){D.call({_w:n,_d:!1},t)}}};M||(A=function(t){p(this,A,x,"_h"),d(t),n.call(this);try{t(f(U,this,1),f(D,this,1))}catch(t){D.call(this,t)}},(n=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=r("./_redefine-all")(A.prototype,{then:function(t,e){var r=P(b(this,A));return r.ok="function"!=typeof t||t,r.fail="function"==typeof e&&e,r.domain=T?k.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&C(this,!1),r.promise},catch:function(t){return this.then(void 0,t)}}),o=function(){var t=new n;this.promise=t,this.resolve=f(U,t,1),this.reject=f(D,t,1)},y.f=P=function(t){return t===A||t===a?new o(t):i(t)}),h(h.G+h.W+h.F*!M,{Promise:A}),r("./_set-to-string-tag")(A,x),r("./_set-species")(x),a=r("./_core")[x],h(h.S+h.F*!M,x,{reject:function(t){var e=P(this);return(0,e.reject)(t),e.promise}}),h(h.S+h.F*(s||!M),x,{resolve:function(t){return E(s&&this===a?A:this,t)}}),h(h.S+h.F*!(M&&r("./_iter-detect")(function(t){A.all(t).catch(O)})),x,{all:function(t){var a=this,e=P(a),s=e.resolve,u=e.reject,r=v(function(){var n=[],i=0,o=1;m(t,!1,function(t){var e=i++,r=!1;n.push(void 0),o++,a.resolve(t).then(function(t){r||(r=!0,n[e]=t,--o||s(n))},u)}),--o||s(n)});return r.e&&u(r.v),e.promise},race:function(t){var e=this,r=P(e),n=r.reject,i=v(function(){m(t,!1,function(t){e.resolve(t).then(r.resolve,n)})});return i.e&&n(i.v),r.promise}})},{"./_a-function":18,"./_an-instance":20,"./_classof":29,"./_core":31,"./_ctx":33,"./_export":39,"./_for-of":44,"./_global":45,"./_is-object":55,"./_iter-detect":60,"./_library":63,"./_microtask":65,"./_new-promise-capability":66,"./_perform":81,"./_promise-resolve":82,"./_redefine-all":84,"./_set-species":87,"./_set-to-string-tag":88,"./_species-constructor":91,"./_task":97,"./_user-agent":109,"./_wks":112}],133:[function(t,e,r){var n=t("./_export"),s=t("./_object-create"),u=t("./_a-function"),f=t("./_an-object"),l=t("./_is-object"),i=t("./_fails"),h=t("./_bind"),c=(t("./_global").Reflect||{}).construct,d=i(function(){function t(){}return!(c(function(){},[],t)instanceof t)}),p=!i(function(){c(function(){})});n(n.S+n.F*(d||p),"Reflect",{construct:function(t,e){u(t),f(e);var r=arguments.length<3?t:u(arguments[2]);if(p&&!d)return c(t,e,r);if(t==r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return n.push.apply(n,e),new(h.apply(t,n))}var i=r.prototype,o=s(l(i)?i:Object.prototype),a=Function.apply.call(t,o,e);return l(a)?a:o}})},{"./_a-function":18,"./_an-object":21,"./_bind":28,"./_export":39,"./_fails":41,"./_global":45,"./_is-object":55,"./_object-create":68}],134:[function(t,e,r){t("./_descriptors")&&"g"!=/./g.flags&&t("./_object-dp").f(RegExp.prototype,"flags",{configurable:!0,get:t("./_flags")})},{"./_descriptors":35,"./_flags":43,"./_object-dp":69}],135:[function(t,e,r){t("./_fix-re-wks")("match",1,function(n,i,t){return[function(t){"use strict";var e=n(this),r=null==t?void 0:t[i];return void 0!==r?r.call(t,e):new RegExp(t)[i](String(e))},t]})},{"./_fix-re-wks":42}],136:[function(t,e,r){t("./_fix-re-wks")("replace",2,function(i,o,a){return[function(t,e){"use strict";var r=i(this),n=null==t?void 0:t[o];return void 0!==n?n.call(t,r,e):a.call(String(r),t,e)},a]})},{"./_fix-re-wks":42}],137:[function(e,t,r){e("./_fix-re-wks")("split",2,function(i,o,a){"use strict";var d=e("./_is-regexp"),p=a,m=[].push,t="split",b="length",g="lastIndex";if("c"=="abbc"[t](/(b)*/)[1]||4!="test"[t](/(?:)/,-1)[b]||2!="ab"[t](/(?:ab)*/)[b]||4!="."[t](/(.?)(.?)/)[b]||1<"."[t](/()()/)[b]||""[t](/.?/)[b]){var _=void 0===/()??/.exec("")[1];a=function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!d(t))return p.call(r,t,e);var n,i,o,a,s,u=[],f=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),l=0,h=void 0===e?4294967295:e>>>0,c=new RegExp(t.source,f+"g");for(_||(n=new RegExp("^"+c.source+"$(?!\\s)",f));(i=c.exec(r))&&!(l<(o=i.index+i[0][b])&&(u.push(r.slice(l,i.index)),!_&&1<i[b]&&i[0].replace(n,function(){for(s=1;s<arguments[b]-2;s++)void 0===arguments[s]&&(i[s]=void 0)}),1<i[b]&&i.index<r[b]&&m.apply(u,i.slice(1)),a=i[0][b],l=o,u[b]>=h));)c[g]===i.index&&c[g]++;return l===r[b]?!a&&c.test("")||u.push(""):u.push(r.slice(l)),u[b]>h?u.slice(0,h):u}}else"0"[t](void 0,0)[b]&&(a=function(t,e){return void 0===t&&0===e?[]:p.call(this,t,e)});return[function(t,e){var r=i(this),n=null==t?void 0:t[o];return void 0!==n?n.call(t,r,e):a.call(String(r),t,e)},a]})},{"./_fix-re-wks":42,"./_is-regexp":56}],138:[function(e,t,r){"use strict";e("./es6.regexp.flags");var n=e("./_an-object"),i=e("./_flags"),o=e("./_descriptors"),a="toString",s=/./[a],u=function(t){e("./_redefine")(RegExp.prototype,a,t,!0)};e("./_fails")(function(){return"/a/b"!=s.call({source:"a",flags:"b"})})?u(function(){var t=n(this);return"/".concat(t.source,"/","flags"in t?t.flags:!o&&t instanceof RegExp?i.call(t):void 0)}):s.name!=a&&u(function(){return s.call(this)})},{"./_an-object":21,"./_descriptors":35,"./_fails":41,"./_flags":43,"./_redefine":85,"./es6.regexp.flags":134}],139:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_string-context"),o="includes";n(n.P+n.F*t("./_fails-is-regexp")(o),"String",{includes:function(t){return!!~i(this,t,o).indexOf(t,1<arguments.length?arguments[1]:void 0)}})},{"./_export":39,"./_fails-is-regexp":40,"./_string-context":94}],140:[function(t,e,r){"use strict";var n=t("./_string-at")(!0);t("./_iter-define")(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,e=this._t,r=this._i;return r>=e.length?{value:void 0,done:!0}:(t=n(e,r),this._i+=t.length,{value:t,done:!1})})},{"./_iter-define":59,"./_string-at":93}],141:[function(t,e,r){"use strict";var n=t("./_global"),a=t("./_has"),i=t("./_descriptors"),o=t("./_export"),s=t("./_redefine"),u=t("./_meta").KEY,f=t("./_fails"),l=t("./_shared"),h=t("./_set-to-string-tag"),c=t("./_uid"),d=t("./_wks"),p=t("./_wks-ext"),m=t("./_wks-define"),b=t("./_enum-keys"),g=t("./_is-array"),_=t("./_an-object"),y=t("./_is-object"),v=t("./_to-iobject"),w=t("./_to-primitive"),E=t("./_property-desc"),x=t("./_object-create"),j=t("./_object-gopn-ext"),k=t("./_object-gopd"),S=t("./_object-dp"),I=t("./_object-keys"),A=k.f,T=S.f,O=j.f,P=n.Symbol,M=n.JSON,L=M&&M.stringify,C="prototype",R=d("_hidden"),B=d("toPrimitive"),N={}.propertyIsEnumerable,D=l("symbol-registry"),U=l("symbols"),F=l("op-symbols"),z=Object[C],H="function"==typeof P,G=n.QObject,W=!G||!G[C]||!G[C].findChild,Z=i&&f(function(){return 7!=x(T({},"a",{get:function(){return T(this,"a",{value:7}).a}})).a})?function(t,e,r){var n=A(z,e);n&&delete z[e],T(t,e,r),n&&t!==z&&T(z,e,n)}:T,q=function(t){var e=U[t]=x(P[C]);return e._k=t,e},Y=H&&"symbol"==typeof P.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof P},V=function(t,e,r){return t===z&&V(F,e,r),_(t),e=w(e,!0),_(r),a(U,e)?(r.enumerable?(a(t,R)&&t[R][e]&&(t[R][e]=!1),r=x(r,{enumerable:E(0,!1)})):(a(t,R)||T(t,R,E(1,{})),t[R][e]=!0),Z(t,e,r)):T(t,e,r)},X=function(t,e){_(t);for(var r,n=b(e=v(e)),i=0,o=n.length;i<o;)V(t,r=n[i++],e[r]);return t},J=function(t){var e=N.call(this,t=w(t,!0));return!(this===z&&a(U,t)&&!a(F,t))&&(!(e||!a(this,t)||!a(U,t)||a(this,R)&&this[R][t])||e)},K=function(t,e){if(t=v(t),e=w(e,!0),t!==z||!a(U,e)||a(F,e)){var r=A(t,e);return!r||!a(U,e)||a(t,R)&&t[R][e]||(r.enumerable=!0),r}},Q=function(t){for(var e,r=O(v(t)),n=[],i=0;r.length>i;)a(U,e=r[i++])||e==R||e==u||n.push(e);return n},$=function(t){for(var e,r=t===z,n=O(r?F:v(t)),i=[],o=0;n.length>o;)!a(U,e=n[o++])||r&&!a(z,e)||i.push(U[e]);return i};H||(s((P=function(){if(this instanceof P)throw TypeError("Symbol is not a constructor!");var e=c(0<arguments.length?arguments[0]:void 0),r=function(t){this===z&&r.call(F,t),a(this,R)&&a(this[R],e)&&(this[R][e]=!1),Z(this,e,E(1,t))};return i&&W&&Z(z,e,{configurable:!0,set:r}),q(e)})[C],"toString",function(){return this._k}),k.f=K,S.f=V,t("./_object-gopn").f=j.f=Q,t("./_object-pie").f=J,t("./_object-gops").f=$,i&&!t("./_library")&&s(z,"propertyIsEnumerable",J,!0),p.f=function(t){return q(d(t))}),o(o.G+o.W+o.F*!H,{Symbol:P});for(var tt="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),et=0;tt.length>et;)d(tt[et++]);for(var rt=I(d.store),nt=0;rt.length>nt;)m(rt[nt++]);o(o.S+o.F*!H,"Symbol",{for:function(t){return a(D,t+="")?D[t]:D[t]=P(t)},keyFor:function(t){if(!Y(t))throw TypeError(t+" is not a symbol!");for(var e in D)if(D[e]===t)return e},useSetter:function(){W=!0},useSimple:function(){W=!1}}),o(o.S+o.F*!H,"Object",{create:function(t,e){return void 0===e?x(t):X(x(t),e)},defineProperty:V,defineProperties:X,getOwnPropertyDescriptor:K,getOwnPropertyNames:Q,getOwnPropertySymbols:$}),M&&o(o.S+o.F*(!H||f(function(){var t=P();return"[null]"!=L([t])||"{}"!=L({a:t})||"{}"!=L(Object(t))})),"JSON",{stringify:function(t){for(var e,r,n=[t],i=1;arguments.length>i;)n.push(arguments[i++]);if(r=e=n[1],(y(e)||void 0!==t)&&!Y(t))return g(e)||(e=function(t,e){if("function"==typeof r&&(e=r.call(this,t,e)),!Y(e))return e}),n[1]=e,L.apply(M,n)}}),P[C][B]||t("./_hide")(P[C],B,P[C].valueOf),h(P,"Symbol"),h(Math,"Math",!0),h(n.JSON,"JSON",!0)},{"./_an-object":21,"./_descriptors":35,"./_enum-keys":38,"./_export":39,"./_fails":41,"./_global":45,"./_has":46,"./_hide":47,"./_is-array":54,"./_is-object":55,"./_library":63,"./_meta":64,"./_object-create":68,"./_object-dp":69,"./_object-gopd":71,"./_object-gopn":73,"./_object-gopn-ext":72,"./_object-gops":74,"./_object-keys":77,"./_object-pie":78,"./_property-desc":83,"./_redefine":85,"./_set-to-string-tag":88,"./_shared":90,"./_to-iobject":101,"./_to-primitive":104,"./_uid":108,"./_wks":112,"./_wks-define":110,"./_wks-ext":111}],142:[function(t,e,r){t("./_typed-array")("Float32",4,function(n){return function(t,e,r){return n(this,t,e,r)}})},{"./_typed-array":105}],143:[function(t,e,r){t("./_typed-array")("Float64",8,function(n){return function(t,e,r){return n(this,t,e,r)}})},{"./_typed-array":105}],144:[function(t,e,r){t("./_typed-array")("Uint8",1,function(n){return function(t,e,r){return n(this,t,e,r)}})},{"./_typed-array":105}],145:[function(t,e,r){t("./_typed-array")("Uint8",1,function(n){return function(t,e,r){return n(this,t,e,r)}},!0)},{"./_typed-array":105}],146:[function(t,e,r){"use strict";var n=t("./_export"),i=t("./_array-includes")(!0);n(n.P,"Array",{includes:function(t){return i(this,t,1<arguments.length?arguments[1]:void 0)}}),t("./_add-to-unscopables")("includes")},{"./_add-to-unscopables":19,"./_array-includes":24,"./_export":39}],147:[function(t,e,r){var n=t("./_export"),i=t("./_object-to-array")(!0);n(n.S,"Object",{entries:function(t){return i(t)}})},{"./_export":39,"./_object-to-array":80}],148:[function(t,e,r){t("./_wks-define")("asyncIterator")},{"./_wks-define":110}],149:[function(t,e,r){for(var n=t("./es6.array.iterator"),i=t("./_object-keys"),o=t("./_redefine"),a=t("./_global"),s=t("./_hide"),u=t("./_iterators"),f=t("./_wks"),l=f("iterator"),h=f("toStringTag"),c=u.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=i(d),m=0;m<p.length;m++){var b,g=p[m],_=d[g],y=a[g],v=y&&y.prototype;if(v&&(v[l]||s(v,l,c),v[h]||s(v,h,g),u[g]=c,_))for(b in n)v[b]||o(v,b,n[b],!0)}},{"./_global":45,"./_hide":47,"./_iterators":62,"./_object-keys":77,"./_redefine":85,"./_wks":112,"./es6.array.iterator":122}],150:[function(t,e,r){(function(t){function e(t){return Object.prototype.toString.call(t)}r.isArray=function(t){return Array.isArray?Array.isArray(t):"[object Array]"===e(t)},r.isBoolean=function(t){return"boolean"==typeof t},r.isNull=function(t){return null===t},r.isNullOrUndefined=function(t){return null==t},r.isNumber=function(t){return"number"==typeof t},r.isString=function(t){return"string"==typeof t},r.isSymbol=function(t){return"symbol"==typeof t},r.isUndefined=function(t){return void 0===t},r.isRegExp=function(t){return"[object RegExp]"===e(t)},r.isObject=function(t){return"object"==typeof t&&null!==t},r.isDate=function(t){return"[object Date]"===e(t)},r.isError=function(t){return"[object Error]"===e(t)||t instanceof Error},r.isFunction=function(t){return"function"==typeof t},r.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},r.isBuffer=t.isBuffer}).call(this,{isBuffer:t("../../is-buffer/index.js")})},{"../../is-buffer/index.js":167}],151:[function(t,e,r){var u=Object.create||function(t){var e=function(){};return e.prototype=t,new e},a=Object.keys||function(t){var e=[];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.push(r);return r},o=Function.prototype.bind||function(t){var e=this;return function(){return e.apply(t,arguments)}};function n(){this._events&&Object.prototype.hasOwnProperty.call(this,"_events")||(this._events=u(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0}((e.exports=n).EventEmitter=n).prototype._events=void 0,n.prototype._maxListeners=void 0;var i,s=10;try{var f={};Object.defineProperty&&Object.defineProperty(f,"x",{value:0}),i=0===f.x}catch(t){i=!1}function l(t){return void 0===t._maxListeners?n.defaultMaxListeners:t._maxListeners}function h(t,e,r,n){var i,o,a;if("function"!=typeof r)throw new TypeError('"listener" argument must be a function');if((o=t._events)?(o.newListener&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),a=o[e]):(o=t._events=u(null),t._eventsCount=0),a){if("function"==typeof a?a=o[e]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),!a.warned&&(i=l(t))&&0<i&&a.length>i){a.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+a.length+' "'+String(e)+'" listeners added. Use emitter.setMaxListeners() to increase limit.');s.name="MaxListenersExceededWarning",s.emitter=t,s.type=e,s.count=a.length,"object"==typeof console&&console.warn&&console.warn("%s: %s",s.name,s.message)}}else a=o[e]=r,++t._eventsCount;return t}function c(){if(!this.fired)switch(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length){case 0:return this.listener.call(this.target);case 1:return this.listener.call(this.target,arguments[0]);case 2:return this.listener.call(this.target,arguments[0],arguments[1]);case 3:return this.listener.call(this.target,arguments[0],arguments[1],arguments[2]);default:for(var t=new Array(arguments.length),e=0;e<t.length;++e)t[e]=arguments[e];this.listener.apply(this.target,t)}}function d(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=o.call(c,n);return i.listener=r,n.wrapFn=i}function p(t,e,r){var n=t._events;if(!n)return[];var i=n[e];return i?"function"==typeof i?r?[i.listener||i]:[i]:r?function(t){for(var e=new Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}(i):b(i,i.length):[]}function m(t){var e=this._events;if(e){var r=e[t];if("function"==typeof r)return 1;if(r)return r.length}return 0}function b(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t[n];return r}i?Object.defineProperty(n,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(t){if("number"!=typeof t||t<0||t!=t)throw new TypeError('"defaultMaxListeners" must be a positive number');s=t}}):n.defaultMaxListeners=s,n.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||isNaN(t))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=t,this},n.prototype.getMaxListeners=function(){return l(this)},n.prototype.emit=function(t){var e,r,n,i,o,a,s="error"===t;if(a=this._events)s=s&&null==a.error;else if(!s)return!1;if(s){if(1<arguments.length&&(e=arguments[1]),e instanceof Error)throw e;var u=new Error('Unhandled "error" event. ('+e+")");throw u.context=e,u}if(!(r=a[t]))return!1;var f="function"==typeof r;switch(n=arguments.length){case 1:!function(t,e,r){if(e)t.call(r);else for(var n=t.length,i=b(t,n),o=0;o<n;++o)i[o].call(r)}(r,f,this);break;case 2:!function(t,e,r,n){if(e)t.call(r,n);else for(var i=t.length,o=b(t,i),a=0;a<i;++a)o[a].call(r,n)}(r,f,this,arguments[1]);break;case 3:!function(t,e,r,n,i){if(e)t.call(r,n,i);else for(var o=t.length,a=b(t,o),s=0;s<o;++s)a[s].call(r,n,i)}(r,f,this,arguments[1],arguments[2]);break;case 4:!function(t,e,r,n,i,o){if(e)t.call(r,n,i,o);else for(var a=t.length,s=b(t,a),u=0;u<a;++u)s[u].call(r,n,i,o)}(r,f,this,arguments[1],arguments[2],arguments[3]);break;default:for(i=new Array(n-1),o=1;o<n;o++)i[o-1]=arguments[o];!function(t,e,r,n){if(e)t.apply(r,n);else for(var i=t.length,o=b(t,i),a=0;a<i;++a)o[a].apply(r,n)}(r,f,this,i)}return!0},n.prototype.on=n.prototype.addListener=function(t,e){return h(this,t,e,!1)},n.prototype.prependListener=function(t,e){return h(this,t,e,!0)},n.prototype.once=function(t,e){if("function"!=typeof e)throw new TypeError('"listener" argument must be a function');return this.on(t,d(this,t,e)),this},n.prototype.prependOnceListener=function(t,e){if("function"!=typeof e)throw new TypeError('"listener" argument must be a function');return this.prependListener(t,d(this,t,e)),this},n.prototype.removeListener=function(t,e){var r,n,i,o,a;if("function"!=typeof e)throw new TypeError('"listener" argument must be a function');if(!(n=this._events))return this;if(!(r=n[t]))return this;if(r===e||r.listener===e)0==--this._eventsCount?this._events=u(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,r.listener||e));else if("function"!=typeof r){for(i=-1,o=r.length-1;0<=o;o--)if(r[o]===e||r[o].listener===e){a=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(t,e){for(var r=e,n=r+1,i=t.length;n<i;r+=1,n+=1)t[r]=t[n];t.pop()}(r,i),1===r.length&&(n[t]=r[0]),n.removeListener&&this.emit("removeListener",t,a||e)}return this},n.prototype.removeAllListeners=function(t){var e,r,n;if(!(r=this._events))return this;if(!r.removeListener)return 0===arguments.length?(this._events=u(null),this._eventsCount=0):r[t]&&(0==--this._eventsCount?this._events=u(null):delete r[t]),this;if(0===arguments.length){var i,o=a(r);for(n=0;n<o.length;++n)"removeListener"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=u(null),this._eventsCount=0,this}if("function"==typeof(e=r[t]))this.removeListener(t,e);else if(e)for(n=e.length-1;0<=n;n--)this.removeListener(t,e[n]);return this},n.prototype.listeners=function(t){return p(this,t,!0)},n.prototype.rawListeners=function(t){return p(this,t,!1)},n.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):m.call(t,e)},n.prototype.listenerCount=m,n.prototype.eventNames=function(){return 0<this._eventsCount?Reflect.ownKeys(this._events):[]}},{}],152:[function(r7,s7,t7){var u7=r7("./lib/parser");s7.exports={create:function(w7,x7){if(w7 instanceof(x7=x7||eval("this")).ArrayBuffer){var y7=r7("./lib/dom-bufferstream");return new u7(new y7(w7,0,w7.byteLength,!0,x7))}var z7=r7("./lib/bufferstream");return new u7(new z7(w7,0,w7.length,!0))}}},{"./lib/bufferstream":153,"./lib/dom-bufferstream":155,"./lib/parser":159}],153:[function(t,e,r){function n(t,e,r,n){this.buffer=t,this.offset=e||0,r="number"==typeof r?r:t.length,this.endPosition=this.offset+r,this.setBigEndian(n)}n.prototype={setBigEndian:function(t){this.bigEndian=!!t},nextUInt8:function(){var t=this.buffer.readUInt8(this.offset);return this.offset+=1,t},nextInt8:function(){var t=this.buffer.readInt8(this.offset);return this.offset+=1,t},nextUInt16:function(){var t=this.bigEndian?this.buffer.readUInt16BE(this.offset):this.buffer.readUInt16LE(this.offset);return this.offset+=2,t},nextUInt32:function(){var t=this.bigEndian?this.buffer.readUInt32BE(this.offset):this.buffer.readUInt32LE(this.offset);return this.offset+=4,t},nextInt16:function(){var t=this.bigEndian?this.buffer.readInt16BE(this.offset):this.buffer.readInt16LE(this.offset);return this.offset+=2,t},nextInt32:function(){var t=this.bigEndian?this.buffer.readInt32BE(this.offset):this.buffer.readInt32LE(this.offset);return this.offset+=4,t},nextFloat:function(){var t=this.bigEndian?this.buffer.readFloatBE(this.offset):this.buffer.readFloatLE(this.offset);return this.offset+=4,t},nextDouble:function(){var t=this.bigEndian?this.buffer.readDoubleBE(this.offset):this.buffer.readDoubleLE(this.offset);return this.offset+=8,t},nextBuffer:function(t){var e=this.buffer.slice(this.offset,this.offset+t);return this.offset+=t,e},remainingLength:function(){return this.endPosition-this.offset},nextString:function(t){var e=this.buffer.toString("utf8",this.offset,this.offset+t);return this.offset+=t,e},mark:function(){var e=this;return{openWithOffset:function(t){return t=(t||0)+this.offset,new n(e.buffer,t,e.endPosition-t,e.bigEndian)},offset:this.offset}},offsetFrom:function(t){return this.offset-t.offset},skip:function(t){this.offset+=t},branch:function(t,e){return e="number"==typeof e?e:this.endPosition-(this.offset+t),new n(this.buffer,this.offset+t,e,this.bigEndian)}},e.exports=n},{}],154:[function(t,e,r){function u(t){return parseInt(t,10)}var a=3600,s=60;function f(t,e){t=t.map(u),e=e.map(u);var r=t[0],n=t[1]-1,i=t[2],o=e[0],a=e[1],s=e[2];return Date.UTC(r,n,i,o,a,s,0)/1e3}function n(t){var e=t.substr(0,10).split("-"),r=t.substr(11,8).split(":"),n=t.substr(19,6).split(":").map(u),i=n[0]*a+n[1]*s,o=f(e,r);if("number"==typeof(o-=i)&&!isNaN(o))return o}function i(t){var e=t.split(" "),r=f(e[0].split(":"),e[1].split(":"));if("number"==typeof r&&!isNaN(r))return r}e.exports={parseDateWithSpecFormat:i,parseDateWithTimezoneFormat:n,parseExifDate:function(t){var e=19===t.length&&":"===t.charAt(4);return 25===t.length&&"T"===t.charAt(10)?n(t):e?i(t):void 0}}},{}],155:[function(t,e,r){function n(t,e,r,n,i,o){this.global=i,e=e||0,r=r||t.byteLength-e,this.arrayBuffer=t.slice(e,e+r),this.view=new i.DataView(this.arrayBuffer,0,this.arrayBuffer.byteLength),this.setBigEndian(n),this.offset=0,this.parentOffset=(o||0)+e}n.prototype={setBigEndian:function(t){this.littleEndian=!t},nextUInt8:function(){var t=this.view.getUint8(this.offset);return this.offset+=1,t},nextInt8:function(){var t=this.view.getInt8(this.offset);return this.offset+=1,t},nextUInt16:function(){var t=this.view.getUint16(this.offset,this.littleEndian);return this.offset+=2,t},nextUInt32:function(){var t=this.view.getUint32(this.offset,this.littleEndian);return this.offset+=4,t},nextInt16:function(){var t=this.view.getInt16(this.offset,this.littleEndian);return this.offset+=2,t},nextInt32:function(){var t=this.view.getInt32(this.offset,this.littleEndian);return this.offset+=4,t},nextFloat:function(){var t=this.view.getFloat32(this.offset,this.littleEndian);return this.offset+=4,t},nextDouble:function(){var t=this.view.getFloat64(this.offset,this.littleEndian);return this.offset+=8,t},nextBuffer:function(t){var e=this.arrayBuffer.slice(this.offset,this.offset+t);return this.offset+=t,e},remainingLength:function(){return this.arrayBuffer.byteLength-this.offset},nextString:function(t){var e=this.arrayBuffer.slice(this.offset,this.offset+t);return e=String.fromCharCode.apply(null,new this.global.Uint8Array(e)),this.offset+=t,e},mark:function(){var e=this;return{openWithOffset:function(t){return t=(t||0)+this.offset,new n(e.arrayBuffer,t,e.arrayBuffer.byteLength-t,!e.littleEndian,e.global,e.parentOffset)},offset:this.offset,getParentOffset:function(){return e.parentOffset}}},offsetFrom:function(t){return this.parentOffset+this.offset-(t.offset+t.getParentOffset())},skip:function(t){this.offset+=t},branch:function(t,e){return e="number"==typeof e?e:this.arrayBuffer.byteLength-(this.offset+t),new n(this.arrayBuffer,this.offset+t,e,!this.littleEndian,this.global,this.parentOffset)}},e.exports=n},{}],156:[function(t,e,r){e.exports={exif:{1:"InteropIndex",2:"InteropVersion",11:"ProcessingSoftware",254:"SubfileType",255:"OldSubfileType",256:"ImageWidth",257:"ImageHeight",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",263:"Thresholding",264:"CellWidth",265:"CellLength",266:"FillOrder",269:"DocumentName",270:"ImageDescription",271:"Make",272:"Model",273:"StripOffsets",274:"Orientation",277:"SamplesPerPixel",278:"RowsPerStrip",279:"StripByteCounts",280:"MinSampleValue",281:"MaxSampleValue",282:"XResolution",283:"YResolution",284:"PlanarConfiguration",285:"PageName",286:"XPosition",287:"YPosition",288:"FreeOffsets",289:"FreeByteCounts",290:"GrayResponseUnit",291:"GrayResponseCurve",292:"T4Options",293:"T6Options",296:"ResolutionUnit",297:"PageNumber",300:"ColorResponseUnit",301:"TransferFunction",305:"Software",306:"ModifyDate",315:"Artist",316:"HostComputer",317:"Predictor",318:"WhitePoint",319:"PrimaryChromaticities",320:"ColorMap",321:"HalftoneHints",322:"TileWidth",323:"TileLength",324:"TileOffsets",325:"TileByteCounts",326:"BadFaxLines",327:"CleanFaxData",328:"ConsecutiveBadFaxLines",330:"SubIFD",332:"InkSet",333:"InkNames",334:"NumberofInks",336:"DotRange",337:"TargetPrinter",338:"ExtraSamples",339:"SampleFormat",340:"SMinSampleValue",341:"SMaxSampleValue",342:"TransferRange",343:"ClipPath",344:"XClipPathUnits",345:"YClipPathUnits",346:"Indexed",347:"JPEGTables",351:"OPIProxy",400:"GlobalParametersIFD",401:"ProfileType",402:"FaxProfile",403:"CodingMethods",404:"VersionYear",405:"ModeNumber",433:"Decode",434:"DefaultImageColor",435:"T82Options",437:"JPEGTables",512:"JPEGProc",513:"ThumbnailOffset",514:"ThumbnailLength",515:"JPEGRestartInterval",517:"JPEGLosslessPredictors",518:"JPEGPointTransforms",519:"JPEGQTables",520:"JPEGDCTables",521:"JPEGACTables",529:"YCbCrCoefficients",530:"YCbCrSubSampling",531:"YCbCrPositioning",532:"ReferenceBlackWhite",559:"StripRowCounts",700:"ApplicationNotes",999:"USPTOMiscellaneous",4096:"RelatedImageFileFormat",4097:"RelatedImageWidth",4098:"RelatedImageHeight",18246:"Rating",18247:"XP_DIP_XML",18248:"StitchInfo",18249:"RatingPercent",32781:"ImageID",32931:"WangTag1",32932:"WangAnnotation",32933:"WangTag3",32934:"WangTag4",32995:"Matteing",32996:"DataType",32997:"ImageDepth",32998:"TileDepth",33405:"Model2",33421:"CFARepeatPatternDim",33422:"CFAPattern2",33423:"BatteryLevel",33424:"KodakIFD",33432:"Copyright",33434:"ExposureTime",33437:"FNumber",33445:"MDFileTag",33446:"MDScalePixel",33447:"MDColorTable",33448:"MDLabName",33449:"MDSampleInfo",33450:"MDPrepDate",33451:"MDPrepTime",33452:"MDFileUnits",33550:"PixelScale",33589:"AdventScale",33590:"AdventRevision",33628:"UIC1Tag",33629:"UIC2Tag",33630:"UIC3Tag",33631:"UIC4Tag",33723:"IPTC-NAA",33918:"IntergraphPacketData",33919:"IntergraphFlagRegisters",33920:"IntergraphMatrix",33921:"INGRReserved",33922:"ModelTiePoint",34016:"Site",34017:"ColorSequence",34018:"IT8Header",34019:"RasterPadding",34020:"BitsPerRunLength",34021:"BitsPerExtendedRunLength",34022:"ColorTable",34023:"ImageColorIndicator",34024:"BackgroundColorIndicator",34025:"ImageColorValue",34026:"BackgroundColorValue",34027:"PixelIntensityRange",34028:"TransparencyIndicator",34029:"ColorCharacterization",34030:"HCUsage",34031:"TrapIndicator",34032:"CMYKEquivalent",34118:"SEMInfo",34152:"AFCP_IPTC",34232:"PixelMagicJBIGOptions",34264:"ModelTransform",34306:"WB_GRGBLevels",34310:"LeafData",34377:"PhotoshopSettings",34665:"ExifOffset",34675:"ICC_Profile",34687:"TIFF_FXExtensions",34688:"MultiProfiles",34689:"SharedData",34690:"T88Options",34732:"ImageLayer",34735:"GeoTiffDirectory",34736:"GeoTiffDoubleParams",34737:"GeoTiffAsciiParams",34850:"ExposureProgram",34852:"SpectralSensitivity",34853:"GPSInfo",34855:"ISO",34856:"Opto-ElectricConvFactor",34857:"Interlace",34858:"TimeZoneOffset",34859:"SelfTimerMode",34864:"SensitivityType",34865:"StandardOutputSensitivity",34866:"RecommendedExposureIndex",34867:"ISOSpeed",34868:"ISOSpeedLatitudeyyy",34869:"ISOSpeedLatitudezzz",34908:"FaxRecvParams",34909:"FaxSubAddress",34910:"FaxRecvTime",34954:"LeafSubIFD",36864:"ExifVersion",36867:"DateTimeOriginal",36868:"CreateDate",37121:"ComponentsConfiguration",37122:"CompressedBitsPerPixel",37377:"ShutterSpeedValue",37378:"ApertureValue",37379:"BrightnessValue",37380:"ExposureCompensation",37381:"MaxApertureValue",37382:"SubjectDistance",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",37387:"FlashEnergy",37388:"SpatialFrequencyResponse",37389:"Noise",37390:"FocalPlaneXResolution",37391:"FocalPlaneYResolution",37392:"FocalPlaneResolutionUnit",37393:"ImageNumber",37394:"SecurityClassification",37395:"ImageHistory",37396:"SubjectArea",37397:"ExposureIndex",37398:"TIFF-EPStandardID",37399:"SensingMethod",37434:"CIP3DataFile",37435:"CIP3Sheet",37436:"CIP3Side",37439:"StoNits",37500:"MakerNote",37510:"UserComment",37520:"SubSecTime",37521:"SubSecTimeOriginal",37522:"SubSecTimeDigitized",37679:"MSDocumentText",37680:"MSPropertySetStorage",37681:"MSDocumentTextPosition",37724:"ImageSourceData",40091:"XPTitle",40092:"XPComment",40093:"XPAuthor",40094:"XPKeywords",40095:"XPSubject",40960:"FlashpixVersion",40961:"ColorSpace",40962:"ExifImageWidth",40963:"ExifImageHeight",40964:"RelatedSoundFile",40965:"InteropOffset",41483:"FlashEnergy",41484:"SpatialFrequencyResponse",41485:"Noise",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41489:"ImageNumber",41490:"SecurityClassification",41491:"ImageHistory",41492:"SubjectLocation",41493:"ExposureIndex",41494:"TIFF-EPStandardID",41495:"SensingMethod",41728:"FileSource",41729:"SceneType",41730:"CFAPattern",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41988:"DigitalZoomRatio",41989:"FocalLengthIn35mmFormat",41990:"SceneCaptureType",41991:"GainControl",41992:"Contrast",41993:"Saturation",41994:"Sharpness",41995:"DeviceSettingDescription",41996:"SubjectDistanceRange",42016:"ImageUniqueID",42032:"OwnerName",42033:"SerialNumber",42034:"LensInfo",42035:"LensMake",42036:"LensModel",42037:"LensSerialNumber",42112:"GDALMetadata",42113:"GDALNoData",42240:"Gamma",44992:"ExpandSoftware",44993:"ExpandLens",44994:"ExpandFilm",44995:"ExpandFilterLens",44996:"ExpandScanner",44997:"ExpandFlashLamp",48129:"PixelFormat",48130:"Transformation",48131:"Uncompressed",48132:"ImageType",48256:"ImageWidth",48257:"ImageHeight",48258:"WidthResolution",48259:"HeightResolution",48320:"ImageOffset",48321:"ImageByteCount",48322:"AlphaOffset",48323:"AlphaByteCount",48324:"ImageDataDiscard",48325:"AlphaDataDiscard",50215:"OceScanjobDesc",50216:"OceApplicationSelector",50217:"OceIDNumber",50218:"OceImageLogic",50255:"Annotations",50341:"PrintIM",50560:"USPTOOriginalContentType",50706:"DNGVersion",50707:"DNGBackwardVersion",50708:"UniqueCameraModel",50709:"LocalizedCameraModel",50710:"CFAPlaneColor",50711:"CFALayout",50712:"LinearizationTable",50713:"BlackLevelRepeatDim",50714:"BlackLevel",50715:"BlackLevelDeltaH",50716:"BlackLevelDeltaV",50717:"WhiteLevel",50718:"DefaultScale",50719:"DefaultCropOrigin",50720:"DefaultCropSize",50721:"ColorMatrix1",50722:"ColorMatrix2",50723:"CameraCalibration1",50724:"CameraCalibration2",50725:"ReductionMatrix1",50726:"ReductionMatrix2",50727:"AnalogBalance",50728:"AsShotNeutral",50729:"AsShotWhiteXY",50730:"BaselineExposure",50731:"BaselineNoise",50732:"BaselineSharpness",50733:"BayerGreenSplit",50734:"LinearResponseLimit",50735:"CameraSerialNumber",50736:"DNGLensInfo",50737:"ChromaBlurRadius",50738:"AntiAliasStrength",50739:"ShadowScale",50740:"DNGPrivateData",50741:"MakerNoteSafety",50752:"RawImageSegmentation",50778:"CalibrationIlluminant1",50779:"CalibrationIlluminant2",50780:"BestQualityScale",50781:"RawDataUniqueID",50784:"AliasLayerMetadata",50827:"OriginalRawFileName",50828:"OriginalRawFileData",50829:"ActiveArea",50830:"MaskedAreas",50831:"AsShotICCProfile",50832:"AsShotPreProfileMatrix",50833:"CurrentICCProfile",50834:"CurrentPreProfileMatrix",50879:"ColorimetricReference",50898:"PanasonicTitle",50899:"PanasonicTitle2",50931:"CameraCalibrationSig",50932:"ProfileCalibrationSig",50933:"ProfileIFD",50934:"AsShotProfileName",50935:"NoiseReductionApplied",50936:"ProfileName",50937:"ProfileHueSatMapDims",50938:"ProfileHueSatMapData1",50939:"ProfileHueSatMapData2",50940:"ProfileToneCurve",50941:"ProfileEmbedPolicy",50942:"ProfileCopyright",50964:"ForwardMatrix1",50965:"ForwardMatrix2",50966:"PreviewApplicationName",50967:"PreviewApplicationVersion",50968:"PreviewSettingsName",50969:"PreviewSettingsDigest",50970:"PreviewColorSpace",50971:"PreviewDateTime",50972:"RawImageDigest",50973:"OriginalRawFileDigest",50974:"SubTileBlockSize",50975:"RowInterleaveFactor",50981:"ProfileLookTableDims",50982:"ProfileLookTableData",51008:"OpcodeList1",51009:"OpcodeList2",51022:"OpcodeList3",51041:"NoiseProfile",51043:"TimeCodes",51044:"FrameRate",51058:"TStop",51081:"ReelName",51089:"OriginalDefaultFinalSize",51090:"OriginalBestQualitySize",51091:"OriginalDefaultCropSize",51105:"CameraLabel",51107:"ProfileHueSatMapEncoding",51108:"ProfileLookTableEncoding",51109:"BaselineExposureOffset",51110:"DefaultBlackRender",51111:"NewRawImageDigest",51112:"RawToPreviewGain",51125:"DefaultUserCrop",59932:"Padding",59933:"OffsetSchema",65e3:"OwnerName",65001:"SerialNumber",65002:"Lens",65024:"KDC_IFD",65100:"RawFile",65101:"Converter",65102:"WhiteBalance",65105:"Exposure",65106:"Shadows",65107:"Brightness",65108:"Contrast",65109:"Saturation",65110:"Sharpness",65111:"Smoothness",65112:"MoireFilter"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude",5:"GPSAltitudeRef",6:"GPSAltitude",7:"GPSTimeStamp",8:"GPSSatellites",9:"GPSStatus",10:"GPSMeasureMode",11:"GPSDOP",12:"GPSSpeedRef",13:"GPSSpeed",14:"GPSTrackRef",15:"GPSTrack",16:"GPSImgDirectionRef",17:"GPSImgDirection",18:"GPSMapDatum",19:"GPSDestLatitudeRef",20:"GPSDestLatitude",21:"GPSDestLongitudeRef",22:"GPSDestLongitude",23:"GPSDestBearingRef",24:"GPSDestBearing",25:"GPSDestDistanceRef",26:"GPSDestDistance",27:"GPSProcessingMethod",28:"GPSAreaInformation",29:"GPSDateStamp",30:"GPSDifferential",31:"GPSHPositioningError"}}},{}],157:[function(t,e,r){function l(t,e){switch(t){case 1:return e.nextUInt8();case 3:return e.nextUInt16();case 4:return e.nextUInt32();case 5:return[e.nextUInt32(),e.nextUInt32()];case 6:return e.nextInt8();case 8:return e.nextUInt16();case 9:return e.nextUInt32();case 10:return[e.nextInt32(),e.nextInt32()];case 11:return e.nextFloat();case 12:return e.nextDouble();default:throw new Error("Invalid format while decoding: "+t)}}function a(t,e){var r,n,i=e.nextUInt16(),o=e.nextUInt16(),a=function(t){switch(t){case 1:case 2:case 6:case 7:return 1;case 3:case 8:return 2;case 4:case 9:case 11:return 4;case 5:case 10:case 12:return 8;default:return 0}}(o),s=e.nextUInt32(),u=a*s;if(4<u&&(e=t.openWithOffset(e.nextUInt32())),2===o){var f=(r=e.nextString(s)).indexOf("\0");-1!==f&&(r=r.substr(0,f))}else if(7===o)r=e.nextBuffer(s);else if(0!==o)for(r=[],n=0;n<s;++n)r.push(l(o,e));return u<4&&e.skip(4-u),[i,r,o]}function h(t,e,r){var n,i,o=e.nextUInt16();for(i=0;i<o;++i)r((n=a(t,e))[0],n[1],n[2])}e.exports={IFD0:1,IFD1:2,GPSIFD:3,SubIFD:4,InteropIFD:5,parseTags:function(t,n){var e,i,o,a;try{e=function(t){if("Exif\0\0"!==t.nextString(6))throw new Error("Invalid EXIF header");var e=t.mark(),r=t.nextUInt16();if(18761===r)t.setBigEndian(!1);else{if(19789!==r)throw new Error("Invalid TIFF header");t.setBigEndian(!0)}if(42!==t.nextUInt16())throw new Error("Invalid TIFF data");return e}(t)}catch(t){return!1}var r=e.openWithOffset(t.nextUInt32()),s=this.IFD0;h(e,r,function(t,e,r){switch(t){case 34853:o=e[0];break;case 34665:i=e[0];break;default:n(s,t,e,r)}});var u=r.nextUInt32();0!==u&&h(e,e.openWithOffset(u),n.bind(null,this.IFD1));o&&h(e,e.openWithOffset(o),n.bind(null,this.GPSIFD));if(i){var f=e.openWithOffset(i),l=this.InteropIFD;h(e,f,function(t,e,r){40965===t?a=e[0]:n(l,t,e,r)})}a&&h(e,e.openWithOffset(a),n.bind(null,this.InteropIFD));return!0}}},{}],158:[function(t,e,r){e.exports={parseSections:function(t,e){var r,n;for(t.setBigEndian(!0);0<t.remainingLength()&&218!==n;){if(255!==t.nextUInt8())throw new Error("Invalid JPEG section offset");r=208<=(n=t.nextUInt8())&&n<=217||218===n?0:t.nextUInt16()-2,e(n,t.branch(0,r)),t.skip(r)}},getSizeFromSOFSection:function(t){return t.skip(1),{height:t.nextUInt16(),width:t.nextUInt16()}},getSectionName:function(t){var e,r;switch(t){case 216:e="SOI";break;case 196:e="DHT";break;case 219:e="DQT";break;case 221:e="DRI";break;case 218:e="SOS";break;case 254:e="COM";break;case 217:e="EOI";break;default:224<=t&&t<=239?(e="APP",r=t-224):192<=t&&t<=207&&196!==t&&200!==t&&204!==t?(e="SOF",r=t-192):208<=t&&t<=215&&(e="RST",r=t-208)}var n={name:e};return"number"==typeof r&&(n.index=r),n}}},{}],159:[function(c,t,e){var d=c("./jpeg"),p=c("./exif"),m=c("./simplify");function b(t,e,r,n,i,o,a){this.startMarker=t,this.tags=e,this.imageSize=r,this.thumbnailOffset=n,this.thumbnailLength=i,this.thumbnailType=o,this.app1Offset=a}function r(t){this.stream=t,this.flags={readBinaryTags:!1,resolveTagNames:!0,simplifyValues:!0,imageSize:!0,hidePointers:!0,returnTags:!0}}b.prototype={hasThumbnail:function(t){return!(!this.thumbnailOffset||!this.thumbnailLength)&&("string"!=typeof t||("image/jpeg"===t.toLowerCase().trim()?6===this.thumbnailType:"image/tiff"===t.toLowerCase().trim()&&1===this.thumbnailType))},getThumbnailOffset:function(){return this.app1Offset+6+this.thumbnailOffset},getThumbnailLength:function(){return this.thumbnailLength},getThumbnailBuffer:function(){return this._getThumbnailStream().nextBuffer(this.thumbnailLength)},_getThumbnailStream:function(){return this.startMarker.openWithOffset(this.getThumbnailOffset())},getImageSize:function(){return this.imageSize},getThumbnailSize:function(){var r,t=this._getThumbnailStream();return d.parseSections(t,function(t,e){"SOF"===d.getSectionName(t).name&&(r=d.getSizeFromSOFSection(e))}),r}},r.prototype={enableBinaryFields:function(t){return this.flags.readBinaryTags=!!t,this},enablePointers:function(t){return this.flags.hidePointers=!t,this},enableTagNames:function(t){return this.flags.resolveTagNames=!!t,this},enableImageSize:function(t){return this.flags.imageSize=!!t,this},enableReturnTags:function(t){return this.flags.returnTags=!!t,this},enableSimpleValues:function(t){return this.flags.simplifyValues=!!t,this},parse:function(){var o,n,a,s,u,i,f,t,e,l=this.stream.mark(),r=l.openWithOffset(0),h=this.flags;return h.resolveTagNames&&(f=c("./exif-tags")),e=h.resolveTagNames?(o={},t=function(t){return o[t.name]},function(t,e){o[t.name]=e}):(o=[],t=function(t){var e;for(e=0;e<o.length;++e)if(o[e].type===t.type&&o[e].section===t.section)return o.value},function(t,e){var r;for(r=0;r<o.length;++r)if(o[r].type===t.type&&o[r].section===t.section)return void(o.value=e)}),d.parseSections(r,function(t,e){var r=e.offsetFrom(l);225===t?p.parseTags(e,function(t,e,r,n){if(h.readBinaryTags||7!==n){if(513===e){if(a=r[0],h.hidePointers)return}else if(514===e){if(s=r[0],h.hidePointers)return}else if(259===e&&(u=r[0],h.hidePointers))return;if(h.returnTags)if(h.simplifyValues&&(r=m.simplifyValue(r,n)),h.resolveTagNames){var i=(t===p.GPSIFD?f.gps:f.exif)[e];i||(i=f.exif[e]),o.hasOwnProperty(i)||(o[i]=r)}else o.push({section:t,type:e,value:r})}})&&(i=r):h.imageSize&&"SOF"===d.getSectionName(t).name&&(n=d.getSizeFromSOFSection(e))}),h.simplifyValues&&(m.castDegreeValues(t,e),m.castDateValues(t,e)),new b(l,o,n,a,s,u,i)}},t.exports=r},{"./exif":157,"./exif-tags":156,"./jpeg":158,"./simplify":160}],160:[function(t,e,r){var n=t("./exif"),o=t("./date"),a=[{section:n.GPSIFD,type:2,name:"GPSLatitude",refType:1,refName:"GPSLatitudeRef",posVal:"N"},{section:n.GPSIFD,type:4,name:"GPSLongitude",refType:3,refName:"GPSLongitudeRef",posVal:"E"}],s=[{section:n.SubIFD,type:306,name:"ModifyDate"},{section:n.SubIFD,type:36867,name:"DateTimeOriginal"},{section:n.SubIFD,type:36868,name:"CreateDate"},{section:n.SubIFD,type:306,name:"ModifyDate"}];e.exports={castDegreeValues:function(i,o){a.forEach(function(t){var e=i(t);if(e){var r=i({section:t.section,type:t.refType,name:t.refName})===t.posVal?1:-1,n=(e[0]+e[1]/60+e[2]/3600)*r;o(t,n)}})},castDateValues:function(n,i){s.forEach(function(t){var e=n(t);if(e){var r=o.parseExifDate(e);void 0!==r&&i(t,r)}})},simplifyValue:function(t,e){return Array.isArray(t)&&1===(t=t.map(function(t){return 10===e||5===e?t[0]/t[1]:t})).length&&(t=t[0]),t}}},{"./date":154,"./exif":157}],161:[function(t,e,r){"use strict";function p(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var m=function(t){return p(t).map(function(t){return t.charCodeAt(0)})},b=m("META-INF/mozilla.rsa"),g=m("[Content_Types].xml"),_=m("_rels/.rels");e.exports=function(t){var n=t instanceof Uint8Array?t:new Uint8Array(t);if(!(n&&1<n.length))return null;var r=function(t,e){e=Object.assign({offset:0},e);for(var r=0;r<t.length;r++)if(e.mask){if(t[r]!==(e.mask[r]&n[r+e.offset]))return!1}else if(t[r]!==n[r+e.offset])return!1;return!0},e=function(t,e){return r(m(t),e)};if(r([255,216,255]))return{ext:"jpg",mime:"image/jpeg"};if(r([137,80,78,71,13,10,26,10]))return{ext:"png",mime:"image/png"};if(r([71,73,70]))return{ext:"gif",mime:"image/gif"};if(r([87,69,66,80],{offset:8}))return{ext:"webp",mime:"image/webp"};if(r([70,76,73,70]))return{ext:"flif",mime:"image/flif"};if((r([73,73,42,0])||r([77,77,0,42]))&&r([67,82],{offset:8}))return{ext:"cr2",mime:"image/x-canon-cr2"};if(r([73,73,42,0])||r([77,77,0,42]))return{ext:"tif",mime:"image/tiff"};if(r([66,77]))return{ext:"bmp",mime:"image/bmp"};if(r([73,73,188]))return{ext:"jxr",mime:"image/vnd.ms-photo"};if(r([56,66,80,83]))return{ext:"psd",mime:"image/vnd.adobe.photoshop"};if(r([80,75,3,4])){if(r([109,105,109,101,116,121,112,101,97,112,112,108,105,99,97,116,105,111,110,47,101,112,117,98,43,122,105,112],{offset:30}))return{ext:"epub",mime:"application/epub+zip"};if(r(b,{offset:30}))return{ext:"xpi",mime:"application/x-xpinstall"};if(e("mimetypeapplication/vnd.oasis.opendocument.text",{offset:30}))return{ext:"odt",mime:"application/vnd.oasis.opendocument.text"};if(e("mimetypeapplication/vnd.oasis.opendocument.spreadsheet",{offset:30}))return{ext:"ods",mime:"application/vnd.oasis.opendocument.spreadsheet"};if(e("mimetypeapplication/vnd.oasis.opendocument.presentation",{offset:30}))return{ext:"odp",mime:"application/vnd.oasis.opendocument.presentation"};var i=function(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return t.findIndex(function(t,e,r){return n<=e&&80===r[e]&&75===r[e+1]&&3===r[e+2]&&4===r[e+3]})},o=0,a=!1,s=null;do{var u=o+30;if(a||(a=r(g,{offset:u})||r(_,{offset:u})),s||(e("word/",{offset:u})?s={ext:"docx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}:e("ppt/",{offset:u})?s={ext:"pptx",mime:"application/vnd.openxmlformats-officedocument.presentationml.presentation"}:e("xl/",{offset:u})&&(s={ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})),a&&s)return s;o=i(n,u)}while(0<=o);if(s)return s}if(r([80,75])&&(3===n[2]||5===n[2]||7===n[2])&&(4===n[3]||6===n[3]||8===n[3]))return{ext:"zip",mime:"application/zip"};if(r([117,115,116,97,114],{offset:257}))return{ext:"tar",mime:"application/x-tar"};if(r([82,97,114,33,26,7])&&(0===n[6]||1===n[6]))return{ext:"rar",mime:"application/x-rar-compressed"};if(r([31,139,8]))return{ext:"gz",mime:"application/gzip"};if(r([66,90,104]))return{ext:"bz2",mime:"application/x-bzip2"};if(r([55,122,188,175,39,28]))return{ext:"7z",mime:"application/x-7z-compressed"};if(r([120,1]))return{ext:"dmg",mime:"application/x-apple-diskimage"};if(r([51,103,112,53])||r([0,0,0])&&r([102,116,121,112],{offset:4})&&(r([109,112,52,49],{offset:8})||r([109,112,52,50],{offset:8})||r([105,115,111,109],{offset:8})||r([105,115,111,50],{offset:8})||r([109,109,112,52],{offset:8})||r([77,52,86],{offset:8})||r([100,97,115,104],{offset:8})))return{ext:"mp4",mime:"video/mp4"};if(r([77,84,104,100]))return{ext:"mid",mime:"audio/midi"};if(r([26,69,223,163])){var f=n.subarray(4,4100),l=f.findIndex(function(t,e,r){return 66===r[e]&&130===r[e+1]});if(-1!==l){var h=l+3,c=function(t){return p(t).every(function(t,e){return f[h+e]===t.charCodeAt(0)})};if(c("matroska"))return{ext:"mkv",mime:"video/x-matroska"};if(c("webm"))return{ext:"webm",mime:"video/webm"}}}if(r([0,0,0,20,102,116,121,112,113,116,32,32])||r([102,114,101,101],{offset:4})||r([102,116,121,112,113,116,32,32],{offset:4})||r([109,100,97,116],{offset:4})||r([119,105,100,101],{offset:4}))return{ext:"mov",mime:"video/quicktime"};if(r([82,73,70,70])){if(r([65,86,73],{offset:8}))return{ext:"avi",mime:"video/vnd.avi"};if(r([87,65,86,69],{offset:8}))return{ext:"wav",mime:"audio/vnd.wave"};if(r([81,76,67,77],{offset:8}))return{ext:"qcp",mime:"audio/qcelp"}}if(r([48,38,178,117,142,102,207,17,166,217]))return{ext:"wmv",mime:"video/x-ms-wmv"};if(r([0,0,1,186])||r([0,0,1,179]))return{ext:"mpg",mime:"video/mpeg"};if(r([102,116,121,112,51,103],{offset:4}))return{ext:"3gp",mime:"video/3gpp"};for(var d=0;d<2&&d<n.length-16;d++){if(r([73,68,51],{offset:d})||r([255,226],{offset:d,mask:[255,226]}))return{ext:"mp3",mime:"audio/mpeg"};if(r([255,228],{offset:d,mask:[255,228]}))return{ext:"mp2",mime:"audio/mpeg"};if(r([255,248],{offset:d,mask:[255,252]}))return{ext:"mp2",mime:"audio/mpeg"};if(r([255,240],{offset:d,mask:[255,252]}))return{ext:"mp4",mime:"audio/mpeg"}}if(r([102,116,121,112,77,52,65],{offset:4})||r([77,52,65,32]))return{ext:"m4a",mime:"audio/mp4"};if(r([79,112,117,115,72,101,97,100],{offset:28}))return{ext:"opus",mime:"audio/opus"};if(r([79,103,103,83]))return r([128,116,104,101,111,114,97],{offset:28})?{ext:"ogv",mime:"video/ogg"}:r([1,118,105,100,101,111,0],{offset:28})?{ext:"ogm",mime:"video/ogg"}:r([127,70,76,65,67],{offset:28})?{ext:"oga",mime:"audio/ogg"}:r([83,112,101,101,120,32,32],{offset:28})?{ext:"spx",mime:"audio/ogg"}:r([1,118,111,114,98,105,115],{offset:28})?{ext:"ogg",mime:"audio/ogg"}:{ext:"ogx",mime:"application/ogg"};if(r([102,76,97,67]))return{ext:"flac",mime:"audio/x-flac"};if(r([77,65,67,32]))return{ext:"ape",mime:"audio/ape"};if(r([119,118,112,107]))return{ext:"wv",mime:"audio/wavpack"};if(r([35,33,65,77,82,10]))return{ext:"amr",mime:"audio/amr"};if(r([37,80,68,70]))return{ext:"pdf",mime:"application/pdf"};if(r([77,90]))return{ext:"exe",mime:"application/x-msdownload"};if((67===n[0]||70===n[0])&&r([87,83],{offset:1}))return{ext:"swf",mime:"application/x-shockwave-flash"};if(r([123,92,114,116,102]))return{ext:"rtf",mime:"application/rtf"};if(r([0,97,115,109]))return{ext:"wasm",mime:"application/wasm"};if(r([119,79,70,70])&&(r([0,1,0,0],{offset:4})||r([79,84,84,79],{offset:4})))return{ext:"woff",mime:"font/woff"};if(r([119,79,70,50])&&(r([0,1,0,0],{offset:4})||r([79,84,84,79],{offset:4})))return{ext:"woff2",mime:"font/woff2"};if(r([76,80],{offset:34})&&(r([0,0,1],{offset:8})||r([1,0,2],{offset:8})||r([2,0,2],{offset:8})))return{ext:"eot",mime:"application/vnd.ms-fontobject"};if(r([0,1,0,0,0]))return{ext:"ttf",mime:"font/ttf"};if(r([79,84,84,79,0]))return{ext:"otf",mime:"font/otf"};if(r([0,0,1,0]))return{ext:"ico",mime:"image/x-icon"};if(r([0,0,2,0]))return{ext:"cur",mime:"image/x-icon"};if(r([70,76,86,1]))return{ext:"flv",mime:"video/x-flv"};if(r([37,33]))return{ext:"ps",mime:"application/postscript"};if(r([253,55,122,88,90,0]))return{ext:"xz",mime:"application/x-xz"};if(r([83,81,76,105]))return{ext:"sqlite",mime:"application/x-sqlite3"};if(r([78,69,83,26]))return{ext:"nes",mime:"application/x-nintendo-nes-rom"};if(r([67,114,50,52]))return{ext:"crx",mime:"application/x-google-chrome-extension"};if(r([77,83,67,70])||r([73,83,99,40]))return{ext:"cab",mime:"application/vnd.ms-cab-compressed"};if(r([33,60,97,114,99,104,62,10,100,101,98,105,97,110,45,98,105,110,97,114,121]))return{ext:"deb",mime:"application/x-deb"};if(r([33,60,97,114,99,104,62]))return{ext:"ar",mime:"application/x-unix-archive"};if(r([237,171,238,219]))return{ext:"rpm",mime:"application/x-rpm"};if(r([31,160])||r([31,157]))return{ext:"Z",mime:"application/x-compress"};if(r([76,90,73,80]))return{ext:"lz",mime:"application/x-lzip"};if(r([208,207,17,224,161,177,26,225]))return{ext:"msi",mime:"application/x-msi"};if(r([6,14,43,52,2,5,1,1,13,1,2,1,1,2]))return{ext:"mxf",mime:"application/mxf"};if(r([71],{offset:4})&&(r([71],{offset:192})||r([71],{offset:196})))return{ext:"mts",mime:"video/mp2t"};if(r([66,76,69,78,68,69,82]))return{ext:"blend",mime:"application/x-blender"};if(r([66,80,71,251]))return{ext:"bpg",mime:"image/bpg"};if(r([0,0,0,12,106,80,32,32,13,10,135,10])){if(r([106,112,50,32],{offset:20}))return{ext:"jp2",mime:"image/jp2"};if(r([106,112,120,32],{offset:20}))return{ext:"jpx",mime:"image/jpx"};if(r([106,112,109,32],{offset:20}))return{ext:"jpm",mime:"image/jpm"};if(r([109,106,112,50],{offset:20}))return{ext:"mj2",mime:"image/mj2"}}if(r([70,79,82,77,0]))return{ext:"aif",mime:"audio/aiff"};if(e("<?xml "))return{ext:"xml",mime:"application/xml"};if(r([66,79,79,75,77,79,66,73],{offset:60}))return{ext:"mobi",mime:"application/x-mobipocket-ebook"};if(r([102,116,121,112],{offset:4})){if(r([109,105,102,49],{offset:8}))return{ext:"heic",mime:"image/heif"};if(r([109,115,102,49],{offset:8}))return{ext:"heic",mime:"image/heif-sequence"};if(r([104,101,105,99],{offset:8})||r([104,101,105,120],{offset:8}))return{ext:"heic",mime:"image/heic"};if(r([104,101,118,99],{offset:8})||r([104,101,118,120],{offset:8}))return{ext:"heic",mime:"image/heic-sequence"}}return r([171,75,84,88,32,49,49,187,13,10,26,10])?{ext:"ktx",mime:"image/ktx"}:null}},{}],162:[function(t,e,r){"use strict";var i=t("is-callable"),o=Object.prototype.toString,a=Object.prototype.hasOwnProperty;e.exports=function(t,e,r){if(!i(e))throw new TypeError("iterator must be a function");var n;3<=arguments.length&&(n=r),"[object Array]"===o.call(t)?function(t,e,r){for(var n=0,i=t.length;n<i;n++)a.call(t,n)&&(null==r?e(t[n],n,t):e.call(r,t[n],n,t))}(t,e,n):"string"==typeof t?function(t,e,r){for(var n=0,i=t.length;n<i;n++)null==r?e(t.charAt(n),n,t):e.call(r,t.charAt(n),n,t)}(t,e,n):function(t,e,r){for(var n in t)a.call(t,n)&&(null==r?e(t[n],n,t):e.call(r,t[n],n,t))}(t,e,n)}},{"is-callable":168}],163:[function(t,r,e){(function(t){var e;e=void 0!==window?window:void 0!==t?t:"undefined"!=typeof self?self:{},r.exports=e}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{}],164:[function(t,e,r){var n=t("http"),i=t("url"),o=e.exports;for(var a in n)n.hasOwnProperty(a)&&(o[a]=n[a]);function s(t){if("string"==typeof t&&(t=i.parse(t)),t.protocol||(t.protocol="https:"),"https:"!==t.protocol)throw new Error('Protocol "'+t.protocol+'" not supported. Expected "https:"');return t}o.request=function(t,e){return t=s(t),n.request.call(this,t,e)},o.get=function(t,e){return t=s(t),n.get.call(this,t,e)}},{http:243,url:253}],165:[function(t,e,r){r.read=function(t,e,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,f=u>>1,l=-7,h=r?i-1:0,c=r?-1:1,d=t[e+h];for(h+=c,o=d&(1<<-l)-1,d>>=-l,l+=s;0<l;o=256*o+t[e+h],h+=c,l-=8);for(a=o&(1<<-l)-1,o>>=-l,l+=n;0<l;a=256*a+t[e+h],h+=c,l-=8);if(0===o)o=1-f;else{if(o===u)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),o-=f}return(d?-1:1)*a*Math.pow(2,o-n)},r.write=function(t,e,r,n,i,o){var a,s,u,f=8*o-i-1,l=(1<<f)-1,h=l>>1,c=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,a=l):(a=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-a))<1&&(a--,u*=2),2<=(e+=1<=a+h?c/u:c*Math.pow(2,1-h))*u&&(a++,u/=2),l<=a+h?(s=0,a=l):1<=a+h?(s=(e*u-1)*Math.pow(2,i),a+=h):(s=e*Math.pow(2,h-1)*Math.pow(2,i),a=0));8<=i;t[r+d]=255&s,d+=p,s/=256,i-=8);for(a=a<<i|s,f+=i;0<f;t[r+d]=255&a,d+=p,a/=256,f-=8);t[r+d-p]|=128*m}},{}],166:[function(t,e,r){arguments[4][4][0].apply(r,arguments)},{dup:4}],167:[function(t,e,r){function n(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}e.exports=function(t){return null!=t&&(n(t)||"function"==typeof(e=t).readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))||!!t._isBuffer);var e}},{}],168:[function(t,e,r){"use strict";var n=Function.prototype.toString,i=/^\s*class\b/,o=function(t){try{var e=n.call(t);return i.test(e)}catch(t){return!1}},a=Object.prototype.toString,s="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;e.exports=function(t){if(!t)return!1;if("function"!=typeof t&&"object"!=typeof t)return!1;if("function"==typeof t&&!t.prototype)return!0;if(s)return function(t){try{return!o(t)&&(n.call(t),!0)}catch(t){return!1}}(t);if(o(t))return!1;var e=a.call(t);return"[object Function]"===e||"[object GeneratorFunction]"===e}},{}],169:[function(t,e,r){e.exports=function(t){var e=n.call(t);return"[object Function]"===e||"function"==typeof t&&"[object RegExp]"!==e||void 0!==window&&(t===window.setTimeout||t===window.alert||t===window.confirm||t===window.prompt)};var n=Object.prototype.toString},{}],170:[function(t,e,r){var n={}.toString;e.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},{}],171:[function(t,e,r){var n=t("./lib/encoder"),i=t("./lib/decoder");e.exports={encode:n,decode:i}},{"./lib/decoder":172,"./lib/encoder":173}],172:[function(t,e,r){(function(o){var a=function(){"use strict";var q=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),y=4017,v=799,w=3406,E=2276,x=1567,j=3784,k=5793,S=2896;function t(){}function F(t,e){for(var r,n,i=0,o=[],a=16;0<a&&!t[a-1];)a--;o.push({children:[],index:0});var s,u=o[0];for(r=0;r<a;r++){for(n=0;n<t[r];n++){for((u=o.pop()).children[u.index]=e[i];0<u.index;)u=o.pop();for(u.index++,o.push(u);o.length<=r;)o.push(s={children:[],index:0}),u.children[u.index]=s.children,u=s;i++}r+1<a&&(o.push(s={children:[],index:0}),u.children[u.index]=s.children,u=s)}return o[0].children}function z(e,r,t,n,i,f,l,o,h){t.precision,t.samplesPerLine,t.scanLines;var a=t.mcusPerLine,s=t.progressive,u=(t.maxH,t.maxV,r),c=0,d=0;function p(){if(0<d)return c>>--d&1;if(255==(c=e[r++])){var t=e[r++];if(t)throw new Error("unexpected marker: "+(c<<8|t).toString(16))}return c>>>(d=7)}function m(t){for(var e,r=t;null!==(e=p());){if("number"==typeof(r=r[e]))return r;if("object"!=typeof r)throw new Error("invalid huffman sequence")}return null}function b(t){for(var e=0;0<t;){var r=p();if(null===r)return;e=e<<1|r,t--}return e}function g(t){var e=b(t);return 1<<t-1<=e?e:e+(-1<<t)+1}var _=0;var y,v=0;var w,E,x,j,k,S,I=n.length;S=s?0===f?0===o?function(t,e){var r=m(t.huffmanTableDC),n=0===r?0:g(r)<<h;e[0]=t.pred+=n}:function(t,e){e[0]|=p()<<h}:0===o?function(t,e){if(0<_)_--;else for(var r=f,n=l;r<=n;){var i=m(t.huffmanTableAC),o=15&i,a=i>>4;if(0!==o)e[q[r+=a]]=g(o)*(1<<h),r++;else{if(a<15){_=b(a)+(1<<a)-1;break}r+=16}}}:function(t,e){for(var r=f,n=l,i=0;r<=n;){var o=q[r],a=e[o]<0?-1:1;switch(v){case 0:var s=m(t.huffmanTableAC),u=15&s;if(i=s>>4,0===u)v=i<15?(_=b(i)+(1<<i),4):(i=16,1);else{if(1!==u)throw new Error("invalid ACn encoding");y=g(u),v=i?2:3}continue;case 1:case 2:e[o]?e[o]+=(p()<<h)*a:0==--i&&(v=2==v?3:0);break;case 3:e[o]?e[o]+=(p()<<h)*a:(e[o]=y<<h,v=0);break;case 4:e[o]&&(e[o]+=(p()<<h)*a)}r++}4===v&&0==--_&&(v=0)}:function(t,e){var r=m(t.huffmanTableDC),n=0===r?0:g(r);e[0]=t.pred+=n;for(var i=1;i<64;){var o=m(t.huffmanTableAC),a=15&o,s=o>>4;if(0!==a)e[q[i+=s]]=g(a),i++;else{if(s<15)break;i+=16}}};var A,T,O,P,M,L,C,R,B,N,D,U,F,z,H,G,W,Z=0;for(T=1==I?n[0].blocksPerLine*n[0].blocksPerColumn:a*t.mcusPerColumn,i||(i=T);Z<T;){for(E=0;E<I;E++)n[E].pred=0;if(_=0,1==I)for(w=n[0],k=0;k<i;k++)z=S,void 0,G=(H=Z)/(F=w).blocksPerLine|0,W=H%F.blocksPerLine,z(F,F.blocks[G][W]),Z++;else for(k=0;k<i;k++){for(E=0;E<I;E++)for(O=(w=n[E]).h,P=w.v,x=0;x<P;x++)for(j=0;j<O;j++)L=S,R=x,B=j,void 0,N=(C=Z)%a,D=(C/a|0)*(M=w).v+R,U=N*M.h+B,L(M,M.blocks[D][U]);if(++Z===T)break}if(d=0,(A=e[r]<<8|e[r+1])<65280)throw new Error("marker was not found");if(!(65488<=A&&A<=65495))break;r+=2}return r-u}function H(t,_){var e,r,n=[],i=_.blocksPerLine,o=_.blocksPerColumn,a=i<<3,s=new Int32Array(64),u=new Uint8Array(64);function f(t,e,r){var n,i,o,a,s,u,f,l,h,c,d=_.quantizationTable,p=r;for(c=0;c<64;c++)p[c]=t[c]*d[c];for(c=0;c<8;++c){var m=8*c;0!=p[1+m]||0!=p[2+m]||0!=p[3+m]||0!=p[4+m]||0!=p[5+m]||0!=p[6+m]||0!=p[7+m]?(n=k*p[0+m]+128>>8,i=k*p[4+m]+128>>8,o=p[2+m],a=p[6+m],s=S*(p[1+m]-p[7+m])+128>>8,l=S*(p[1+m]+p[7+m])+128>>8,u=p[3+m]<<4,f=p[5+m]<<4,h=n-i+1>>1,n=n+i+1>>1,i=h,h=o*j+a*x+128>>8,o=o*x-a*j+128>>8,a=h,h=s-f+1>>1,s=s+f+1>>1,f=h,h=l+u+1>>1,u=l-u+1>>1,l=h,h=n-a+1>>1,n=n+a+1>>1,a=h,h=i-o+1>>1,i=i+o+1>>1,o=h,h=s*E+l*w+2048>>12,s=s*w-l*E+2048>>12,l=h,h=u*v+f*y+2048>>12,u=u*y-f*v+2048>>12,f=h,p[0+m]=n+l,p[7+m]=n-l,p[1+m]=i+f,p[6+m]=i-f,p[2+m]=o+u,p[5+m]=o-u,p[3+m]=a+s,p[4+m]=a-s):(h=k*p[0+m]+512>>10,p[0+m]=h,p[1+m]=h,p[2+m]=h,p[3+m]=h,p[4+m]=h,p[5+m]=h,p[6+m]=h,p[7+m]=h)}for(c=0;c<8;++c){var b=c;0!=p[8+b]||0!=p[16+b]||0!=p[24+b]||0!=p[32+b]||0!=p[40+b]||0!=p[48+b]||0!=p[56+b]?(n=k*p[0+b]+2048>>12,i=k*p[32+b]+2048>>12,o=p[16+b],a=p[48+b],s=S*(p[8+b]-p[56+b])+2048>>12,l=S*(p[8+b]+p[56+b])+2048>>12,u=p[24+b],f=p[40+b],h=n-i+1>>1,n=n+i+1>>1,i=h,h=o*j+a*x+2048>>12,o=o*x-a*j+2048>>12,a=h,h=s-f+1>>1,s=s+f+1>>1,f=h,h=l+u+1>>1,u=l-u+1>>1,l=h,h=n-a+1>>1,n=n+a+1>>1,a=h,h=i-o+1>>1,i=i+o+1>>1,o=h,h=s*E+l*w+2048>>12,s=s*w-l*E+2048>>12,l=h,h=u*v+f*y+2048>>12,u=u*y-f*v+2048>>12,f=h,p[0+b]=n+l,p[56+b]=n-l,p[8+b]=i+f,p[48+b]=i-f,p[16+b]=o+u,p[40+b]=o-u,p[24+b]=a+s,p[32+b]=a-s):(h=k*r[c+0]+8192>>14,p[0+b]=h,p[8+b]=h,p[16+b]=h,p[24+b]=h,p[32+b]=h,p[40+b]=h,p[48+b]=h,p[56+b]=h)}for(c=0;c<64;++c){var g=128+(p[c]+8>>4);e[c]=g<0?0:255<g?255:g}}for(var l=0;l<o;l++){var h=l<<3;for(e=0;e<8;e++)n.push(new Uint8Array(a));for(var c=0;c<i;c++){f(_.blocks[l][c],u,s);var d=0,p=c<<3;for(r=0;r<8;r++){var m=n[h+r];for(e=0;e<8;e++)m[p+e]=u[d++]}}}return n}function A(t){return t<0?0:255<t?255:t}return t.prototype={load:function(t){var e=new XMLHttpRequest;e.open("GET",t,!0),e.responseType="arraybuffer",e.onload=function(){var t=new Uint8Array(e.response||e.mozResponseArrayBuffer);this.parse(t),this.onload&&this.onload()}.bind(this),e.send(null)},parse:function(e){var r=0;e.length;function t(){var t=e[r]<<8|e[r+1];return r+=2,t}function n(t){var e,r,n=0,i=0;for(r in t.components)t.components.hasOwnProperty(r)&&(n<(e=t.components[r]).h&&(n=e.h),i<e.v&&(i=e.v));var o=Math.ceil(t.samplesPerLine/8/n),a=Math.ceil(t.scanLines/8/i);for(r in t.components)if(t.components.hasOwnProperty(r)){e=t.components[r];for(var s=Math.ceil(Math.ceil(t.samplesPerLine/8)*e.h/n),u=Math.ceil(Math.ceil(t.scanLines/8)*e.v/i),f=o*e.h,l=a*e.v,h=[],c=0;c<l;c++){for(var d=[],p=0;p<f;p++)d.push(new Int32Array(64));h.push(d)}e.blocksPerLine=s,e.blocksPerColumn=u,e.blocks=h}t.maxH=n,t.maxV=i,t.mcusPerLine=o,t.mcusPerColumn=a}var i,o,a,s,u=null,f=null,l=[],h=[],c=[],d=[],p=t();if(65496!=p)throw new Error("SOI not found");for(p=t();65497!=p;){switch(p){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var m=(void 0,a=t(),s=e.subarray(r,r+a-2),r+=s.length,s);65504===p&&74===m[0]&&70===m[1]&&73===m[2]&&70===m[3]&&0===m[4]&&(u={version:{major:m[5],minor:m[6]},densityUnits:m[7],xDensity:m[8]<<8|m[9],yDensity:m[10]<<8|m[11],thumbWidth:m[12],thumbHeight:m[13],thumbData:m.subarray(14,14+3*m[12]*m[13])}),65518===p&&65===m[0]&&100===m[1]&&111===m[2]&&98===m[3]&&101===m[4]&&0===m[5]&&(f={version:m[6],flags0:m[7]<<8|m[8],flags1:m[9]<<8|m[10],transformCode:m[11]});break;case 65499:for(var b=t()+r-2;r<b;){var g=e[r++],_=new Int32Array(64);if(g>>4==0)for(D=0;D<64;D++){_[q[D]]=e[r++]}else{if(g>>4!=1)throw new Error("DQT: invalid table spec");for(D=0;D<64;D++){_[q[D]]=t()}}l[15&g]=_}break;case 65472:case 65473:case 65474:t(),(i={}).extended=65473===p,i.progressive=65474===p,i.precision=e[r++],i.scanLines=t(),i.samplesPerLine=t(),i.components={},i.componentsOrder=[];var y,v=e[r++];for(B=0;B<v;B++){y=e[r];var w=e[r+1]>>4,E=15&e[r+1],x=e[r+2];i.componentsOrder.push(y),i.components[y]={h:w,v:E,quantizationIdx:x},r+=3}n(i),h.push(i);break;case 65476:var j=t();for(B=2;B<j;){var k=e[r++],S=new Uint8Array(16),I=0;for(D=0;D<16;D++,r++)I+=S[D]=e[r];var A=new Uint8Array(I);for(D=0;D<I;D++,r++)A[D]=e[r];B+=17+I,(k>>4==0?d:c)[15&k]=F(S,A)}break;case 65501:t(),o=t();break;case 65498:t();var T=e[r++],O=[];for(B=0;B<T;B++){U=i.components[e[r++]];var P=e[r++];U.huffmanTableDC=d[P>>4],U.huffmanTableAC=c[15&P],O.push(U)}var M=e[r++],L=e[r++],C=e[r++],R=z(e,r,i,O,o,M,L,C>>4,15&C);r+=R;break;case 65535:255!==e[r]&&r--;break;default:if(255==e[r-3]&&192<=e[r-2]&&e[r-2]<=254){r-=3;break}throw new Error("unknown JPEG marker "+p.toString(16))}p=t()}if(1!=h.length)throw new Error("only single frame JPEGs supported");for(var B=0;B<h.length;B++){var N=h[B].components;for(var D in N)N[D].quantizationTable=l[N[D].quantizationIdx],delete N[D].quantizationIdx}this.width=i.samplesPerLine,this.height=i.scanLines,this.jfif=u,this.adobe=f,this.components=[];for(B=0;B<i.componentsOrder.length;B++){var U=i.components[i.componentsOrder[B]];this.components.push({lines:H(0,U),scaleX:U.h/i.maxH,scaleY:U.v/i.maxV})}},getData:function(t,e){var r,n,i,o,a,s,u,f,l,h,c,d,p,m,b,g,_,y,v,w,E,x=this.width/t,j=this.height/e,k=0,S=t*e*this.components.length,I=new Uint8Array(S);switch(this.components.length){case 1:for(r=this.components[0],h=0;h<e;h++)for(a=r.lines[0|h*r.scaleY*j],l=0;l<t;l++)c=a[0|l*r.scaleX*x],I[k++]=c;break;case 2:for(r=this.components[0],n=this.components[1],h=0;h<e;h++)for(a=r.lines[0|h*r.scaleY*j],s=n.lines[0|h*n.scaleY*j],l=0;l<t;l++)c=a[0|l*r.scaleX*x],I[k++]=c,c=s[0|l*n.scaleX*x],I[k++]=c;break;case 3:for(E=!0,this.adobe&&this.adobe.transformCode?E=!0:void 0!==this.colorTransform&&(E=!!this.colorTransform),r=this.components[0],n=this.components[1],i=this.components[2],h=0;h<e;h++)for(a=r.lines[0|h*r.scaleY*j],s=n.lines[0|h*n.scaleY*j],u=i.lines[0|h*i.scaleY*j],l=0;l<t;l++)w=E?(c=a[0|l*r.scaleX*x],d=s[0|l*n.scaleX*x],y=A(c+1.402*((p=u[0|l*i.scaleX*x])-128)),v=A(c-.3441363*(d-128)-.71413636*(p-128)),A(c+1.772*(d-128))):(y=a[0|l*r.scaleX*x],v=s[0|l*n.scaleX*x],u[0|l*i.scaleX*x]),I[k++]=y,I[k++]=v,I[k++]=w;break;case 4:if(!this.adobe)throw"Unsupported color mode (4 components)";for(E=!1,this.adobe&&this.adobe.transformCode?E=!0:void 0!==this.colorTransform&&(E=!!this.colorTransform),r=this.components[0],n=this.components[1],i=this.components[2],o=this.components[3],h=0;h<e;h++)for(a=r.lines[0|h*r.scaleY*j],s=n.lines[0|h*n.scaleY*j],u=i.lines[0|h*i.scaleY*j],f=o.lines[0|h*o.scaleY*j],l=0;l<t;l++)E?(c=a[0|l*r.scaleX*x],d=s[0|l*n.scaleX*x],p=u[0|l*i.scaleX*x],m=f[0|l*o.scaleX*x],b=255-A(c+1.402*(p-128)),g=255-A(c-.3441363*(d-128)-.71413636*(p-128)),_=255-A(c+1.772*(d-128))):(b=a[0|l*r.scaleX*x],g=s[0|l*n.scaleX*x],_=u[0|l*i.scaleX*x],m=f[0|l*o.scaleX*x]),I[k++]=255-b,I[k++]=255-g,I[k++]=255-_,I[k++]=255-m;break;default:throw"Unsupported color mode"}return I},copyToImageData:function(t){var e,r,n,i,o,a,s,u,f,l=t.width,h=t.height,c=t.data,d=this.getData(l,h),p=0,m=0;switch(this.components.length){case 1:for(r=0;r<h;r++)for(e=0;e<l;e++)n=d[p++],c[m++]=n,c[m++]=n,c[m++]=n,c[m++]=255;break;case 3:for(r=0;r<h;r++)for(e=0;e<l;e++)s=d[p++],u=d[p++],f=d[p++],c[m++]=s,c[m++]=u,c[m++]=f,c[m++]=255;break;case 4:for(r=0;r<h;r++)for(e=0;e<l;e++)o=d[p++],a=d[p++],n=d[p++],s=255-A(o*(1-(i=d[p++])/255)+i),u=255-A(a*(1-i/255)+i),f=255-A(n*(1-i/255)+i),c[m++]=s,c[m++]=u,c[m++]=f,c[m++]=255;break;default:throw"Unsupported color mode"}}},t}();e.exports=function(t,e){var r=new Uint8Array(t),n=new a;n.parse(r);var i={width:n.width,height:n.height,data:e?new Uint8Array(n.width*n.height*4):new o(n.width*n.height*4)};return n.copyToImageData(i),i}}).call(this,t("buffer").Buffer)},{buffer:16}],173:[function(t,e,r){(function(J){function r(t){Math.round;var E,x,j,k,e,h=Math.floor,S=new Array(64),I=new Array(64),A=new Array(64),T=new Array(64),g=new Array(65535),_=new Array(65535),K=new Array(64),y=new Array(64),O=[],P=0,M=7,L=new Array(64),C=new Array(64),R=new Array(64),r=new Array(256),B=new Array(2048),v=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],N=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],D=[0,1,2,3,4,5,6,7,8,9,10,11],U=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],F=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],z=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],H=[0,1,2,3,4,5,6,7,8,9,10,11],G=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],W=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function n(t,e){for(var r=0,n=0,i=new Array,o=1;o<=16;o++){for(var a=1;a<=t[o];a++)i[e[n]]=[],i[e[n]][0]=r,i[e[n]][1]=o,n++,r++;r*=2}return i}function Z(t){for(var e=t[0],r=t[1]-1;0<=r;)e&1<<r&&(P|=1<<M),r--,--M<0&&(255==P?(q(255),q(0)):q(P),M=7,P=0)}function q(t){O.push(t)}function Y(t){q(t>>8&255),q(255&t)}function V(t,e,r,n,i){for(var o,a=i[0],s=i[240],u=function(t,e){var r,n,i,o,a,s,u,f,l,h,c=0;for(l=0;l<8;++l){r=t[c],n=t[c+1],i=t[c+2],o=t[c+3],a=t[c+4],s=t[c+5],u=t[c+6];var d=r+(f=t[c+7]),p=r-f,m=n+u,b=n-u,g=i+s,_=i-s,y=o+a,v=o-a,w=d+y,E=d-y,x=m+g,j=m-g;t[c]=w+x,t[c+4]=w-x;var k=.707106781*(j+E);t[c+2]=E+k,t[c+6]=E-k;var S=.382683433*((w=v+_)-(j=b+p)),I=.5411961*w+S,A=1.306562965*j+S,T=.707106781*(x=_+b),O=p+T,P=p-T;t[c+5]=P+I,t[c+3]=P-I,t[c+1]=O+A,t[c+7]=O-A,c+=8}for(l=c=0;l<8;++l){r=t[c],n=t[c+8],i=t[c+16],o=t[c+24],a=t[c+32],s=t[c+40],u=t[c+48];var M=r+(f=t[c+56]),L=r-f,C=n+u,R=n-u,B=i+s,N=i-s,D=o+a,U=o-a,F=M+D,z=M-D,H=C+B,G=C-B;t[c]=F+H,t[c+32]=F-H;var W=.707106781*(G+z);t[c+16]=z+W,t[c+48]=z-W;var Z=.382683433*((F=U+N)-(G=R+L)),q=.5411961*F+Z,Y=1.306562965*G+Z,V=.707106781*(H=N+R),X=L+V,J=L-V;t[c+40]=J+q,t[c+24]=J-q,t[c+8]=X+Y,t[c+56]=X-Y,c++}for(l=0;l<64;++l)h=t[l]*e[l],K[l]=0<h?h+.5|0:h-.5|0;return K}(t,e),f=0;f<64;++f)y[v[f]]=u[f];var l=y[0]-r;r=y[0],0==l?Z(n[0]):(Z(n[_[o=32767+l]]),Z(g[o]));for(var h=63;0<h&&0==y[h];h--);if(0==h)return Z(a),r;for(var c,d=1;d<=h;){for(var p=d;0==y[d]&&d<=h;++d);var m=d-p;if(16<=m){c=m>>4;for(var b=1;b<=c;++b)Z(s);m&=15}o=32767+y[d],Z(i[(m<<4)+_[o]]),Z(g[o]),d++}return 63!=h&&Z(a),r}function X(t){if(t<=0&&(t=1),100<t&&(t=100),e!=t){(function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],r=0;r<64;r++){var n=h((e[r]*t+50)/100);n<1?n=1:255<n&&(n=255),S[v[r]]=n}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var a=h((i[o]*t+50)/100);a<1?a=1:255<a&&(a=255),I[v[o]]=a}for(var s=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],u=0,f=0;f<8;f++)for(var l=0;l<8;l++)A[u]=1/(S[v[u]]*s[f]*s[l]*8),T[u]=1/(I[v[u]]*s[f]*s[l]*8),u++})(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),e=t}}this.encode=function(t,e){var r,n;(new Date).getTime();e&&X(e),O=new Array,P=0,M=7,Y(65496),Y(65504),Y(16),q(74),q(70),q(73),q(70),q(0),q(1),q(1),q(0),Y(1),Y(1),q(0),q(0),function(){Y(65499),Y(132),q(0);for(var t=0;t<64;t++)q(S[t]);q(1);for(var e=0;e<64;e++)q(I[e])}(),r=t.width,n=t.height,Y(65472),Y(17),q(8),Y(n),Y(r),q(3),q(1),q(17),q(0),q(2),q(17),q(1),q(3),q(17),q(1),function(){Y(65476),Y(418),q(0);for(var t=0;t<16;t++)q(N[t+1]);for(var e=0;e<=11;e++)q(D[e]);q(16);for(var r=0;r<16;r++)q(U[r+1]);for(var n=0;n<=161;n++)q(F[n]);q(1);for(var i=0;i<16;i++)q(z[i+1]);for(var o=0;o<=11;o++)q(H[o]);q(17);for(var a=0;a<16;a++)q(G[a+1]);for(var s=0;s<=161;s++)q(W[s])}(),Y(65498),Y(12),q(3),q(1),q(0),q(2),q(17),q(3),q(17),q(0),q(63),q(0);var i=0,o=0,a=0;P=0,M=7,this.encode.displayName="_encode_";for(var s,u,f,l,h,c,d,p,m,b=t.data,g=t.width,_=t.height,y=4*g,v=0;v<_;){for(s=0;s<y;){for(c=h=y*v+s,d=-1,m=p=0;m<64;m++)c=h+(p=m>>3)*y+(d=4*(7&m)),_<=v+p&&(c-=y*(v+1+p-_)),y<=s+d&&(c-=s+d-y+4),u=b[c++],f=b[c++],l=b[c++],L[m]=(B[u]+B[f+256>>0]+B[l+512>>0]>>16)-128,C[m]=(B[u+768>>0]+B[f+1024>>0]+B[l+1280>>0]>>16)-128,R[m]=(B[u+1280>>0]+B[f+1536>>0]+B[l+1792>>0]>>16)-128;i=V(L,A,i,E,j),o=V(C,T,o,x,k),a=V(R,T,a,x,k),s+=32}v+=8}if(0<=M){var w=[];w[1]=M+1,w[0]=(1<<M+1)-1,Z(w)}return Y(65497),new J(O)},function(){(new Date).getTime();t||(t=50),function(){for(var t=String.fromCharCode,e=0;e<256;e++)r[e]=t(e)}(),E=n(N,D),x=n(z,H),j=n(U,F),k=n(G,W),function(){for(var t=1,e=2,r=1;r<=15;r++){for(var n=t;n<e;n++)_[32767+n]=r,g[32767+n]=[],g[32767+n][1]=r,g[32767+n][0]=n;for(var i=-(e-1);i<=-t;i++)_[32767+i]=r,g[32767+i]=[],g[32767+i][1]=r,g[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)B[t]=19595*t,B[t+256>>0]=38470*t,B[t+512>>0]=7471*t+32768,B[t+768>>0]=-11059*t,B[t+1024>>0]=-21709*t,B[t+1280>>0]=32768*t+8421375,B[t+1536>>0]=-27439*t,B[t+1792>>0]=-5329*t}(),X(t),(new Date).getTime()}()}e.exports=function(t,e){void 0===e&&(e=50);return{data:new r(e).encode(t,e),width:t.width,height:t.height}}}).call(this,t("buffer").Buffer)},{buffer:16}],174:[function(t,e,r){(function(u){var f=t("path"),l=t("fs"),h=parseInt("0777",8);function c(n,i,t,o){"function"==typeof i?(t=i,i={}):i&&"object"==typeof i||(i={mode:i});var e=i.mode,a=i.fs||l;void 0===e&&(e=h&~u.umask()),o||(o=null);var s=t||function(){};n=f.resolve(n),a.mkdir(n,e,function(r){if(!r)return s(null,o=o||n);switch(r.code){case"ENOENT":c(f.dirname(n),i,function(t,e){t?s(t,e):c(n,i,s,e)});break;default:a.stat(n,function(t,e){t||!e.isDirectory()?s(r,o):s(null,o)})}})}e.exports=c.mkdirp=c.mkdirP=c,c.sync=function t(r,n,i){n&&"object"==typeof n||(n={mode:n});var e=n.mode,o=n.fs||l;void 0===e&&(e=h&~u.umask()),i||(i=null),r=f.resolve(r);try{o.mkdirSync(r,e),i=i||r}catch(e){switch(e.code){case"ENOENT":t(r,n,i=t(f.dirname(r),n,i));break;default:var a;try{a=o.statSync(r)}catch(t){throw e}if(!a.isDirectory())throw e}}return i}}).call(this,t("_process"))},{_process:223,fs:14,path:197}],175:[function(t,e,r){"use strict";function A(t,e,r,n){for(var i=t[e++],o=1<<i,a=o+1,s=a+1,u=i+1,f=(1<<u)-1,l=0,h=0,c=0,d=t[e++],p=new Int32Array(4096),m=null;;){for(;l<16&&0!==d;)h|=t[e++]<<l,l+=8,1===d?d=t[e++]:--d;if(l<u)break;var b=h&f;if(h>>=u,l-=u,b!==o){if(b===a)break;for(var g=b<s?b:m,_=0,y=g;o<y;)y=p[y]>>8,++_;var v=y;if(n<c+_+(g!==b?1:0))return void console.log("Warning, gif stream longer than expected.");r[c++]=v;var w=c+=_;for(g!==b&&(r[c++]=v),y=g;_--;)y=p[y],r[--w]=255&y,y>>=8;null!==m&&s<4096&&(p[s++]=m<<8|v,f+1<=s&&u<12&&(++u,f=f<<1|1)),m=b}else s=a+1,f=(1<<(u=i+1))-1,m=null}return c!==n&&console.log("Warning, gif stream shorter than expected."),r}try{r.GifWriter=function(g,t,e,r){var _=0,n=void 0===(r=void 0===r?{}:r).loop?null:r.loop,y=void 0===r.palette?null:r.palette;if(t<=0||e<=0||65535<t||65535<e)throw new Error("Width/Height invalid.");function v(t){var e=t.length;if(e<2||256<e||e&e-1)throw new Error("Invalid code/color length, must be power of 2 and 2 .. 256.");return e}g[_++]=71,g[_++]=73,g[_++]=70,g[_++]=56,g[_++]=57,g[_++]=97;var i=0,o=0;if(null!==y){for(var a=v(y);a>>=1;)++i;if(a=1<<i,--i,void 0!==r.background){if(a<=(o=r.background))throw new Error("Background index out of range.");if(0===o)throw new Error("Background index explicitly passed as 0.")}}if(g[_++]=255&t,g[_++]=t>>8&255,g[_++]=255&e,g[_++]=e>>8&255,g[_++]=(null!==y?128:0)|i,g[_++]=o,g[_++]=0,null!==y)for(var s=0,u=y.length;s<u;++s){var f=y[s];g[_++]=f>>16&255,g[_++]=f>>8&255,g[_++]=255&f}if(null!==n){if(n<0||65535<n)throw new Error("Loop count invalid.");g[_++]=33,g[_++]=255,g[_++]=11,g[_++]=78,g[_++]=69,g[_++]=84,g[_++]=83,g[_++]=67,g[_++]=65,g[_++]=80,g[_++]=69,g[_++]=50,g[_++]=46,g[_++]=48,g[_++]=3,g[_++]=1,g[_++]=255&n,g[_++]=n>>8&255,g[_++]=0}var w=!1;this.addFrame=function(t,e,r,n,i,o){if(!0===w&&(--_,w=!1),o=void 0===o?{}:o,t<0||e<0||65535<t||65535<e)throw new Error("x/y invalid.");if(r<=0||n<=0||65535<r||65535<n)throw new Error("Width/Height invalid.");if(i.length<r*n)throw new Error("Not enough pixels for the frame size.");var a=!0,s=o.palette;if(null==s&&(a=!1,s=y),null==s)throw new Error("Must supply either a local or global palette.");for(var u=v(s),f=0;u>>=1;)++f;u=1<<f;var l=void 0===o.delay?0:o.delay,h=void 0===o.disposal?0:o.disposal;if(h<0||3<h)throw new Error("Disposal out of range.");var c=!1,d=0;if(void 0!==o.transparent&&null!==o.transparent&&(c=!0,(d=o.transparent)<0||u<=d))throw new Error("Transparent color index.");if((0!==h||c||0!==l)&&(g[_++]=33,g[_++]=249,g[_++]=4,g[_++]=h<<2|(!0===c?1:0),g[_++]=255&l,g[_++]=l>>8&255,g[_++]=d,g[_++]=0),g[_++]=44,g[_++]=255&t,g[_++]=t>>8&255,g[_++]=255&e,g[_++]=e>>8&255,g[_++]=255&r,g[_++]=r>>8&255,g[_++]=255&n,g[_++]=n>>8&255,g[_++]=!0===a?128|f-1:0,!0===a)for(var p=0,m=s.length;p<m;++p){var b=s[p];g[_++]=b>>16&255,g[_++]=b>>8&255,g[_++]=255&b}return _=function(e,r,t,n){e[r++]=t;var i=r++,o=1<<t,a=o-1,s=o+1,u=s+1,f=t+1,l=0,h=0;function c(t){for(;t<=l;)e[r++]=255&h,h>>=8,l-=8,r===i+256&&(e[i]=255,i=r++)}function d(t){h|=t<<l,l+=f,c(8)}var p=n[0]&a,m={};d(o);for(var b=1,g=n.length;b<g;++b){var _=n[b]&a,y=p<<8|_,v=m[y];if(void 0===v){for(h|=p<<l,l+=f;8<=l;)e[r++]=255&h,h>>=8,l-=8,r===i+256&&(e[i]=255,i=r++);4096===u?(d(o),u=s+1,f=t+1,m={}):(1<<f<=u&&++f,m[y]=u++),p=_}else p=v}return d(p),d(s),c(1),i+1===r?e[i]=0:(e[i]=r-i-1,e[r++]=0),r}(g,_,f<2?2:f,i)},this.end=function(){return!1===w&&(g[_++]=59,w=!0),_},this.getOutputBuffer=function(){return g},this.setOutputBuffer=function(t){g=t},this.getOutputBufferPosition=function(){return _},this.setOutputBufferPosition=function(t){_=t}},r.GifReader=function(w){var t=0;if(71!==w[t++]||73!==w[t++]||70!==w[t++]||56!==w[t++]||56!=(w[t++]+1&253)||97!==w[t++])throw new Error("Invalid GIF 87a/89a header.");var E=w[t++]|w[t++]<<8,e=w[t++]|w[t++]<<8,r=w[t++],n=r>>7,i=1<<1+(7&r);w[t++],w[t++];var o=null,a=null;n&&(o=t,t+=3*(a=i));var s=!0,u=[],f=0,l=null,h=0,c=null;for(this.width=E,this.height=e;s&&t<w.length;)switch(w[t++]){case 33:switch(w[t++]){case 255:if(11!==w[t]||78==w[t+1]&&69==w[t+2]&&84==w[t+3]&&83==w[t+4]&&67==w[t+5]&&65==w[t+6]&&80==w[t+7]&&69==w[t+8]&&50==w[t+9]&&46==w[t+10]&&48==w[t+11]&&3==w[t+12]&&1==w[t+13]&&0==w[t+16])t+=14,c=w[t++]|w[t++]<<8,t++;else for(t+=12;;){if(!(0<=(I=w[t++])))throw Error("Invalid block size");if(0===I)break;t+=I}break;case 249:if(4!==w[t++]||0!==w[t+4])throw new Error("Invalid graphics extension block.");var d=w[t++];f=w[t++]|w[t++]<<8,l=w[t++],0==(1&d)&&(l=null),h=d>>2&7,t++;break;case 254:for(;;){if(!(0<=(I=w[t++])))throw Error("Invalid block size");if(0===I)break;t+=I}break;default:throw new Error("Unknown graphic control label: 0x"+w[t-1].toString(16))}break;case 44:var p=w[t++]|w[t++]<<8,m=w[t++]|w[t++]<<8,b=w[t++]|w[t++]<<8,g=w[t++]|w[t++]<<8,_=w[t++],y=_>>6&1,v=1<<1+(7&_),x=o,j=a,k=!1;_>>7&&(k=!0,x=t,t+=3*(j=v));var S=t;for(t++;;){var I;if(!(0<=(I=w[t++])))throw Error("Invalid block size");if(0===I)break;t+=I}u.push({x:p,y:m,width:b,height:g,has_local_palette:k,palette_offset:x,palette_size:j,data_offset:S,data_length:t-S,transparent_index:l,interlaced:!!y,delay:f,disposal:h});break;case 59:s=!1;break;default:throw new Error("Unknown gif block: 0x"+w[t-1].toString(16))}this.numFrames=function(){return u.length},this.loopCount=function(){return c},this.frameInfo=function(t){if(t<0||t>=u.length)throw new Error("Frame index out of range.");return u[t]},this.decodeAndBlitFrameBGRA=function(t,e){var r=this.frameInfo(t),n=r.width*r.height,i=new Uint8Array(n);A(w,r.data_offset,i,n);var o=r.palette_offset,a=r.transparent_index;null===a&&(a=256);var s=r.width,u=E-s,f=s,l=4*(r.y*E+r.x),h=4*((r.y+r.height)*E+r.x),c=l,d=4*u;!0===r.interlaced&&(d+=4*E*7);for(var p=8,m=0,b=i.length;m<b;++m){var g=i[m];if(0===f&&(f=s,h<=(c+=d)&&(d=4*u+4*E*(p-1),c=l+(s+u)*(p<<1),p>>=1)),g===a)c+=4;else{var _=w[o+3*g],y=w[o+3*g+1],v=w[o+3*g+2];e[c++]=v,e[c++]=y,e[c++]=_,e[c++]=255}--f}},this.decodeAndBlitFrameRGBA=function(t,e){var r=this.frameInfo(t),n=r.width*r.height,i=new Uint8Array(n);A(w,r.data_offset,i,n);var o=r.palette_offset,a=r.transparent_index;null===a&&(a=256);var s=r.width,u=E-s,f=s,l=4*(r.y*E+r.x),h=4*((r.y+r.height)*E+r.x),c=l,d=4*u;!0===r.interlaced&&(d+=4*E*7);for(var p=8,m=0,b=i.length;m<b;++m){var g=i[m];if(0===f&&(f=s,h<=(c+=d)&&(d=4*u+4*E*(p-1),c=l+(s+u)*(p<<1),p>>=1)),g===a)c+=4;else{var _=w[o+3*g],y=w[o+3*g+1],v=w[o+3*g+2];e[c++]=_,e[c++]=y,e[c++]=v,e[c++]=255}--f}}}}catch(t){}},{}],176:[function(t,e,r){"use strict";var n={};(0,t("./lib/utils/common").assign)(n,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),e.exports=n},{"./lib/deflate":177,"./lib/inflate":178,"./lib/utils/common":179,"./lib/zlib/constants":182}],177:[function(t,e,r){"use strict";var a=t("./zlib/deflate"),s=t("./utils/common"),u=t("./utils/strings"),i=t("./zlib/messages"),o=t("./zlib/zstream"),f=Object.prototype.toString,l=0,h=-1,c=0,d=8;function p(t){if(!(this instanceof p))return new p(t);this.options=s.assign({level:h,method:d,chunkSize:16384,windowBits:15,memLevel:8,strategy:c,to:""},t||{});var e=this.options;e.raw&&0<e.windowBits?e.windowBits=-e.windowBits:e.gzip&&0<e.windowBits&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var r=a.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(r!==l)throw new Error(i[r]);if(e.header&&a.deflateSetHeader(this.strm,e.header),e.dictionary){var n;if(n="string"==typeof e.dictionary?u.string2buf(e.dictionary):"[object ArrayBuffer]"===f.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,(r=a.deflateSetDictionary(this.strm,n))!==l)throw new Error(i[r]);this._dict_set=!0}}function n(t,e){var r=new p(e);if(r.push(t,!0),r.err)throw r.msg||i[r.err];return r.result}p.prototype.push=function(t,e){var r,n,i=this.strm,o=this.options.chunkSize;if(this.ended)return!1;n=e===~~e?e:!0===e?4:0,"string"==typeof t?i.input=u.string2buf(t):"[object ArrayBuffer]"===f.call(t)?i.input=new Uint8Array(t):i.input=t,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new s.Buf8(o),i.next_out=0,i.avail_out=o),1!==(r=a.deflate(i,n))&&r!==l)return this.onEnd(r),!(this.ended=!0);0!==i.avail_out&&(0!==i.avail_in||4!==n&&2!==n)||("string"===this.options.to?this.onData(u.buf2binstring(s.shrinkBuf(i.output,i.next_out))):this.onData(s.shrinkBuf(i.output,i.next_out)))}while((0<i.avail_in||0===i.avail_out)&&1!==r);return 4===n?(r=a.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===l):2!==n||(this.onEnd(l),!(i.avail_out=0))},p.prototype.onData=function(t){this.chunks.push(t)},p.prototype.onEnd=function(t){t===l&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=s.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Deflate=p,r.deflate=n,r.deflateRaw=function(t,e){return(e=e||{}).raw=!0,n(t,e)},r.gzip=function(t,e){return(e=e||{}).gzip=!0,n(t,e)}},{"./utils/common":179,"./utils/strings":180,"./zlib/deflate":184,"./zlib/messages":189,"./zlib/zstream":191}],178:[function(t,e,r){"use strict";var c=t("./zlib/inflate"),d=t("./utils/common"),p=t("./utils/strings"),m=t("./zlib/constants"),n=t("./zlib/messages"),i=t("./zlib/zstream"),o=t("./zlib/gzheader"),b=Object.prototype.toString;function a(t){if(!(this instanceof a))return new a(t);this.options=d.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&0<=e.windowBits&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(0<=e.windowBits&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),15<e.windowBits&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new i,this.strm.avail_out=0;var r=c.inflateInit2(this.strm,e.windowBits);if(r!==m.Z_OK)throw new Error(n[r]);this.header=new o,c.inflateGetHeader(this.strm,this.header)}function s(t,e){var r=new a(e);if(r.push(t,!0),r.err)throw r.msg||n[r.err];return r.result}a.prototype.push=function(t,e){var r,n,i,o,a,s,u=this.strm,f=this.options.chunkSize,l=this.options.dictionary,h=!1;if(this.ended)return!1;n=e===~~e?e:!0===e?m.Z_FINISH:m.Z_NO_FLUSH,"string"==typeof t?u.input=p.binstring2buf(t):"[object ArrayBuffer]"===b.call(t)?u.input=new Uint8Array(t):u.input=t,u.next_in=0,u.avail_in=u.input.length;do{if(0===u.avail_out&&(u.output=new d.Buf8(f),u.next_out=0,u.avail_out=f),(r=c.inflate(u,m.Z_NO_FLUSH))===m.Z_NEED_DICT&&l&&(s="string"==typeof l?p.string2buf(l):"[object ArrayBuffer]"===b.call(l)?new Uint8Array(l):l,r=c.inflateSetDictionary(this.strm,s)),r===m.Z_BUF_ERROR&&!0===h&&(r=m.Z_OK,h=!1),r!==m.Z_STREAM_END&&r!==m.Z_OK)return this.onEnd(r),!(this.ended=!0);u.next_out&&(0!==u.avail_out&&r!==m.Z_STREAM_END&&(0!==u.avail_in||n!==m.Z_FINISH&&n!==m.Z_SYNC_FLUSH)||("string"===this.options.to?(i=p.utf8border(u.output,u.next_out),o=u.next_out-i,a=p.buf2string(u.output,i),u.next_out=o,u.avail_out=f-o,o&&d.arraySet(u.output,u.output,i,o,0),this.onData(a)):this.onData(d.shrinkBuf(u.output,u.next_out)))),0===u.avail_in&&0===u.avail_out&&(h=!0)}while((0<u.avail_in||0===u.avail_out)&&r!==m.Z_STREAM_END);return r===m.Z_STREAM_END&&(n=m.Z_FINISH),n===m.Z_FINISH?(r=c.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===m.Z_OK):n!==m.Z_SYNC_FLUSH||(this.onEnd(m.Z_OK),!(u.avail_out=0))},a.prototype.onData=function(t){this.chunks.push(t)},a.prototype.onEnd=function(t){t===m.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=d.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Inflate=a,r.inflate=s,r.inflateRaw=function(t,e){return(e=e||{}).raw=!0,s(t,e)},r.ungzip=s},{"./utils/common":179,"./utils/strings":180,"./zlib/constants":182,"./zlib/gzheader":185,"./zlib/inflate":187,"./zlib/messages":189,"./zlib/zstream":191}],179:[function(t,e,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(t){for(var e,r,n=Array.prototype.slice.call(arguments,1);n.length;){var i=n.shift();if(i){if("object"!=typeof i)throw new TypeError(i+"must be non-object");for(var o in i)e=i,r=o,Object.prototype.hasOwnProperty.call(e,r)&&(t[o]=i[o])}}return t},r.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var i={arraySet:function(t,e,r,n,i){if(e.subarray&&t.subarray)t.set(e.subarray(r,r+n),i);else for(var o=0;o<n;o++)t[i+o]=e[r+o]},flattenChunks:function(t){var e,r,n,i,o,a;for(e=n=0,r=t.length;e<r;e++)n+=t[e].length;for(a=new Uint8Array(n),e=i=0,r=t.length;e<r;e++)o=t[e],a.set(o,i),i+=o.length;return a}},o={arraySet:function(t,e,r,n,i){for(var o=0;o<n;o++)t[i+o]=e[r+o]},flattenChunks:function(t){return[].concat.apply([],t)}};r.setTyped=function(t){t?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,o))},r.setTyped(n)},{}],180:[function(t,e,r){"use strict";var u=t("./common"),i=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(t){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){o=!1}for(var f=new u.Buf8(256),n=0;n<256;n++)f[n]=252<=n?6:248<=n?5:240<=n?4:224<=n?3:192<=n?2:1;function l(t,e){if(e<65537&&(t.subarray&&o||!t.subarray&&i))return String.fromCharCode.apply(null,u.shrinkBuf(t,e));for(var r="",n=0;n<e;n++)r+=String.fromCharCode(t[n]);return r}f[254]=f[254]=1,r.string2buf=function(t){var e,r,n,i,o,a=t.length,s=0;for(i=0;i<a;i++)55296==(64512&(r=t.charCodeAt(i)))&&i+1<a&&56320==(64512&(n=t.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(n-56320),i++),s+=r<128?1:r<2048?2:r<65536?3:4;for(e=new u.Buf8(s),i=o=0;o<s;i++)55296==(64512&(r=t.charCodeAt(i)))&&i+1<a&&56320==(64512&(n=t.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(n-56320),i++),e[o++]=r<128?r:(e[o++]=r<2048?192|r>>>6:(e[o++]=r<65536?224|r>>>12:(e[o++]=240|r>>>18,128|r>>>12&63),128|r>>>6&63),128|63&r);return e},r.buf2binstring=function(t){return l(t,t.length)},r.binstring2buf=function(t){for(var e=new u.Buf8(t.length),r=0,n=e.length;r<n;r++)e[r]=t.charCodeAt(r);return e},r.buf2string=function(t,e){var r,n,i,o,a=e||t.length,s=new Array(2*a);for(r=n=0;r<a;)if((i=t[r++])<128)s[n++]=i;else if(4<(o=f[i]))s[n++]=65533,r+=o-1;else{for(i&=2===o?31:3===o?15:7;1<o&&r<a;)i=i<<6|63&t[r++],o--;s[n++]=1<o?65533:i<65536?i:(i-=65536,s[n++]=55296|i>>10&1023,56320|1023&i)}return l(s,n)},r.utf8border=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;0<=r&&128==(192&t[r]);)r--;return r<0?e:0===r?e:r+f[t[r]]>e?r:e}},{"./common":179}],181:[function(t,e,r){"use strict";e.exports=function(t,e,r,n){for(var i=65535&t|0,o=t>>>16&65535|0,a=0;0!==r;){for(r-=a=2e3<r?2e3:r;o=o+(i=i+e[n++]|0)|0,--a;);i%=65521,o%=65521}return i|o<<16|0}},{}],182:[function(t,e,r){"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],183:[function(t,e,r){"use strict";var s=function(){for(var t,e=[],r=0;r<256;r++){t=r;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[r]=t}return e}();e.exports=function(t,e,r,n){var i=s,o=n+r;t^=-1;for(var a=n;a<o;a++)t=t>>>8^i[255&(t^e[a])];return-1^t}},{}],184:[function(t,e,r){"use strict";var u,c=t("../utils/common"),f=t("./trees"),d=t("./adler32"),p=t("./crc32"),n=t("./messages"),l=0,h=4,m=0,b=-2,g=-1,_=4,i=2,y=8,v=9,o=286,a=30,s=19,w=2*o+1,E=15,x=3,j=258,k=j+x+1,S=42,I=113,A=1,T=2,O=3,P=4;function M(t,e){return t.msg=n[e],e}function L(t){return(t<<1)-(4<t?9:0)}function C(t){for(var e=t.length;0<=--e;)t[e]=0}function R(t){var e=t.state,r=e.pending;r>t.avail_out&&(r=t.avail_out),0!==r&&(c.arraySet(t.output,e.pending_buf,e.pending_out,r,t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending&&(e.pending_out=0))}function B(t,e){f._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,R(t.strm)}function N(t,e){t.pending_buf[t.pending++]=e}function D(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function U(t,e){var r,n,i=t.max_chain_length,o=t.strstart,a=t.prev_length,s=t.nice_match,u=t.strstart>t.w_size-k?t.strstart-(t.w_size-k):0,f=t.window,l=t.w_mask,h=t.prev,c=t.strstart+j,d=f[o+a-1],p=f[o+a];t.prev_length>=t.good_match&&(i>>=2),s>t.lookahead&&(s=t.lookahead);do{if(f[(r=e)+a]===p&&f[r+a-1]===d&&f[r]===f[o]&&f[++r]===f[o+1]){o+=2,r++;do{}while(f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&o<c);if(n=j-(c-o),o=c-j,a<n){if(t.match_start=e,s<=(a=n))break;d=f[o+a-1],p=f[o+a]}}}while((e=h[e&l])>u&&0!=--i);return a<=t.lookahead?a:t.lookahead}function F(t){var e,r,n,i,o,a,s,u,f,l,h=t.w_size;do{if(i=t.window_size-t.lookahead-t.strstart,t.strstart>=h+(h-k)){for(c.arraySet(t.window,t.window,h,h,0),t.match_start-=h,t.strstart-=h,t.block_start-=h,e=r=t.hash_size;n=t.head[--e],t.head[e]=h<=n?n-h:0,--r;);for(e=r=h;n=t.prev[--e],t.prev[e]=h<=n?n-h:0,--r;);i+=h}if(0===t.strm.avail_in)break;if(a=t.strm,s=t.window,u=t.strstart+t.lookahead,f=i,l=void 0,l=a.avail_in,f<l&&(l=f),r=0===l?0:(a.avail_in-=l,c.arraySet(s,a.input,a.next_in,l,u),1===a.state.wrap?a.adler=d(a.adler,s,l,u):2===a.state.wrap&&(a.adler=p(a.adler,s,l,u)),a.next_in+=l,a.total_in+=l,l),t.lookahead+=r,t.lookahead+t.insert>=x)for(o=t.strstart-t.insert,t.ins_h=t.window[o],t.ins_h=(t.ins_h<<t.hash_shift^t.window[o+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[o+x-1])&t.hash_mask,t.prev[o&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=o,o++,t.insert--,!(t.lookahead+t.insert<x)););}while(t.lookahead<k&&0!==t.strm.avail_in)}function z(t,e){for(var r,n;;){if(t.lookahead<k){if(F(t),t.lookahead<k&&e===l)return A;if(0===t.lookahead)break}if(r=0,t.lookahead>=x&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+x-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-k&&(t.match_length=U(t,r)),t.match_length>=x)if(n=f._tr_tally(t,t.strstart-t.match_start,t.match_length-x),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=x){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+x-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else n=f._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(B(t,!1),0===t.strm.avail_out))return A}return t.insert=t.strstart<x-1?t.strstart:x-1,e===h?(B(t,!0),0===t.strm.avail_out?O:P):t.last_lit&&(B(t,!1),0===t.strm.avail_out)?A:T}function H(t,e){for(var r,n,i;;){if(t.lookahead<k){if(F(t),t.lookahead<k&&e===l)return A;if(0===t.lookahead)break}if(r=0,t.lookahead>=x&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+x-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=x-1,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-k&&(t.match_length=U(t,r),t.match_length<=5&&(1===t.strategy||t.match_length===x&&4096<t.strstart-t.match_start)&&(t.match_length=x-1)),t.prev_length>=x&&t.match_length<=t.prev_length){for(i=t.strstart+t.lookahead-x,n=f._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-x),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=i&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+x-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=x-1,t.strstart++,n&&(B(t,!1),0===t.strm.avail_out))return A}else if(t.match_available){if((n=f._tr_tally(t,0,t.window[t.strstart-1]))&&B(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return A}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=f._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<x-1?t.strstart:x-1,e===h?(B(t,!0),0===t.strm.avail_out?O:P):t.last_lit&&(B(t,!1),0===t.strm.avail_out)?A:T}function G(t,e,r,n,i){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=n,this.func=i}function W(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=y,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new c.Buf16(2*w),this.dyn_dtree=new c.Buf16(2*(2*a+1)),this.bl_tree=new c.Buf16(2*(2*s+1)),C(this.dyn_ltree),C(this.dyn_dtree),C(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new c.Buf16(E+1),this.heap=new c.Buf16(2*o+1),C(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new c.Buf16(2*o+1),C(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Z(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=i,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?S:I,t.adler=2===e.wrap?0:1,e.last_flush=l,f._tr_init(e),m):M(t,b)}function q(t){var e,r=Z(t);return r===m&&((e=t.state).window_size=2*e.w_size,C(e.head),e.max_lazy_match=u[e.level].max_lazy,e.good_match=u[e.level].good_length,e.nice_match=u[e.level].nice_length,e.max_chain_length=u[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=x-1,e.match_available=0,e.ins_h=0),r}function Y(t,e,r,n,i,o){if(!t)return b;var a=1;if(e===g&&(e=6),n<0?(a=0,n=-n):15<n&&(a=2,n-=16),i<1||v<i||r!==y||n<8||15<n||e<0||9<e||o<0||_<o)return M(t,b);8===n&&(n=9);var s=new W;return(t.state=s).strm=t,s.wrap=a,s.gzhead=null,s.w_bits=n,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+x-1)/x),s.window=new c.Buf8(2*s.w_size),s.head=new c.Buf16(s.hash_size),s.prev=new c.Buf16(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new c.Buf8(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=e,s.strategy=o,s.method=r,q(t)}u=[new G(0,0,0,0,function(t,e){var r=65535;for(r>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(F(t),0===t.lookahead&&e===l)return A;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+r;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,B(t,!1),0===t.strm.avail_out))return A;if(t.strstart-t.block_start>=t.w_size-k&&(B(t,!1),0===t.strm.avail_out))return A}return t.insert=0,e===h?(B(t,!0),0===t.strm.avail_out?O:P):(t.strstart>t.block_start&&(B(t,!1),t.strm.avail_out),A)}),new G(4,4,8,4,z),new G(4,5,16,8,z),new G(4,6,32,32,z),new G(4,4,16,16,H),new G(8,16,32,32,H),new G(8,16,128,128,H),new G(8,32,128,256,H),new G(32,128,258,1024,H),new G(32,258,258,4096,H)],r.deflateInit=function(t,e){return Y(t,e,y,15,8,0)},r.deflateInit2=Y,r.deflateReset=q,r.deflateResetKeep=Z,r.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?b:(t.state.gzhead=e,m):b},r.deflate=function(t,e){var r,n,i,o;if(!t||!t.state||5<e||e<0)return t?M(t,b):b;if(n=t.state,!t.output||!t.input&&0!==t.avail_in||666===n.status&&e!==h)return M(t,0===t.avail_out?-5:b);if(n.strm=t,r=n.last_flush,n.last_flush=e,n.status===S)if(2===n.wrap)t.adler=0,N(n,31),N(n,139),N(n,8),n.gzhead?(N(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),N(n,255&n.gzhead.time),N(n,n.gzhead.time>>8&255),N(n,n.gzhead.time>>16&255),N(n,n.gzhead.time>>24&255),N(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),N(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(N(n,255&n.gzhead.extra.length),N(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(t.adler=p(t.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(N(n,0),N(n,0),N(n,0),N(n,0),N(n,0),N(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),N(n,3),n.status=I);else{var a=y+(n.w_bits-8<<4)<<8;a|=(2<=n.strategy||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(a|=32),a+=31-a%31,n.status=I,D(n,a),0!==n.strstart&&(D(n,t.adler>>>16),D(n,65535&t.adler)),t.adler=1}if(69===n.status)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>i&&(t.adler=p(t.adler,n.pending_buf,n.pending-i,i)),R(t),i=n.pending,n.pending!==n.pending_buf_size));)N(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(t.adler=p(t.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(t.adler=p(t.adler,n.pending_buf,n.pending-i,i)),R(t),i=n.pending,n.pending===n.pending_buf_size)){o=1;break}N(n,o=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0)}while(0!==o);n.gzhead.hcrc&&n.pending>i&&(t.adler=p(t.adler,n.pending_buf,n.pending-i,i)),0===o&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(t.adler=p(t.adler,n.pending_buf,n.pending-i,i)),R(t),i=n.pending,n.pending===n.pending_buf_size)){o=1;break}N(n,o=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0)}while(0!==o);n.gzhead.hcrc&&n.pending>i&&(t.adler=p(t.adler,n.pending_buf,n.pending-i,i)),0===o&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&R(t),n.pending+2<=n.pending_buf_size&&(N(n,255&t.adler),N(n,t.adler>>8&255),t.adler=0,n.status=I)):n.status=I),0!==n.pending){if(R(t),0===t.avail_out)return n.last_flush=-1,m}else if(0===t.avail_in&&L(e)<=L(r)&&e!==h)return M(t,-5);if(666===n.status&&0!==t.avail_in)return M(t,-5);if(0!==t.avail_in||0!==n.lookahead||e!==l&&666!==n.status){var s=2===n.strategy?function(t,e){for(var r;;){if(0===t.lookahead&&(F(t),0===t.lookahead)){if(e===l)return A;break}if(t.match_length=0,r=f._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(B(t,!1),0===t.strm.avail_out))return A}return t.insert=0,e===h?(B(t,!0),0===t.strm.avail_out?O:P):t.last_lit&&(B(t,!1),0===t.strm.avail_out)?A:T}(n,e):3===n.strategy?function(t,e){for(var r,n,i,o,a=t.window;;){if(t.lookahead<=j){if(F(t),t.lookahead<=j&&e===l)return A;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=x&&0<t.strstart&&(n=a[i=t.strstart-1])===a[++i]&&n===a[++i]&&n===a[++i]){o=t.strstart+j;do{}while(n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&i<o);t.match_length=j-(o-i),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=x?(r=f._tr_tally(t,1,t.match_length-x),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=f._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(B(t,!1),0===t.strm.avail_out))return A}return t.insert=0,e===h?(B(t,!0),0===t.strm.avail_out?O:P):t.last_lit&&(B(t,!1),0===t.strm.avail_out)?A:T}(n,e):u[n.level].func(n,e);if(s!==O&&s!==P||(n.status=666),s===A||s===O)return 0===t.avail_out&&(n.last_flush=-1),m;if(s===T&&(1===e?f._tr_align(n):5!==e&&(f._tr_stored_block(n,0,0,!1),3===e&&(C(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),R(t),0===t.avail_out))return n.last_flush=-1,m}return e!==h?m:n.wrap<=0?1:(2===n.wrap?(N(n,255&t.adler),N(n,t.adler>>8&255),N(n,t.adler>>16&255),N(n,t.adler>>24&255),N(n,255&t.total_in),N(n,t.total_in>>8&255),N(n,t.total_in>>16&255),N(n,t.total_in>>24&255)):(D(n,t.adler>>>16),D(n,65535&t.adler)),R(t),0<n.wrap&&(n.wrap=-n.wrap),0!==n.pending?m:1)},r.deflateEnd=function(t){var e;return t&&t.state?(e=t.state.status)!==S&&69!==e&&73!==e&&91!==e&&103!==e&&e!==I&&666!==e?M(t,b):(t.state=null,e===I?M(t,-3):m):b},r.deflateSetDictionary=function(t,e){var r,n,i,o,a,s,u,f,l=e.length;if(!t||!t.state)return b;if(2===(o=(r=t.state).wrap)||1===o&&r.status!==S||r.lookahead)return b;for(1===o&&(t.adler=d(t.adler,e,l,0)),r.wrap=0,l>=r.w_size&&(0===o&&(C(r.head),r.strstart=0,r.block_start=0,r.insert=0),f=new c.Buf8(r.w_size),c.arraySet(f,e,l-r.w_size,r.w_size,0),e=f,l=r.w_size),a=t.avail_in,s=t.next_in,u=t.input,t.avail_in=l,t.next_in=0,t.input=e,F(r);r.lookahead>=x;){for(n=r.strstart,i=r.lookahead-(x-1);r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+x-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--i;);r.strstart=n,r.lookahead=x-1,F(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=x-1,r.match_available=0,t.next_in=s,t.input=u,t.avail_in=a,r.wrap=o,m},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":179,"./adler32":181,"./crc32":183,"./messages":189,"./trees":190}],185:[function(t,e,r){"use strict";e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],186:[function(t,e,r){"use strict";e.exports=function(t,e){var r,n,i,o,a,s,u,f,l,h,c,d,p,m,b,g,_,y,v,w,E,x,j,k,S;r=t.state,n=t.next_in,k=t.input,i=n+(t.avail_in-5),o=t.next_out,S=t.output,a=o-(e-t.avail_out),s=o+(t.avail_out-257),u=r.dmax,f=r.wsize,l=r.whave,h=r.wnext,c=r.window,d=r.hold,p=r.bits,m=r.lencode,b=r.distcode,g=(1<<r.lenbits)-1,_=(1<<r.distbits)-1;t:do{p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),y=m[d&g];e:for(;;){if(d>>>=v=y>>>24,p-=v,0===(v=y>>>16&255))S[o++]=65535&y;else{if(!(16&v)){if(0==(64&v)){y=m[(65535&y)+(d&(1<<v)-1)];continue e}if(32&v){r.mode=12;break t}t.msg="invalid literal/length code",r.mode=30;break t}w=65535&y,(v&=15)&&(p<v&&(d+=k[n++]<<p,p+=8),w+=d&(1<<v)-1,d>>>=v,p-=v),p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),y=b[d&_];r:for(;;){if(d>>>=v=y>>>24,p-=v,!(16&(v=y>>>16&255))){if(0==(64&v)){y=b[(65535&y)+(d&(1<<v)-1)];continue r}t.msg="invalid distance code",r.mode=30;break t}if(E=65535&y,p<(v&=15)&&(d+=k[n++]<<p,(p+=8)<v&&(d+=k[n++]<<p,p+=8)),u<(E+=d&(1<<v)-1)){t.msg="invalid distance too far back",r.mode=30;break t}if(d>>>=v,p-=v,(v=o-a)<E){if(l<(v=E-v)&&r.sane){t.msg="invalid distance too far back",r.mode=30;break t}if(j=c,(x=0)===h){if(x+=f-v,v<w){for(w-=v;S[o++]=c[x++],--v;);x=o-E,j=S}}else if(h<v){if(x+=f+h-v,(v-=h)<w){for(w-=v;S[o++]=c[x++],--v;);if(x=0,h<w){for(w-=v=h;S[o++]=c[x++],--v;);x=o-E,j=S}}}else if(x+=h-v,v<w){for(w-=v;S[o++]=c[x++],--v;);x=o-E,j=S}for(;2<w;)S[o++]=j[x++],S[o++]=j[x++],S[o++]=j[x++],w-=3;w&&(S[o++]=j[x++],1<w&&(S[o++]=j[x++]))}else{for(x=o-E;S[o++]=S[x++],S[o++]=S[x++],S[o++]=S[x++],2<(w-=3););w&&(S[o++]=S[x++],1<w&&(S[o++]=S[x++]))}break}}break}}while(n<i&&o<s);n-=w=p>>3,d&=(1<<(p-=w<<3))-1,t.next_in=n,t.next_out=o,t.avail_in=n<i?i-n+5:5-(n-i),t.avail_out=o<s?s-o+257:257-(o-s),r.hold=d,r.bits=p}},{}],187:[function(t,e,r){"use strict";var T=t("../utils/common"),O=t("./adler32"),P=t("./crc32"),M=t("./inffast"),L=t("./inftrees"),C=1,R=2,B=0,N=-2,D=1,n=852,i=592;function U(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function o(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new T.Buf16(320),this.work=new T.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function a(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=D,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new T.Buf32(n),e.distcode=e.distdyn=new T.Buf32(i),e.sane=1,e.back=-1,B):N}function s(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,a(t)):N}function u(t,e){var r,n;return t&&t.state?(n=t.state,e<0?(r=0,e=-e):(r=1+(e>>4),e<48&&(e&=15)),e&&(e<8||15<e)?N:(null!==n.window&&n.wbits!==e&&(n.window=null),n.wrap=r,n.wbits=e,s(t))):N}function f(t,e){var r,n;return t?(n=new o,(t.state=n).window=null,(r=u(t,e))!==B&&(t.state=null),r):N}var l,h,c=!0;function F(t){if(c){var e;for(l=new T.Buf32(512),h=new T.Buf32(32),e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(L(C,t.lens,0,288,l,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;L(R,t.lens,0,32,h,0,t.work,{bits:5}),c=!1}t.lencode=l,t.lenbits=9,t.distcode=h,t.distbits=5}function z(t,e,r,n){var i,o=t.state;return null===o.window&&(o.wsize=1<<o.wbits,o.wnext=0,o.whave=0,o.window=new T.Buf8(o.wsize)),n>=o.wsize?(T.arraySet(o.window,e,r-o.wsize,o.wsize,0),o.wnext=0,o.whave=o.wsize):(n<(i=o.wsize-o.wnext)&&(i=n),T.arraySet(o.window,e,r-n,i,o.wnext),(n-=i)?(T.arraySet(o.window,e,r-n,n,0),o.wnext=n,o.whave=o.wsize):(o.wnext+=i,o.wnext===o.wsize&&(o.wnext=0),o.whave<o.wsize&&(o.whave+=i))),0}r.inflateReset=s,r.inflateReset2=u,r.inflateResetKeep=a,r.inflateInit=function(t){return f(t,15)},r.inflateInit2=f,r.inflate=function(t,e){var r,n,i,o,a,s,u,f,l,h,c,d,p,m,b,g,_,y,v,w,E,x,j,k,S=0,I=new T.Buf8(4),A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return N;12===(r=t.state).mode&&(r.mode=13),a=t.next_out,i=t.output,u=t.avail_out,o=t.next_in,n=t.input,s=t.avail_in,f=r.hold,l=r.bits,h=s,c=u,x=B;t:for(;;)switch(r.mode){case D:if(0===r.wrap){r.mode=13;break}for(;l<16;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(2&r.wrap&&35615===f){I[r.check=0]=255&f,I[1]=f>>>8&255,r.check=P(r.check,I,2,0),l=f=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&f)<<8)+(f>>8))%31){t.msg="incorrect header check",r.mode=30;break}if(8!=(15&f)){t.msg="unknown compression method",r.mode=30;break}if(l-=4,E=8+(15&(f>>>=4)),0===r.wbits)r.wbits=E;else if(E>r.wbits){t.msg="invalid window size",r.mode=30;break}r.dmax=1<<E,t.adler=r.check=1,r.mode=512&f?10:12,l=f=0;break;case 2:for(;l<16;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(r.flags=f,8!=(255&r.flags)){t.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){t.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=f>>8&1),512&r.flags&&(I[0]=255&f,I[1]=f>>>8&255,r.check=P(r.check,I,2,0)),l=f=0,r.mode=3;case 3:for(;l<32;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}r.head&&(r.head.time=f),512&r.flags&&(I[0]=255&f,I[1]=f>>>8&255,I[2]=f>>>16&255,I[3]=f>>>24&255,r.check=P(r.check,I,4,0)),l=f=0,r.mode=4;case 4:for(;l<16;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}r.head&&(r.head.xflags=255&f,r.head.os=f>>8),512&r.flags&&(I[0]=255&f,I[1]=f>>>8&255,r.check=P(r.check,I,2,0)),l=f=0,r.mode=5;case 5:if(1024&r.flags){for(;l<16;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}r.length=f,r.head&&(r.head.extra_len=f),512&r.flags&&(I[0]=255&f,I[1]=f>>>8&255,r.check=P(r.check,I,2,0)),l=f=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&(s<(d=r.length)&&(d=s),d&&(r.head&&(E=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),T.arraySet(r.head.extra,n,o,d,E)),512&r.flags&&(r.check=P(r.check,n,d,o)),s-=d,o+=d,r.length-=d),r.length))break t;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===s)break t;for(d=0;E=n[o+d++],r.head&&E&&r.length<65536&&(r.head.name+=String.fromCharCode(E)),E&&d<s;);if(512&r.flags&&(r.check=P(r.check,n,d,o)),s-=d,o+=d,E)break t}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===s)break t;for(d=0;E=n[o+d++],r.head&&E&&r.length<65536&&(r.head.comment+=String.fromCharCode(E)),E&&d<s;);if(512&r.flags&&(r.check=P(r.check,n,d,o)),s-=d,o+=d,E)break t}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;l<16;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(f!==(65535&r.check)){t.msg="header crc mismatch",r.mode=30;break}l=f=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),t.adler=r.check=0,r.mode=12;break;case 10:for(;l<32;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}t.adler=r.check=U(f),l=f=0,r.mode=11;case 11:if(0===r.havedict)return t.next_out=a,t.avail_out=u,t.next_in=o,t.avail_in=s,r.hold=f,r.bits=l,2;t.adler=r.check=1,r.mode=12;case 12:if(5===e||6===e)break t;case 13:if(r.last){f>>>=7&l,l-=7&l,r.mode=27;break}for(;l<3;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}switch(r.last=1&f,l-=1,3&(f>>>=1)){case 0:r.mode=14;break;case 1:if(F(r),r.mode=20,6!==e)break;f>>>=2,l-=2;break t;case 2:r.mode=17;break;case 3:t.msg="invalid block type",r.mode=30}f>>>=2,l-=2;break;case 14:for(f>>>=7&l,l-=7&l;l<32;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if((65535&f)!=(f>>>16^65535)){t.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&f,l=f=0,r.mode=15,6===e)break t;case 15:r.mode=16;case 16:if(d=r.length){if(s<d&&(d=s),u<d&&(d=u),0===d)break t;T.arraySet(i,n,o,d,a),s-=d,o+=d,u-=d,a+=d,r.length-=d;break}r.mode=12;break;case 17:for(;l<14;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(r.nlen=257+(31&f),f>>>=5,l-=5,r.ndist=1+(31&f),f>>>=5,l-=5,r.ncode=4+(15&f),f>>>=4,l-=4,286<r.nlen||30<r.ndist){t.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;l<3;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}r.lens[A[r.have++]]=7&f,f>>>=3,l-=3}for(;r.have<19;)r.lens[A[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,j={bits:r.lenbits},x=L(0,r.lens,0,19,r.lencode,0,r.work,j),r.lenbits=j.bits,x){t.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;g=(S=r.lencode[f&(1<<r.lenbits)-1])>>>16&255,_=65535&S,!((b=S>>>24)<=l);){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(_<16)f>>>=b,l-=b,r.lens[r.have++]=_;else{if(16===_){for(k=b+2;l<k;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(f>>>=b,l-=b,0===r.have){t.msg="invalid bit length repeat",r.mode=30;break}E=r.lens[r.have-1],d=3+(3&f),f>>>=2,l-=2}else if(17===_){for(k=b+3;l<k;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}l-=b,E=0,d=3+(7&(f>>>=b)),f>>>=3,l-=3}else{for(k=b+7;l<k;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}l-=b,E=0,d=11+(127&(f>>>=b)),f>>>=7,l-=7}if(r.have+d>r.nlen+r.ndist){t.msg="invalid bit length repeat",r.mode=30;break}for(;d--;)r.lens[r.have++]=E}}if(30===r.mode)break;if(0===r.lens[256]){t.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,j={bits:r.lenbits},x=L(C,r.lens,0,r.nlen,r.lencode,0,r.work,j),r.lenbits=j.bits,x){t.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,j={bits:r.distbits},x=L(R,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,j),r.distbits=j.bits,x){t.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===e)break t;case 20:r.mode=21;case 21:if(6<=s&&258<=u){t.next_out=a,t.avail_out=u,t.next_in=o,t.avail_in=s,r.hold=f,r.bits=l,M(t,c),a=t.next_out,i=t.output,u=t.avail_out,o=t.next_in,n=t.input,s=t.avail_in,f=r.hold,l=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;g=(S=r.lencode[f&(1<<r.lenbits)-1])>>>16&255,_=65535&S,!((b=S>>>24)<=l);){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(g&&0==(240&g)){for(y=b,v=g,w=_;g=(S=r.lencode[w+((f&(1<<y+v)-1)>>y)])>>>16&255,_=65535&S,!(y+(b=S>>>24)<=l);){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}f>>>=y,l-=y,r.back+=y}if(f>>>=b,l-=b,r.back+=b,r.length=_,0===g){r.mode=26;break}if(32&g){r.back=-1,r.mode=12;break}if(64&g){t.msg="invalid literal/length code",r.mode=30;break}r.extra=15&g,r.mode=22;case 22:if(r.extra){for(k=r.extra;l<k;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}r.length+=f&(1<<r.extra)-1,f>>>=r.extra,l-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;g=(S=r.distcode[f&(1<<r.distbits)-1])>>>16&255,_=65535&S,!((b=S>>>24)<=l);){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(0==(240&g)){for(y=b,v=g,w=_;g=(S=r.distcode[w+((f&(1<<y+v)-1)>>y)])>>>16&255,_=65535&S,!(y+(b=S>>>24)<=l);){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}f>>>=y,l-=y,r.back+=y}if(f>>>=b,l-=b,r.back+=b,64&g){t.msg="invalid distance code",r.mode=30;break}r.offset=_,r.extra=15&g,r.mode=24;case 24:if(r.extra){for(k=r.extra;l<k;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}r.offset+=f&(1<<r.extra)-1,f>>>=r.extra,l-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){t.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===u)break t;if(d=c-u,r.offset>d){if((d=r.offset-d)>r.whave&&r.sane){t.msg="invalid distance too far back",r.mode=30;break}p=d>r.wnext?(d-=r.wnext,r.wsize-d):r.wnext-d,d>r.length&&(d=r.length),m=r.window}else m=i,p=a-r.offset,d=r.length;for(u<d&&(d=u),u-=d,r.length-=d;i[a++]=m[p++],--d;);0===r.length&&(r.mode=21);break;case 26:if(0===u)break t;i[a++]=r.length,u--,r.mode=21;break;case 27:if(r.wrap){for(;l<32;){if(0===s)break t;s--,f|=n[o++]<<l,l+=8}if(c-=u,t.total_out+=c,r.total+=c,c&&(t.adler=r.check=r.flags?P(r.check,i,c,a-c):O(r.check,i,c,a-c)),c=u,(r.flags?f:U(f))!==r.check){t.msg="incorrect data check",r.mode=30;break}l=f=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;l<32;){if(0===s)break t;s--,f+=n[o++]<<l,l+=8}if(f!==(4294967295&r.total)){t.msg="incorrect length check",r.mode=30;break}l=f=0}r.mode=29;case 29:x=1;break t;case 30:x=-3;break t;case 31:return-4;case 32:default:return N}return t.next_out=a,t.avail_out=u,t.next_in=o,t.avail_in=s,r.hold=f,r.bits=l,(r.wsize||c!==t.avail_out&&r.mode<30&&(r.mode<27||4!==e))&&z(t,t.output,t.next_out,c-t.avail_out)?(r.mode=31,-4):(h-=t.avail_in,c-=t.avail_out,t.total_in+=h,t.total_out+=c,r.total+=c,r.wrap&&c&&(t.adler=r.check=r.flags?P(r.check,i,c,t.next_out-c):O(r.check,i,c,t.next_out-c)),t.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0===h&&0===c||4===e)&&x===B&&(x=-5),x)},r.inflateEnd=function(t){if(!t||!t.state)return N;var e=t.state;return e.window&&(e.window=null),t.state=null,B},r.inflateGetHeader=function(t,e){var r;return t&&t.state?0==(2&(r=t.state).wrap)?N:((r.head=e).done=!1,B):N},r.inflateSetDictionary=function(t,e){var r,n=e.length;return t&&t.state?0!==(r=t.state).wrap&&11!==r.mode?N:11===r.mode&&O(1,e,n,0)!==r.check?-3:z(t,e,n,n)?(r.mode=31,-4):(r.havedict=1,B):N},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":179,"./adler32":181,"./crc32":183,"./inffast":186,"./inftrees":188}],188:[function(t,e,r){"use strict";var C=t("../utils/common"),R=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],B=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],N=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],D=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(t,e,r,n,i,o,a,s){var u,f,l,h,c,d,p,m,b,g=s.bits,_=0,y=0,v=0,w=0,E=0,x=0,j=0,k=0,S=0,I=0,A=null,T=0,O=new C.Buf16(16),P=new C.Buf16(16),M=null,L=0;for(_=0;_<=15;_++)O[_]=0;for(y=0;y<n;y++)O[e[r+y]]++;for(E=g,w=15;1<=w&&0===O[w];w--);if(w<E&&(E=w),0===w)return i[o++]=20971520,i[o++]=20971520,s.bits=1,0;for(v=1;v<w&&0===O[v];v++);for(E<v&&(E=v),_=k=1;_<=15;_++)if(k<<=1,(k-=O[_])<0)return-1;if(0<k&&(0===t||1!==w))return-1;for(P[1]=0,_=1;_<15;_++)P[_+1]=P[_]+O[_];for(y=0;y<n;y++)0!==e[r+y]&&(a[P[e[r+y]]++]=y);if(d=0===t?(A=M=a,19):1===t?(A=R,T-=257,M=B,L-=257,256):(A=N,M=D,-1),_=v,c=o,j=y=I=0,l=-1,h=(S=1<<(x=E))-1,1===t&&852<S||2===t&&592<S)return 1;for(;;){for(p=_-j,b=a[y]<d?(m=0,a[y]):a[y]>d?(m=M[L+a[y]],A[T+a[y]]):(m=96,0),u=1<<_-j,v=f=1<<x;i[c+(I>>j)+(f-=u)]=p<<24|m<<16|b|0,0!==f;);for(u=1<<_-1;I&u;)u>>=1;if(0!==u?(I&=u-1,I+=u):I=0,y++,0==--O[_]){if(_===w)break;_=e[r+a[y]]}if(E<_&&(I&h)!==l){for(0===j&&(j=E),c+=v,k=1<<(x=_-j);x+j<w&&!((k-=O[x+j])<=0);)x++,k<<=1;if(S+=1<<x,1===t&&852<S||2===t&&592<S)return 1;i[l=I&h]=E<<24|x<<16|c-o|0}}return 0!==I&&(i[c+I]=_-j<<24|64<<16|0),s.bits=E,0}},{"../utils/common":179}],189:[function(t,e,r){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],190:[function(t,e,r){"use strict";var u=t("../utils/common"),s=0,f=1;function n(t){for(var e=t.length;0<=--e;)t[e]=0}var l=0,a=29,h=256,c=h+1+a,d=30,p=19,b=2*c+1,g=15,i=16,m=7,_=256,y=16,v=17,w=18,E=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],x=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],j=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],k=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S=new Array(2*(c+2));n(S);var I=new Array(2*d);n(I);var A=new Array(512);n(A);var T=new Array(256);n(T);var O=new Array(a);n(O);var P,M,L,C=new Array(d);function R(t,e,r,n,i){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=t&&t.length}function o(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function B(t){return t<256?A[t]:A[256+(t>>>7)]}function N(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function D(t,e,r){t.bi_valid>i-r?(t.bi_buf|=e<<t.bi_valid&65535,N(t,t.bi_buf),t.bi_buf=e>>i-t.bi_valid,t.bi_valid+=r-i):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)}function U(t,e,r){D(t,r[2*e],r[2*e+1])}function F(t,e){for(var r=0;r|=1&t,t>>>=1,r<<=1,0<--e;);return r>>>1}function z(t,e,r){var n,i,o=new Array(g+1),a=0;for(n=1;n<=g;n++)o[n]=a=a+r[n-1]<<1;for(i=0;i<=e;i++){var s=t[2*i+1];0!==s&&(t[2*i]=F(o[s]++,s))}}function H(t){var e;for(e=0;e<c;e++)t.dyn_ltree[2*e]=0;for(e=0;e<d;e++)t.dyn_dtree[2*e]=0;for(e=0;e<p;e++)t.bl_tree[2*e]=0;t.dyn_ltree[2*_]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function G(t){8<t.bi_valid?N(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function W(t,e,r,n){var i=2*e,o=2*r;return t[i]<t[o]||t[i]===t[o]&&n[e]<=n[r]}function Z(t,e,r){for(var n=t.heap[r],i=r<<1;i<=t.heap_len&&(i<t.heap_len&&W(e,t.heap[i+1],t.heap[i],t.depth)&&i++,!W(e,n,t.heap[i],t.depth));)t.heap[r]=t.heap[i],r=i,i<<=1;t.heap[r]=n}function q(t,e,r){var n,i,o,a,s=0;if(0!==t.last_lit)for(;n=t.pending_buf[t.d_buf+2*s]<<8|t.pending_buf[t.d_buf+2*s+1],i=t.pending_buf[t.l_buf+s],s++,0===n?U(t,i,e):(U(t,(o=T[i])+h+1,e),0!==(a=E[o])&&D(t,i-=O[o],a),U(t,o=B(--n),r),0!==(a=x[o])&&D(t,n-=C[o],a)),s<t.last_lit;);U(t,_,e)}function Y(t,e){var r,n,i,o=e.dyn_tree,a=e.stat_desc.static_tree,s=e.stat_desc.has_stree,u=e.stat_desc.elems,f=-1;for(t.heap_len=0,t.heap_max=b,r=0;r<u;r++)0!==o[2*r]?(t.heap[++t.heap_len]=f=r,t.depth[r]=0):o[2*r+1]=0;for(;t.heap_len<2;)o[2*(i=t.heap[++t.heap_len]=f<2?++f:0)]=1,t.depth[i]=0,t.opt_len--,s&&(t.static_len-=a[2*i+1]);for(e.max_code=f,r=t.heap_len>>1;1<=r;r--)Z(t,o,r);for(i=u;r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],Z(t,o,1),n=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=n,o[2*i]=o[2*r]+o[2*n],t.depth[i]=(t.depth[r]>=t.depth[n]?t.depth[r]:t.depth[n])+1,o[2*r+1]=o[2*n+1]=i,t.heap[1]=i++,Z(t,o,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t,e){var r,n,i,o,a,s,u=e.dyn_tree,f=e.max_code,l=e.stat_desc.static_tree,h=e.stat_desc.has_stree,c=e.stat_desc.extra_bits,d=e.stat_desc.extra_base,p=e.stat_desc.max_length,m=0;for(o=0;o<=g;o++)t.bl_count[o]=0;for(u[2*t.heap[t.heap_max]+1]=0,r=t.heap_max+1;r<b;r++)p<(o=u[2*u[2*(n=t.heap[r])+1]+1]+1)&&(o=p,m++),u[2*n+1]=o,f<n||(t.bl_count[o]++,a=0,d<=n&&(a=c[n-d]),s=u[2*n],t.opt_len+=s*(o+a),h&&(t.static_len+=s*(l[2*n+1]+a)));if(0!==m){do{for(o=p-1;0===t.bl_count[o];)o--;t.bl_count[o]--,t.bl_count[o+1]+=2,t.bl_count[p]--,m-=2}while(0<m);for(o=p;0!==o;o--)for(n=t.bl_count[o];0!==n;)f<(i=t.heap[--r])||(u[2*i+1]!==o&&(t.opt_len+=(o-u[2*i+1])*u[2*i],u[2*i+1]=o),n--)}}(t,e),z(o,f,t.bl_count)}function V(t,e,r){var n,i,o=-1,a=e[1],s=0,u=7,f=4;for(0===a&&(u=138,f=3),e[2*(r+1)+1]=65535,n=0;n<=r;n++)i=a,a=e[2*(n+1)+1],++s<u&&i===a||(s<f?t.bl_tree[2*i]+=s:0!==i?(i!==o&&t.bl_tree[2*i]++,t.bl_tree[2*y]++):s<=10?t.bl_tree[2*v]++:t.bl_tree[2*w]++,o=i,f=(s=0)===a?(u=138,3):i===a?(u=6,3):(u=7,4))}function X(t,e,r){var n,i,o=-1,a=e[1],s=0,u=7,f=4;for(0===a&&(u=138,f=3),n=0;n<=r;n++)if(i=a,a=e[2*(n+1)+1],!(++s<u&&i===a)){if(s<f)for(;U(t,i,t.bl_tree),0!=--s;);else 0!==i?(i!==o&&(U(t,i,t.bl_tree),s--),U(t,y,t.bl_tree),D(t,s-3,2)):s<=10?(U(t,v,t.bl_tree),D(t,s-3,3)):(U(t,w,t.bl_tree),D(t,s-11,7));o=i,f=(s=0)===a?(u=138,3):i===a?(u=6,3):(u=7,4)}}n(C);var J=!1;function K(t,e,r,n){var i,o,a,s;D(t,(l<<1)+(n?1:0),3),o=e,a=r,s=!0,G(i=t),s&&(N(i,a),N(i,~a)),u.arraySet(i.pending_buf,i.window,o,a,i.pending),i.pending+=a}r._tr_init=function(t){J||(function(){var t,e,r,n,i,o=new Array(g+1);for(n=r=0;n<a-1;n++)for(O[n]=r,t=0;t<1<<E[n];t++)T[r++]=n;for(T[r-1]=n,n=i=0;n<16;n++)for(C[n]=i,t=0;t<1<<x[n];t++)A[i++]=n;for(i>>=7;n<d;n++)for(C[n]=i<<7,t=0;t<1<<x[n]-7;t++)A[256+i++]=n;for(e=0;e<=g;e++)o[e]=0;for(t=0;t<=143;)S[2*t+1]=8,t++,o[8]++;for(;t<=255;)S[2*t+1]=9,t++,o[9]++;for(;t<=279;)S[2*t+1]=7,t++,o[7]++;for(;t<=287;)S[2*t+1]=8,t++,o[8]++;for(z(S,c+1,o),t=0;t<d;t++)I[2*t+1]=5,I[2*t]=F(t,5);P=new R(S,E,h+1,c,g),M=new R(I,x,0,d,g),L=new R(new Array(0),j,0,p,m)}(),J=!0),t.l_desc=new o(t.dyn_ltree,P),t.d_desc=new o(t.dyn_dtree,M),t.bl_desc=new o(t.bl_tree,L),t.bi_buf=0,t.bi_valid=0,H(t)},r._tr_stored_block=K,r._tr_flush_block=function(t,e,r,n){var i,o,a=0;0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,r=4093624447;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return s;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return f;for(e=32;e<h;e++)if(0!==t.dyn_ltree[2*e])return f;return s}(t)),Y(t,t.l_desc),Y(t,t.d_desc),a=function(t){var e;for(V(t,t.dyn_ltree,t.l_desc.max_code),V(t,t.dyn_dtree,t.d_desc.max_code),Y(t,t.bl_desc),e=p-1;3<=e&&0===t.bl_tree[2*k[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(o=t.static_len+3+7>>>3)<=i&&(i=o)):i=o=r+5,r+4<=i&&-1!==e?K(t,e,r,n):4===t.strategy||o===i?(D(t,2+(n?1:0),3),q(t,S,I)):(D(t,4+(n?1:0),3),function(t,e,r,n){var i;for(D(t,e-257,5),D(t,r-1,5),D(t,n-4,4),i=0;i<n;i++)D(t,t.bl_tree[2*k[i]+1],3);X(t,t.dyn_ltree,e-1),X(t,t.dyn_dtree,r-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,a+1),q(t,t.dyn_ltree,t.dyn_dtree)),H(t),n&&G(t)},r._tr_tally=function(t,e,r){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&r,t.last_lit++,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(T[r]+h+1)]++,t.dyn_dtree[2*B(e)]++),t.last_lit===t.lit_bufsize-1},r._tr_align=function(t){var e;D(t,2,3),U(t,_,S),16===(e=t).bi_valid?(N(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}},{"../utils/common":179}],191:[function(t,e,r){"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],192:[function(t,e,r){function o(t,e){if(!(t=t.replace(/\t+/g," ").trim()))return null;var r=t.indexOf(" ");if(-1===r)throw new Error("no named row at line "+e);var n=t.substring(0,r);t=(t=(t=(t=t.substring(r+1)).replace(/letter=[\'\"]\S+[\'\"]/gi,"")).split("=")).map(function(t){return t.trim().match(/(".*?"|[^"\s]+)+(?=\s*|\s*$)/g)});for(var i=[],o=0;o<t.length;o++){var a=t[o];0===o?i.push({key:a[0],data:""}):o===t.length-1?i[i.length-1].data=u(a[0]):(i[i.length-1].data=u(a[0]),i.push({key:a[1],data:""}))}var s={key:n,data:{}};return i.forEach(function(t){s.data[t.key]=t.data}),s}function u(t){return t&&0!==t.length?0===t.indexOf('"')||0===t.indexOf("'")?t.substring(1,t.length-1):-1!==t.indexOf(",")?t.split(",").map(function(t){return parseInt(t,10)}):parseInt(t,10):""}e.exports=function(t){if(!t)throw new Error("no data provided");var e={pages:[],chars:[],kernings:[]},r=(t=t.toString().trim()).split(/\r\n?|\n/g);if(0===r.length)throw new Error("no data in BMFont file");for(var n=0;n<r.length;n++){var i=o(r[n],n);if(i)if("page"===i.key){if("number"!=typeof i.data.id)throw new Error("malformed file at line "+n+" -- needs page id=N");if("string"!=typeof i.data.file)throw new Error("malformed file at line "+n+' -- needs page file="path"');e.pages[i.data.id]=i.data.file}else"chars"===i.key||"kernings"===i.key||("char"===i.key?e.chars.push(i.data):"kerning"===i.key?e.kernings.push(i.data):e[i.key]=i.data)}return e}},{}],193:[function(t,e,r){var i=[66,77,70];function o(t,e,r){if(r>e.length-1)return 0;var n=e.readUInt8(r++),i=e.readInt32LE(r);switch(r+=4,n){case 1:t.info=function(t,e){var r={};r.size=t.readInt16LE(e);var n=t.readUInt8(e+2);r.smooth=n>>7&1,r.unicode=n>>6&1,r.italic=n>>5&1,r.bold=n>>4&1,n>>3&1&&(r.fixedHeight=1);return r.charset=t.readUInt8(e+3)||"",r.stretchH=t.readUInt16LE(e+4),r.aa=t.readUInt8(e+6),r.padding=[t.readInt8(e+7),t.readInt8(e+8),t.readInt8(e+9),t.readInt8(e+10)],r.spacing=[t.readInt8(e+11),t.readInt8(e+12)],r.outline=t.readUInt8(e+13),r.face=(i=t,o=e+14,u(i,o).toString("utf8")),r;var i,o}(e,r);break;case 2:t.common=function(t,e){var r={};r.lineHeight=t.readUInt16LE(e),r.base=t.readUInt16LE(e+2),r.scaleW=t.readUInt16LE(e+4),r.scaleH=t.readUInt16LE(e+6),r.pages=t.readUInt16LE(e+8);t.readUInt8(e+10);return r.packed=0,r.alphaChnl=t.readUInt8(e+11),r.redChnl=t.readUInt8(e+12),r.greenChnl=t.readUInt8(e+13),r.blueChnl=t.readUInt8(e+14),r}(e,r);break;case 3:t.pages=function(t,e,r){for(var n=[],i=u(t,e),o=i.length+1,a=r/o,s=0;s<a;s++)n[s]=t.slice(e,e+i.length).toString("utf8"),e+=o;return n}(e,r,i);break;case 4:t.chars=function(t,e,r){for(var n=[],i=r/20,o=0;o<i;o++){var a={},s=20*o;a.id=t.readUInt32LE(e+0+s),a.x=t.readUInt16LE(e+4+s),a.y=t.readUInt16LE(e+6+s),a.width=t.readUInt16LE(e+8+s),a.height=t.readUInt16LE(e+10+s),a.xoffset=t.readInt16LE(e+12+s),a.yoffset=t.readInt16LE(e+14+s),a.xadvance=t.readInt16LE(e+16+s),a.page=t.readUInt8(e+18+s),a.chnl=t.readUInt8(e+19+s),n[o]=a}return n}(e,r,i);break;case 5:t.kernings=function(t,e,r){for(var n=[],i=r/10,o=0;o<i;o++){var a={},s=10*o;a.first=t.readUInt32LE(e+0+s),a.second=t.readUInt32LE(e+4+s),a.amount=t.readInt16LE(e+8+s),n[o]=a}return n}(e,r,i)}return 5+i}function u(t,e){for(var r=e;r<t.length&&0!==t[r];r++);return t.slice(e,r)}e.exports=function(r){if(r.length<6)throw new Error("invalid buffer length for BMFont");if(!i.every(function(t,e){return r.readUInt8(e)===t}))throw new Error("BMFont missing BMF byte header");var t=3;if(3<r.readUInt8(t++))throw new Error("Only supports BMFont Binary v3 (BMFont App v1.10)");for(var e={kernings:[],chars:[]},n=0;n<5;n++)t+=o(e,r,t);return e}},{}],194:[function(t,e,r){var f=t("./parse-attribs"),l=t("xml-parse-from-string"),n={scaleh:"scaleH",scalew:"scaleW",stretchh:"stretchH",lineheight:"lineHeight",alphachnl:"alphaChnl",redchnl:"redChnl",greenchnl:"greenChnl",bluechnl:"blueChnl"};function h(t){return function(t){for(var e=[],r=0;r<t.attributes.length;r++)e.push(t.attributes[r]);return e}(t).reduce(function(t,e){var r;return t[(r=e.nodeName,n[r.toLowerCase()]||r)]=e.nodeValue,t},{})}e.exports=function(t){t=t.toString();var a=l(t),s={pages:[],chars:[],kernings:[]};["info","common"].forEach(function(t){var e=a.getElementsByTagName(t)[0];e&&(s[t]=f(h(e)))});var e=a.getElementsByTagName("pages")[0];if(!e)throw new Error("malformed file -- no <pages> element");for(var r=e.getElementsByTagName("page"),n=0;n<r.length;n++){var i=r[n],o=parseInt(i.getAttribute("id"),10),u=i.getAttribute("file");if(isNaN(o))throw new Error('malformed file -- page "id" attribute is NaN');if(!u)throw new Error('malformed file -- needs page "file" attribute');s.pages[parseInt(o,10)]=u}return["chars","kernings"].forEach(function(t){var e=a.getElementsByTagName(t)[0];if(e)for(var r=t.substring(0,t.length-1),n=e.getElementsByTagName(r),i=0;i<n.length;i++){var o=n[i];s[t].push(f(h(o)))}}),s}},{"./parse-attribs":195,"xml-parse-from-string":260}],195:[function(t,e,r){var n="chasrset";e.exports=function(t){for(var e in n in t&&(t.charset=t[n],delete t[n]),t)"face"!==e&&"charset"!==e&&(t[e]="padding"===e||"spacing"===e?t[e].split(",").map(function(t){return parseInt(t,10)}):parseInt(t[e],10));return t}},{}],196:[function(t,e,r){var a=t("trim"),n=t("for-each");e.exports=function(t){if(!t)return{};var o={};return n(a(t).split("\n"),function(t){var e,r=t.indexOf(":"),n=a(t.slice(0,r)).toLowerCase(),i=a(t.slice(r+1));void 0===o[n]?o[n]=i:(e=o[n],"[object Array]"===Object.prototype.toString.call(e)?o[n].push(i):o[n]=[o[n],i])}),o}},{"for-each":162,trim:252}],197:[function(t,e,f){(function(i){function o(t,e){for(var r=0,n=t.length-1;0<=n;n--){var i=t[n];"."===i?t.splice(n,1):".."===i?(t.splice(n,1),r++):r&&(t.splice(n,1),r--)}if(e)for(;r--;r)t.unshift("..");return t}function a(t,e){if(t.filter)return t.filter(e);for(var r=[],n=0;n<t.length;n++)e(t[n],n,t)&&r.push(t[n]);return r}f.resolve=function(){for(var t="",e=!1,r=arguments.length-1;-1<=r&&!e;r--){var n=0<=r?arguments[r]:i.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");n&&(t=n+"/"+t,e="/"===n.charAt(0))}return(e?"/":"")+(t=o(a(t.split("/"),function(t){return!!t}),!e).join("/"))||"."},f.normalize=function(t){var e=f.isAbsolute(t),r="/"===n(t,-1);return(t=o(a(t.split("/"),function(t){return!!t}),!e).join("/"))||e||(t="."),t&&r&&(t+="/"),(e?"/":"")+t},f.isAbsolute=function(t){return"/"===t.charAt(0)},f.join=function(){var t=Array.prototype.slice.call(arguments,0);return f.normalize(a(t,function(t,e){if("string"!=typeof t)throw new TypeError("Arguments to path.join must be strings");return t}).join("/"))},f.relative=function(t,e){function r(t){for(var e=0;e<t.length&&""===t[e];e++);for(var r=t.length-1;0<=r&&""===t[r];r--);return r<e?[]:t.slice(e,r-e+1)}t=f.resolve(t).substr(1),e=f.resolve(e).substr(1);for(var n=r(t.split("/")),i=r(e.split("/")),o=Math.min(n.length,i.length),a=o,s=0;s<o;s++)if(n[s]!==i[s]){a=s;break}var u=[];for(s=a;s<n.length;s++)u.push("..");return(u=u.concat(i.slice(a))).join("/")},f.sep="/",f.delimiter=":",f.dirname=function(t){if("string"!=typeof t&&(t+=""),0===t.length)return".";for(var e=t.charCodeAt(0),r=47===e,n=-1,i=!0,o=t.length-1;1<=o;--o)if(47===(e=t.charCodeAt(o))){if(!i){n=o;break}}else i=!1;return-1===n?r?"/":".":r&&1===n?"/":t.slice(0,n)},f.basename=function(t,e){var r=function(t){"string"!=typeof t&&(t+="");var e,r=0,n=-1,i=!0;for(e=t.length-1;0<=e;--e)if(47===t.charCodeAt(e)){if(!i){r=e+1;break}}else-1===n&&(i=!1,n=e+1);return-1===n?"":t.slice(r,n)}(t);return e&&r.substr(-1*e.length)===e&&(r=r.substr(0,r.length-e.length)),r},f.extname=function(t){"string"!=typeof t&&(t+="");for(var e=-1,r=0,n=-1,i=!0,o=0,a=t.length-1;0<=a;--a){var s=t.charCodeAt(a);if(47===s){if(i)continue;r=a+1;break}-1===n&&(i=!1,n=a+1),46===s?-1===e?e=a:1!==o&&(o=1):-1!==e&&(o=-1)}return-1===e||-1===n||0===o||1===o&&e===n-1&&e===r+1?"":t.slice(e,n)};var n="b"==="ab".substr(-1)?function(t,e,r){return t.substr(e,r)}:function(t,e,r){return e<0&&(e=t.length+e),t.substr(e,r)}}).call(this,t("_process"))},{_process:223}],198:[function(e,r,t){(function(u){"use strict";var f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l=e("http"),h=e("https"),c=e("url"),d=e("querystring"),p=e("zlib"),t=e("util"),i=function(t,r){if("string"!=typeof t&&!t.hasOwnProperty("url"))throw new Error("Missing url option from options for request method.");var e="object"===(void 0===t?"undefined":f(t))?c.parse(t.url):c.parse(t),n={hostname:e.hostname,port:e.port||("http:"===e.protocol.toLowerCase()?80:443),path:e.path,method:"GET",headers:{},auth:e.auth||null,parse:"none",stream:!1};if("object"===(void 0===t?"undefined":f(t))&&(n=Object.assign(n,t)),n.port=Number(n.port),n.hasOwnProperty("timeout")&&delete n.timeout,!0===n.compressed&&(n.headers["accept-encoding"]="gzip, deflate"),t.hasOwnProperty("form")){if("object"!==f(t.form))throw new Error("phin 'form' option must be of type Object if present.");var i=d.stringify(t.form);n.headers["Content-Type"]="application/x-www-form-urlencoded",n.headers["Content-Length"]=u.byteLength(i),t.data=i}var o=void 0,a=function(e){var t=e;!0===n.compressed&&("gzip"===e.headers["content-encoding"]?t=e.pipe(p.createGunzip()):"deflate"===e.headers["content-encoding"]&&(t=e.pipe(p.createInflate()))),!0===n.stream?(e.stream=t,r(null,e)):(e.body=new u([]),t.on("data",function(t){e.body=u.concat([e.body,t])}),t.on("end",function(){if(r){if("json"===n.parse)try{e.body=JSON.parse(e.body.toString())}catch(t){return void r("Invalid JSON received.",e)}r(null,e)}}))};switch(e.protocol.toLowerCase()){case"http:":o=l.request(n,a);break;case"https:":o=h.request(n,a);break;default:return void(r&&r(new Error("Invalid / unknown URL protocol. Expected HTTP or HTTPS."),null))}if("number"==typeof t.timeout&&o.setTimeout(t.timeout,function(){o.abort(),r(new Error("Timeout has been reached."),null),r=null}),o.on("error",function(t){r&&r(t,null)}),t.hasOwnProperty("data")){var s=t.data;if(!(t.data instanceof u)&&"object"===f(t.data))if("application/x-www-form-urlencoded"===(n.headers["content-type"]||n.headers["Content-Type"]))s=d.stringify(t.data);else try{s=JSON.stringify(t.data)}catch(t){r(new Error("Couldn't stringify object. (Likely due to a circular reference.)"),null)}o.write(s)}o.end()};i.promisified=function(t,e){return new Promise(function(r,n){i(t,function(t,e){t?n(t):r(e)})})},t.promisify&&(i[t.promisify.custom]=i.promisified),r.exports=i}).call(this,e("buffer").Buffer)},{buffer:16,http:243,https:164,querystring:227,url:253,util:258,zlib:13}],199:[function(t,e,r){"use strict";function x(t,e,r,n,i,o){for(var a,s,u,f,l=Math.max(e-1,0),h=Math.max(r-1,0),c=Math.min(e+1,n-1),d=Math.min(r+1,i-1),p=4*(r*n+e),m=0,b=0,g=0,_=0,y=0,v=l;v<=c;v++)for(var w=h;w<=d;w++)if(v!==e||w!==r){var E=j(t,t,p,4*(w*n+v),!0);if(0===E?m++:E<0?g++:0<E&&b++,2<m)return!1;o&&(E<_&&(_=E,a=v,s=w),y<E&&(y=E,u=v,f=w))}return!o||0!==g&&0!==b&&(!x(t,a,s,n,i)&&!x(o,a,s,n,i)||!x(t,u,f,n,i)&&!x(o,u,f,n,i))}function j(t,e,r,n,i){var o=t[r+3]/255,a=e[n+3]/255,s=w(t[r+0],o),u=w(t[r+1],o),f=w(t[r+2],o),l=w(e[n+0],a),h=w(e[n+1],a),c=w(e[n+2],a),d=v(s,u,f)-v(l,h,c);if(i)return d;var p=b(s,u,f)-b(l,h,c),m=g(s,u,f)-g(l,h,c);return.5053*d*d+.299*p*p+.1957*m*m}function v(t,e,r){return.29889531*t+.58662247*e+.11448223*r}function b(t,e,r){return.59597799*t-.2741761*e-.32180189*r}function g(t,e,r){return.21147017*t-.52261711*e+.31114694*r}function w(t,e){return 255+(t-255)*e}function E(t,e,r,n,i){t[e+0]=r,t[e+1]=n,t[e+2]=i,t[e+3]=255}e.exports=function(t,e,r,n,i,o){o||(o={});for(var a=void 0===o.threshold?.1:o.threshold,s=35215*a*a,u=0,f=0;f<i;f++)for(var l=0;l<n;l++){var h=4*(f*n+l),c=j(t,e,h,h);if(s<c)o.includeAA||!x(t,l,f,n,i,e)&&!x(e,l,f,n,i,t)?(r&&E(r,h,255,0,0),u++):r&&E(r,h,255,255,0);else if(r){var d=w((void 0,b=(p=t)[(m=h)+3]/255,g=w(p[m+0],b),_=w(p[m+1],b),y=w(p[m+2],b),v(g,_,y)),.1);E(r,h,d,d,d)}}var p,m,b,g,_,y;return u}},{}],200:[function(t,e,r){(function(m){"use strict";var b=t("./interlace"),g={1:{0:0,1:0,2:0,3:255},2:{0:0,1:0,2:0,3:1},3:{0:0,1:1,2:2,3:255},4:{0:0,1:1,2:2,3:3}};function _(t,e,r,n,i,o){for(var a=t.width,s=t.height,u=t.index,f=0;f<s;f++)for(var l=0;l<a;l++){for(var h=r(l,f,u),c=0;c<4;c++){var d=g[n][c];if(255===d)e[h+c]=255;else{var p=d+o;if(p===i.length)throw new Error("Ran out of data");e[h+c]=i[p]}}o+=n}return o}function y(t,e,r,n,i,o){for(var a=t.width,s=t.height,u=t.index,f=0;f<s;f++){for(var l=0;l<a;l++)for(var h=i.get(n),c=r(l,f,u),d=0;d<4;d++){var p=g[n][d];e[c+d]=255!==p?h[p]:o}i.resetAfterLine()}}r.dataToBitMap=function(t,e){var r,n=e.width,i=e.height,o=e.depth,a=e.bpp,s=e.interlace;if(8!==o)var u=function(f,l){var h=[],c=0;function r(){if(c===f.length)throw new Error("Ran out of data");var t,e,r,n,i,o,a,s,u=f[c];switch(c++,l){default:throw new Error("unrecognised depth");case 16:a=f[c],c++,h.push((u<<8)+a);break;case 4:a=15&u,s=u>>4,h.push(s,a);break;case 2:i=3&u,o=u>>2&3,a=u>>4&3,s=u>>6&3,h.push(s,a,o,i);break;case 1:t=1&u,e=u>>1&1,r=u>>2&1,n=u>>3&1,i=u>>4&1,o=u>>5&1,a=u>>6&1,s=u>>7&1,h.push(s,a,o,i,n,r,e,t)}}return{get:function(t){for(;h.length<t;)r();var e=h.slice(0,t);return h=h.slice(t),e},resetAfterLine:function(){h.length=0},end:function(){if(c!==f.length)throw new Error("extra data found")}}}(t,o);r=o<=8?new m(n*i*4):new Uint16Array(n*i*4);var f,l,h=Math.pow(2,o)-1,c=0;if(s)f=b.getImagePasses(n,i),l=b.getInterlaceIterator(n,i);else{var d=0;l=function(){var t=d;return d+=4,t},f=[{width:n,height:i}]}for(var p=0;p<f.length;p++)8===o?c=_(f[p],r,l,a,t,c):y(f[p],r,l,a,u,h);if(8===o){if(c!==t.length)throw new Error("extra data found")}else u.end();return r}}).call(this,t("buffer").Buffer)},{"./interlace":210,buffer:16}],201:[function(t,e,r){(function(v){"use strict";var w=t("./constants");e.exports=function(t,e,r,a){var n,s=-1!==[w.COLORTYPE_COLOR_ALPHA,w.COLORTYPE_ALPHA].indexOf(a.colorType);if(a.colorType===a.inputColorType){var i=(n=new ArrayBuffer(2),new DataView(n).setInt16(0,256,!0),256!==new Int16Array(n)[0]);if(8===a.bitDepth||16===a.bitDepth&&i)return t}var o=16!==a.bitDepth?t:new Uint16Array(t.buffer),u=255,f=w.COLORTYPE_TO_BPP_MAP[a.inputColorType];4!=f||a.inputHasAlpha||(f=3);var l=w.COLORTYPE_TO_BPP_MAP[a.colorType];16===a.bitDepth&&(u=65535,l*=2);var h=new v(e*r*l),c=0,d=0,p=a.bgColor||{};function m(t,e){var r,n,i,o=u;switch(a.inputColorType){case w.COLORTYPE_COLOR_ALPHA:o=t[e+3],r=t[e],n=t[e+1],i=t[e+2];break;case w.COLORTYPE_COLOR:r=t[e],n=t[e+1],i=t[e+2];break;case w.COLORTYPE_ALPHA:o=t[e+1],i=n=r=t[e];break;case w.COLORTYPE_GRAYSCALE:i=n=r=t[e];break;default:throw new Error("input color type:"+a.inputColorType+" is not supported at present")}return a.inputHasAlpha&&(s||(o/=u,r=Math.min(Math.max(Math.round((1-o)*p.red+o*r),0),u),n=Math.min(Math.max(Math.round((1-o)*p.green+o*n),0),u),i=Math.min(Math.max(Math.round((1-o)*p.blue+o*i),0),u))),{red:r,green:n,blue:i,alpha:o}}void 0===p.red&&(p.red=u),void 0===p.green&&(p.green=u),void 0===p.blue&&(p.blue=u);for(var b=0;b<r;b++)for(var g=0;g<e;g++){var _=m(o,c);switch(a.colorType){case w.COLORTYPE_COLOR_ALPHA:case w.COLORTYPE_COLOR:8===a.bitDepth?(h[d]=_.red,h[d+1]=_.green,h[d+2]=_.blue,s&&(h[d+3]=_.alpha)):(h.writeUInt16BE(_.red,d),h.writeUInt16BE(_.green,d+2),h.writeUInt16BE(_.blue,d+4),s&&h.writeUInt16BE(_.alpha,d+6));break;case w.COLORTYPE_ALPHA:case w.COLORTYPE_GRAYSCALE:var y=(_.red+_.green+_.blue)/3;8===a.bitDepth?(h[d]=y,s&&(h[d+1]=_.alpha)):(h.writeUInt16BE(y,d),s&&h.writeUInt16BE(_.alpha,d+2))}c+=f,d+=l}return h}}).call(this,t("buffer").Buffer)},{"./constants":203,buffer:16}],202:[function(i,o,t){(function(r,a){"use strict";var t=i("util"),e=i("stream"),n=o.exports=function(){e.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};t.inherits(n,e),n.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),r.nextTick(function(){this._process(),this._paused&&0<this._reads.length&&(this._paused=!1,this.emit("drain"))}.bind(this))},n.prototype.write=function(t,e){return this.writable?(r=a.isBuffer(t)?t:new a(t,e||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",new Error("Stream not writable")),!1);var r},n.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},n.prototype.destroySoon=n.prototype.end,n.prototype._end=function(){0<this._reads.length&&this.emit("error",new Error("There are some read requests waiting on finished stream")),this.destroy()},n.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},n.prototype._processReadAllowingLess=function(t){this._reads.shift();var e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},n.prototype._processRead=function(t){this._reads.shift();for(var e=0,r=0,n=new a(t.length);e<t.length;){var i=this._buffers[r++],o=Math.min(i.length,t.length-e);i.copy(n,e,0,o),e+=o,o!==i.length&&(this._buffers[--r]=i.slice(o))}0<r&&this._buffers.splice(0,r),this._buffered-=t.length,t.func.call(this,n)},n.prototype._process=function(){try{for(;0<this._buffered&&this._reads&&0<this._reads.length;){var t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else{if(!(this._buffered>=t.length))break;this._processRead(t)}}this._buffers&&0<this._buffers.length&&null===this._buffers[0]&&this._end()}catch(t){this.emit("error",t)}}}).call(this,i("_process"),i("buffer").Buffer)},{_process:223,buffer:16,stream:242,util:258}],203:[function(t,e,r){"use strict";e.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},{}],204:[function(t,e,r){"use strict";var n=[];!function(){for(var t=0;t<256;t++){for(var e=t,r=0;r<8;r++)1&e?e=3988292384^e>>>1:e>>>=1;n[t]=e}}();var i=e.exports=function(){this._crc=-1};i.prototype.write=function(t){for(var e=0;e<t.length;e++)this._crc=n[255&(this._crc^t[e])]^this._crc>>>8;return!0},i.prototype.crc32=function(){return-1^this._crc},i.crc32=function(t){for(var e=-1,r=0;r<t.length;r++)e=n[255&(e^t[r])]^e>>>8;return-1^e}},{}],205:[function(t,e,r){(function(m){"use strict";var h=t("./paeth-predictor");var b={0:function(t,e,r,n,i){for(var o=0;o<r;o++)n[i+o]=t[e+o]},1:function(t,e,r,n,i,o){for(var a=0;a<r;a++){var s=o<=a?t[e+a-o]:0,u=t[e+a]-s;n[i+a]=u}},2:function(t,e,r,n,i){for(var o=0;o<r;o++){var a=0<e?t[e+o-r]:0,s=t[e+o]-a;n[i+o]=s}},3:function(t,e,r,n,i,o){for(var a=0;a<r;a++){var s=o<=a?t[e+a-o]:0,u=0<e?t[e+a-r]:0,f=t[e+a]-(s+u>>1);n[i+a]=f}},4:function(t,e,r,n,i,o){for(var a=0;a<r;a++){var s=o<=a?t[e+a-o]:0,u=0<e?t[e+a-r]:0,f=0<e&&o<=a?t[e+a-(r+o)]:0,l=t[e+a]-h(s,u,f);n[i+a]=l}}},g={0:function(t,e,r){for(var n=0,i=e+r,o=e;o<i;o++)n+=Math.abs(t[o]);return n},1:function(t,e,r,n){for(var i=0,o=0;o<r;o++){var a=n<=o?t[e+o-n]:0,s=t[e+o]-a;i+=Math.abs(s)}return i},2:function(t,e,r){for(var n=0,i=e+r,o=e;o<i;o++){var a=0<e?t[o-r]:0,s=t[o]-a;n+=Math.abs(s)}return n},3:function(t,e,r,n){for(var i=0,o=0;o<r;o++){var a=n<=o?t[e+o-n]:0,s=0<e?t[e+o-r]:0,u=t[e+o]-(a+s>>1);i+=Math.abs(u)}return i},4:function(t,e,r,n){for(var i=0,o=0;o<r;o++){var a=n<=o?t[e+o-n]:0,s=0<e?t[e+o-r]:0,u=0<e&&n<=o?t[e+o-(r+n)]:0,f=t[e+o]-h(a,s,u);i+=Math.abs(f)}return i}};e.exports=function(t,e,r,n,i){var o;if("filterType"in n&&-1!==n.filterType){if("number"!=typeof n.filterType)throw new Error("unrecognised filter types");o=[n.filterType]}else o=[0,1,2,3,4];16===n.bitDepth&&(i*=2);for(var a=e*i,s=0,u=0,f=new m((a+1)*r),l=o[0],h=0;h<r;h++){if(1<o.length)for(var c=1/0,d=0;d<o.length;d++){var p=g[o[d]](t,u,a,i);p<c&&(l=o[d],c=p)}f[s]=l,s++,b[l](t,u,a,f,s,i),s+=a,u+=a}return f}}).call(this,t("buffer").Buffer)},{"./paeth-predictor":214,buffer:16}],206:[function(r,a,t){(function(n){"use strict";var t=r("util"),i=r("./chunkstream"),o=r("./filter-parse"),e=a.exports=function(t){i.call(this);var e=[],r=this;this._filter=new o(t,{read:this.read.bind(this),write:function(t){e.push(t)},complete:function(){r.emit("complete",n.concat(e))}}),this._filter.start()};t.inherits(e,i)}).call(this,r("buffer").Buffer)},{"./chunkstream":202,"./filter-parse":208,buffer:16,util:258}],207:[function(t,e,r){(function(i){"use strict";var o=t("./sync-reader"),a=t("./filter-parse");r.process=function(t,e){var r=[],n=new o(t);return new a(e,{read:n.read.bind(n),write:function(t){r.push(t)},complete:function(){}}).start(),n.process(),i.concat(r)}}).call(this,t("buffer").Buffer)},{"./filter-parse":208,"./sync-reader":221,buffer:16}],208:[function(e,r,t){(function(o){"use strict";var f=e("./interlace"),c=e("./paeth-predictor");function l(t,e,r){var n=t*e;return 8!==r&&(n=Math.ceil(n/(8/r))),n}var t=r.exports=function(t,e){var r=t.width,n=t.height,i=t.interlace,o=t.bpp,a=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],i)for(var s=f.getImagePasses(r,n),u=0;u<s.length;u++)this._images.push({byteWidth:l(s[u].width,o,a),height:s[u].height,lineIndex:0});else this._images.push({byteWidth:l(r,o,a),height:n,lineIndex:0});this._xComparison=8===a?o:16===a?2*o:1};t.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},t.prototype._unFilterType1=function(t,e,r){for(var n=this._xComparison,i=n-1,o=0;o<r;o++){var a=t[1+o],s=i<o?e[o-n]:0;e[o]=a+s}},t.prototype._unFilterType2=function(t,e,r){for(var n=this._lastLine,i=0;i<r;i++){var o=t[1+i],a=n?n[i]:0;e[i]=o+a}},t.prototype._unFilterType3=function(t,e,r){for(var n=this._xComparison,i=n-1,o=this._lastLine,a=0;a<r;a++){var s=t[1+a],u=o?o[a]:0,f=i<a?e[a-n]:0,l=Math.floor((f+u)/2);e[a]=s+l}},t.prototype._unFilterType4=function(t,e,r){for(var n=this._xComparison,i=n-1,o=this._lastLine,a=0;a<r;a++){var s=t[1+a],u=o?o[a]:0,f=i<a?e[a-n]:0,l=i<a&&o?o[a-n]:0,h=c(f,u,l);e[a]=s+h}},t.prototype._reverseFilterLine=function(t){var e,r=t[0],n=this._images[this._imageIndex],i=n.byteWidth;if(0===r)e=t.slice(1,i+1);else switch(e=new o(i),r){case 1:this._unFilterType1(t,e,i);break;case 2:this._unFilterType2(t,e,i);break;case 3:this._unFilterType3(t,e,i);break;case 4:this._unFilterType4(t,e,i);break;default:throw new Error("Unrecognised filter type - "+r)}this.write(e),n.lineIndex++,n.lineIndex>=n.height?(this._lastLine=null,this._imageIndex++,n=this._images[this._imageIndex]):this._lastLine=e,n?this.read(n.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}}).call(this,e("buffer").Buffer)},{"./interlace":210,"./paeth-predictor":214,buffer:16}],209:[function(t,e,r){(function(f){"use strict";e.exports=function(t,e){var r=e.depth,n=e.width,i=e.height,o=e.colorType,a=e.transColor,s=e.palette,u=t;return 3===o?function(t,e,r,n,i){for(var o=0,a=0;a<n;a++)for(var s=0;s<r;s++){var u=i[t[o]];if(!u)throw new Error("index "+t[o]+" not in palette");for(var f=0;f<4;f++)e[o+f]=u[f];o+=4}}(t,u,n,i,s):(a&&function(t,e,r,n,i){for(var o=0,a=0;a<n;a++)for(var s=0;s<r;s++){var u=!1;if(1===i.length?i[0]===t[o]&&(u=!0):i[0]===t[o]&&i[1]===t[o+1]&&i[2]===t[o+2]&&(u=!0),u)for(var f=0;f<4;f++)e[o+f]=0;o+=4}}(t,u,n,i,a),8!==r&&(16===r&&(u=new f(n*i*4)),function(t,e,r,n,i){for(var o=Math.pow(2,i)-1,a=0,s=0;s<n;s++)for(var u=0;u<r;u++){for(var f=0;f<4;f++)e[a+f]=Math.floor(255*t[a+f]/o+.5);a+=4}}(t,u,n,i,r))),u}}).call(this,t("buffer").Buffer)},{buffer:16}],210:[function(t,e,r){"use strict";var c=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];r.getImagePasses=function(t,e){for(var r=[],n=t%8,i=e%8,o=(t-n)/8,a=(e-i)/8,s=0;s<c.length;s++){for(var u=c[s],f=o*u.x.length,l=a*u.y.length,h=0;h<u.x.length&&u.x[h]<n;h++)f++;for(h=0;h<u.y.length&&u.y[h]<i;h++)l++;0<f&&0<l&&r.push({width:f,height:l,index:s})}return r},r.getInterlaceIterator=function(a){return function(t,e,r){var n=t%c[r].x.length,i=(t-n)/c[r].x.length*8+c[r].x[n],o=e%c[r].y.length;return 4*i+((e-o)/c[r].y.length*8+c[r].y[o])*a*4}}},{}],211:[function(i,s,t){(function(o){"use strict";var t=i("util"),r=i("stream"),a=i("./constants"),n=i("./packer"),e=s.exports=function(t){r.call(this);var e=t||{};this._packer=new n(e),this._deflate=this._packer.createDeflate(),this.readable=!0};t.inherits(e,r),e.prototype.pack=function(t,e,r,n){this.emit("data",new o(a.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,r)),n&&this.emit("data",this._packer.packGAMA(n));var i=this._packer.filterData(t,e,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(t){this.emit("data",this._packer.packIDAT(t))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(i)}}).call(this,i("buffer").Buffer)},{"./constants":203,"./packer":213,buffer:16,stream:242,util:258}],212:[function(t,e,r){(function(a){"use strict";var s=!0,u=t("zlib");u.deflateSync||(s=!1);var f=t("./constants"),l=t("./packer");e.exports=function(t,e){if(!s)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");var r=new l(e||{}),n=[];n.push(new a(f.PNG_SIGNATURE)),n.push(r.packIHDR(t.width,t.height)),t.gamma&&n.push(r.packGAMA(t.gamma));var i=r.filterData(t.data,t.width,t.height),o=u.deflateSync(i,r.getDeflateOptions());if(i=null,!o||!o.length)throw new Error("bad png - invalid compressed data response");return n.push(r.packIDAT(o)),n.push(r.packIEND()),a.concat(n)}}).call(this,t("buffer").Buffer)},{"./constants":203,"./packer":213,buffer:16,zlib:13}],213:[function(r,n,t){(function(i){"use strict";var o=r("./constants"),a=r("./crc"),s=r("./bitpacker"),u=r("./filter-pack"),e=r("zlib"),t=n.exports=function(t){if((this._options=t).deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||e.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"==typeof t.colorType?t.colorType:o.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"==typeof t.inputColorType?t.inputColorType:o.COLORTYPE_COLOR_ALPHA,-1===[o.COLORTYPE_GRAYSCALE,o.COLORTYPE_COLOR,o.COLORTYPE_COLOR_ALPHA,o.COLORTYPE_ALPHA].indexOf(t.colorType))throw new Error("option color type:"+t.colorType+" is not supported at present");if(-1===[o.COLORTYPE_GRAYSCALE,o.COLORTYPE_COLOR,o.COLORTYPE_COLOR_ALPHA,o.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};t.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},t.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},t.prototype.filterData=function(t,e,r){var n=s(t,e,r,this._options),i=o.COLORTYPE_TO_BPP_MAP[this._options.colorType];return u(n,e,r,this._options,i)},t.prototype._packChunk=function(t,e){var r=e?e.length:0,n=new i(r+12);return n.writeUInt32BE(r,0),n.writeUInt32BE(t,4),e&&e.copy(n,8),n.writeInt32BE(a.crc32(n.slice(4,n.length-4)),n.length-4),n},t.prototype.packGAMA=function(t){var e=new i(4);return e.writeUInt32BE(Math.floor(t*o.GAMMA_DIVISION),0),this._packChunk(o.TYPE_gAMA,e)},t.prototype.packIHDR=function(t,e){var r=new i(13);return r.writeUInt32BE(t,0),r.writeUInt32BE(e,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(o.TYPE_IHDR,r)},t.prototype.packIDAT=function(t){return this._packChunk(o.TYPE_IDAT,t)},t.prototype.packIEND=function(){return this._packChunk(o.TYPE_IEND,null)}}).call(this,r("buffer").Buffer)},{"./bitpacker":201,"./constants":203,"./crc":204,"./filter-pack":205,buffer:16,zlib:13}],214:[function(t,e,r){"use strict";e.exports=function(t,e,r){var n=t+e-r,i=Math.abs(n-t),o=Math.abs(n-e),a=Math.abs(n-r);return i<=o&&i<=a?t:o<=a?e:r}},{}],215:[function(t,e,r){"use strict";var n=t("util"),a=t("zlib"),i=t("./chunkstream"),o=t("./filter-parse-async"),s=t("./parser"),u=t("./bitmapper"),f=t("./format-normaliser"),l=e.exports=function(t){i.call(this),this._parser=new s(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};n.inherits(l,i),l.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},l.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=a.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{var e=(1+(this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3))*this._bitmapInfo.height,r=Math.max(e,a.Z_MIN_CHUNK);this._inflate=a.createInflate({chunkSize:r});var n=e,i=this.emit.bind(this,"error");this._inflate.on("error",function(t){n&&i(t)}),this._filter.on("complete",this._complete.bind(this));var o=this._filter.write.bind(this._filter);this._inflate.on("data",function(t){n&&(t.length>n&&(t=t.slice(0,n)),n-=t.length,o(t))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)},l.prototype._handleMetaData=function(t){this.emit("metadata",t),this._bitmapInfo=Object.create(t),this._filter=new o(this._bitmapInfo)},l.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},l.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},l.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"),this.destroySoon())},l.prototype._complete=function(t){if(!this.errord){try{var e=u.dataToBitMap(t,this._bitmapInfo),r=f(e,this._bitmapInfo);e=null}catch(t){return void this._handleError(t)}this.emit("parsed",r)}}},{"./bitmapper":200,"./chunkstream":202,"./filter-parse-async":206,"./format-normaliser":209,"./parser":217,util:258,zlib:13}],216:[function(t,e,r){(function(d){"use strict";var p=!0,m=t("zlib"),b=t("./sync-inflate");m.deflateSync||(p=!1);var g=t("./sync-reader"),_=t("./filter-parse-sync"),y=t("./parser"),v=t("./bitmapper"),w=t("./format-normaliser");e.exports=function(t,e){if(!p)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");var r,n,i;var o=[];var a=new g(t);if(new y(e,{read:a.read.bind(a),error:function(t){r=t},metadata:function(t){n=t},gamma:function(t){i=t},palette:function(t){n.palette=t},transColor:function(t){n.transColor=t},inflateData:function(t){o.push(t)}}).start(),a.process(),r)throw r;var s,u=d.concat(o);if(o.length=0,n.interlace)s=m.inflateSync(u);else{var f=(1+(n.width*n.bpp*n.depth+7>>3))*n.height;s=b(u,{chunkSize:f,maxLength:f})}if(u=null,!s||!s.length)throw new Error("bad png - invalid inflate data response");var l=_.process(s,n);u=null;var h=v.dataToBitMap(l,n);l=null;var c=w(h,n);return n.data=c,n.gamma=i||0,n}}).call(this,t("buffer").Buffer)},{"./bitmapper":200,"./filter-parse-sync":207,"./format-normaliser":209,"./parser":217,"./sync-inflate":220,"./sync-reader":221,buffer:16,zlib:13}],217:[function(e,r,t){(function(a){"use strict";var f=e("./constants"),s=e("./crc"),t=r.exports=function(t,e){(this._options=t).checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[f.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[f.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[f.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[f.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[f.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[f.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished};t.prototype.start=function(){this.read(f.PNG_SIGNATURE.length,this._parseSignature.bind(this))},t.prototype._parseSignature=function(t){for(var e=f.PNG_SIGNATURE,r=0;r<e.length;r++)if(t[r]!==e[r])return void this.error(new Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},t.prototype._parseChunkBegin=function(t){for(var e=t.readUInt32BE(0),r=t.readUInt32BE(4),n="",i=4;i<8;i++)n+=String.fromCharCode(t[i]);var o=Boolean(32&t[4]);if(this._hasIHDR||r===f.TYPE_IHDR){if(this._crc=new s,this._crc.write(new a(n)),this._chunks[r])return this._chunks[r](e);o?this.read(e+4,this._skipChunk.bind(this)):this.error(new Error("Unsupported critical chunk type "+n))}else this.error(new Error("Expected IHDR on beggining"))},t.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},t.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},t.prototype._parseChunkEnd=function(t){var e=t.readInt32BE(0),r=this._crc.crc32();this._options.checkCRC&&r!==e?this.error(new Error("Crc error - "+e+" - "+r)):this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},t.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},t.prototype._parseIHDR=function(t){this._crc.write(t);var e=t.readUInt32BE(0),r=t.readUInt32BE(4),n=t[8],i=t[9],o=t[10],a=t[11],s=t[12];if(8===n||4===n||2===n||1===n||16===n)if(i in f.COLORTYPE_TO_BPP_MAP)if(0===o)if(0===a)if(0===s||1===s){this._colorType=i;var u=f.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:r,depth:n,interlace:Boolean(s),palette:Boolean(i&f.COLORTYPE_PALETTE),color:Boolean(i&f.COLORTYPE_COLOR),alpha:Boolean(i&f.COLORTYPE_ALPHA),bpp:u,colorType:i}),this._handleChunkEnd()}else this.error(new Error("Unsupported interlace method"));else this.error(new Error("Unsupported filter method"));else this.error(new Error("Unsupported compression method"));else this.error(new Error("Unsupported color type"));else this.error(new Error("Unsupported bit depth "+n))},t.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},t.prototype._parsePLTE=function(t){this._crc.write(t);for(var e=Math.floor(t.length/3),r=0;r<e;r++)this._palette.push([t[3*r],t[3*r+1],t[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},t.prototype._handleTRNS=function(t){this.read(t,this._parseTRNS.bind(this))},t.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===f.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(new Error("Transparency chunk must be after palette"));if(t.length>this._palette.length)return void this.error(new Error("More transparent colors than palette size"));for(var e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===f.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===f.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},t.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},t.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/f.GAMMA_DIVISION),this._handleChunkEnd()},t.prototype._handleIDAT=function(t){this.read(-t,this._parseIDAT.bind(this,t))},t.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===f.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw new Error("Expected palette not found");this.inflateData(e);var r=t-e.length;0<r?this._handleIDAT(r):this._handleChunkEnd()},t.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},t.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}}).call(this,e("buffer").Buffer)},{"./constants":203,"./crc":204,buffer:16}],218:[function(t,e,r){"use strict";var n=t("./parser-sync"),i=t("./packer-sync");r.read=function(t,e){return n(t,e||{})},r.write=function(t,e){return i(t,e)}},{"./packer-sync":212,"./parser-sync":216}],219:[function(u,t,f){(function(t,e){"use strict";var r=u("util"),n=u("stream"),i=u("./parser-async"),o=u("./packer-async"),a=u("./png-sync"),s=f.PNG=function(t){n.call(this),t=t||{},this.width=0|t.width,this.height=0|t.height,this.data=0<this.width&&0<this.height?new e(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new i(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(t){this.data=t,this.emit("parsed",t)}.bind(this)),this._packer=new o(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};r.inherits(s,n),s.sync=a,s.prototype.pack=function(){return this.data&&this.data.length?t.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)):this.emit("error","No data provided"),this},s.prototype.parse=function(t,e){var r,n;e&&(r=function(t){this.removeListener("error",n),this.data=t,e(null,this)}.bind(this),n=function(t){this.removeListener("parsed",r),e(t,null)}.bind(this),this.once("parsed",r),this.once("error",n));return this.end(t),this},s.prototype.write=function(t){return this._parser.write(t),!0},s.prototype.end=function(t){this._parser.end(t)},s.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},s.prototype._gamma=function(t){this.gamma=t},s.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},s.bitblt=function(t,e,r,n,i,o,a,s){if(n|=0,i|=0,o|=0,a|=0,s|=0,(r|=0)>t.width||n>t.height||r+i>t.width||n+o>t.height)throw new Error("bitblt reading outside image");if(a>e.width||s>e.height||a+i>e.width||s+o>e.height)throw new Error("bitblt writing outside image");for(var u=0;u<o;u++)t.data.copy(e.data,(s+u)*e.width+a<<2,(n+u)*t.width+r<<2,(n+u)*t.width+r+i<<2)},s.prototype.bitblt=function(t,e,r,n,i,o,a){return s.bitblt(this,t,e,r,n,i,o,a),this},s.adjustGamma=function(t){if(t.gamma){for(var e=0;e<t.height;e++)for(var r=0;r<t.width;r++)for(var n=t.width*e+r<<2,i=0;i<3;i++){var o=t.data[n+i]/255;o=Math.pow(o,1/2.2/t.gamma),t.data[n+i]=Math.round(255*o)}t.gamma=0}},s.prototype.adjustGamma=function(){s.adjustGamma(this)}}).call(this,u("_process"),u("buffer").Buffer)},{"./packer-async":211,"./parser-async":215,"./png-sync":218,_process:223,buffer:16,stream:242,util:258}],220:[function(i,o,a){(function(r,p){"use strict";var m=i("assert").ok,b=i("zlib"),t=i("util"),g=i("buffer").kMaxLength;function n(t){if(!(this instanceof n))return new n(t);t&&t.chunkSize<b.Z_MIN_CHUNK&&(t.chunkSize=b.Z_MIN_CHUNK),b.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function _(t,e){e&&r.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function e(t,e){return function(t,e){if("string"==typeof e&&(e=p.from(e)),!(e instanceof p))throw new TypeError("Not a string or buffer");var r=t._finishFlushFlag;return null==r&&(r=b.Z_FINISH),t._processChunk(e,r)}(new n(e),t)}n.prototype._processChunk=function(t,e,r){if("function"==typeof r)return b.Inflate._processChunk.call(this,t,e,r);var n,i=this,o=t&&t.length,a=this._chunkSize-this._offset,s=this._maxLength,u=0,f=[],l=0;function h(t,e){if(!i._hadError){var r=a-e;if(m(0<=r,"have should not go down"),0<r){var n=i._buffer.slice(i._offset,i._offset+r);if(i._offset+=r,n.length>s&&(n=n.slice(0,s)),f.push(n),l+=n.length,0===(s-=n.length))return!1}return(0===e||i._offset>=i._chunkSize)&&(a=i._chunkSize,i._offset=0,i._buffer=p.allocUnsafe(i._chunkSize)),0===e&&(u+=o-t,o=t,!0)}}this.on("error",function(t){n=t}),m(this._handle,"zlib binding closed");do{var c=this._handle.writeSync(e,t,u,o,this._buffer,this._offset,a);c=c||this._writeState}while(!this._hadError&&h(c[0],c[1]));if(this._hadError)throw n;if(g<=l)throw _(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+g.toString(16)+" bytes");var d=p.concat(f,l);return _(this),d},t.inherits(n,b.Inflate),o.exports=a=e,a.Inflate=n,a.createInflate=function(t){return new n(t)},a.inflateSync=e}).call(this,i("_process"),i("buffer").Buffer)},{_process:223,assert:3,buffer:16,util:258,zlib:13}],221:[function(t,e,r){"use strict";var n=e.exports=function(t){this._buffer=t,this._reads=[]};n.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},n.prototype.process=function(){for(;0<this._reads.length&&this._buffer.length;){var t=this._reads[0];if(!this._buffer.length||!(this._buffer.length>=t.length||t.allowLess))break;this._reads.shift();var e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}return 0<this._reads.length?new Error("There are some read requests waitng on finished stream"):0<this._buffer.length?new Error("unrecognised content at end of stream"):void 0}},{}],222:[function(t,e,r){(function(s){"use strict";!s.version||0===s.version.indexOf("v0.")||0===s.version.indexOf("v1.")&&0!==s.version.indexOf("v1.8.")?e.exports={nextTick:function(t,e,r,n){if("function"!=typeof t)throw new TypeError('"callback" argument must be a function');var i,o,a=arguments.length;switch(a){case 0:case 1:return s.nextTick(t);case 2:return s.nextTick(function(){t.call(null,e)});case 3:return s.nextTick(function(){t.call(null,e,r)});case 4:return s.nextTick(function(){t.call(null,e,r,n)});default:for(i=new Array(a-1),o=0;o<i.length;)i[o++]=arguments[o];return s.nextTick(function(){t.apply(null,i)})}}}:e.exports=s}).call(this,t("_process"))},{_process:223}],223:[function(t,e,r){var n,i,o=e.exports={};function a(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(e){if(n===setTimeout)return setTimeout(e,0);if((n===a||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:a}catch(t){n=a}try{i="function"==typeof clearTimeout?clearTimeout:s}catch(t){i=s}}();var f,l=[],h=!1,c=-1;function d(){h&&f&&(h=!1,f.length?l=f.concat(l):c=-1,l.length&&p())}function p(){if(!h){var t=u(d);h=!0;for(var e=l.length;e;){for(f=l,l=[];++c<e;)f&&f[c].run();c=-1,e=l.length}f=null,h=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===s||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(t)}}function m(t,e){this.fun=t,this.array=e}function b(){}o.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];l.push(new m(t,e)),1!==l.length||h||u(p)},m.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=b,o.addListener=b,o.once=b,o.off=b,o.removeListener=b,o.removeAllListeners=b,o.emit=b,o.prependListener=b,o.prependOnceListener=b,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],224:[function(t,M,L){(function(P){!function(t){var e="object"==typeof L&&L&&!L.nodeType&&L,r="object"==typeof M&&M&&!M.nodeType&&M,n="object"==typeof P&&P;n.global!==n&&n.window!==n&&n.self!==n||(t=n);var i,o,g=2147483647,_=36,y=1,v=26,a=38,s=700,w=72,E=128,x="-",u=/^xn--/,f=/[^\x20-\x7E]/,l=/[\x2E\u3002\uFF0E\uFF61]/g,h={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},c=_-y,j=Math.floor,k=String.fromCharCode;function S(t){throw new RangeError(h[t])}function d(t,e){for(var r=t.length,n=[];r--;)n[r]=e(t[r]);return n}function p(t,e){var r=t.split("@"),n="";return 1<r.length&&(n=r[0]+"@",t=r[1]),n+d((t=t.replace(l,".")).split("."),e).join(".")}function I(t){for(var e,r,n=[],i=0,o=t.length;i<o;)55296<=(e=t.charCodeAt(i++))&&e<=56319&&i<o?56320==(64512&(r=t.charCodeAt(i++)))?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--):n.push(e);return n}function A(t){return d(t,function(t){var e="";return 65535<t&&(e+=k((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+=k(t)}).join("")}function T(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function O(t,e,r){var n=0;for(t=r?j(t/s):t>>1,t+=j(t/e);c*v>>1<t;n+=_)t=j(t/c);return j(n+(c+1)*t/(t+a))}function m(t){var e,r,n,i,o,a,s,u,f,l,h,c=[],d=t.length,p=0,m=E,b=w;for((r=t.lastIndexOf(x))<0&&(r=0),n=0;n<r;++n)128<=t.charCodeAt(n)&&S("not-basic"),c.push(t.charCodeAt(n));for(i=0<r?r+1:0;i<d;){for(o=p,a=1,s=_;d<=i&&S("invalid-input"),h=t.charCodeAt(i++),(_<=(u=h-48<10?h-22:h-65<26?h-65:h-97<26?h-97:_)||u>j((g-p)/a))&&S("overflow"),p+=u*a,!(u<(f=s<=b?y:b+v<=s?v:s-b));s+=_)a>j(g/(l=_-f))&&S("overflow"),a*=l;b=O(p-o,e=c.length+1,0==o),j(p/e)>g-m&&S("overflow"),m+=j(p/e),p%=e,c.splice(p++,0,m)}return A(c)}function b(t){var e,r,n,i,o,a,s,u,f,l,h,c,d,p,m,b=[];for(c=(t=I(t)).length,e=E,o=w,a=r=0;a<c;++a)(h=t[a])<128&&b.push(k(h));for(n=i=b.length,i&&b.push(x);n<c;){for(s=g,a=0;a<c;++a)e<=(h=t[a])&&h<s&&(s=h);for(s-e>j((g-r)/(d=n+1))&&S("overflow"),r+=(s-e)*d,e=s,a=0;a<c;++a)if((h=t[a])<e&&++r>g&&S("overflow"),h==e){for(u=r,f=_;!(u<(l=f<=o?y:o+v<=f?v:f-o));f+=_)m=u-l,p=_-l,b.push(k(T(l+m%p,0))),u=j(m/p);b.push(k(T(u,0))),o=O(r,d,n==i),r=0,++n}++r,++e}return b.join("")}if(i={version:"1.4.1",ucs2:{decode:I,encode:A},decode:m,encode:b,toASCII:function(t){return p(t,function(t){return f.test(t)?"xn--"+b(t):t})},toUnicode:function(t){return p(t,function(t){return u.test(t)?m(t.slice(4).toLowerCase()):t})}},e&&r)if(M.exports==e)r.exports=i;else for(o in i)i.hasOwnProperty(o)&&(e[o]=i[o]);else t.punycode=i}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{}],225:[function(t,e,r){"use strict";e.exports=function(t,e,r,n){e=e||"&",r=r||"=";var i={};if("string"!=typeof t||0===t.length)return i;var o=/\+/g;t=t.split(e);var a=1e3;n&&"number"==typeof n.maxKeys&&(a=n.maxKeys);var s,u,f=t.length;0<a&&a<f&&(f=a);for(var l=0;l<f;++l){var h,c,d,p,m=t[l].replace(o,"%20"),b=m.indexOf(r);c=0<=b?(h=m.substr(0,b),m.substr(b+1)):(h=m,""),d=decodeURIComponent(h),p=decodeURIComponent(c),s=i,u=d,Object.prototype.hasOwnProperty.call(s,u)?g(i[d])?i[d].push(p):i[d]=[i[d],p]:i[d]=p}return i};var g=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},{}],226:[function(t,e,r){"use strict";var o=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}};e.exports=function(r,n,i,t){return n=n||"&",i=i||"=",null===r&&(r=void 0),"object"==typeof r?s(u(r),function(t){var e=encodeURIComponent(o(t))+i;return a(r[t])?s(r[t],function(t){return e+encodeURIComponent(o(t))}).join(n):e+encodeURIComponent(o(r[t]))}).join(n):t?encodeURIComponent(o(t))+i+encodeURIComponent(o(r)):""};var a=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function s(t,e){if(t.map)return t.map(e);for(var r=[],n=0;n<t.length;n++)r.push(e(t[n],n));return r}var u=Object.keys||function(t){var e=[];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.push(r);return e}},{}],227:[function(t,e,r){"use strict";r.decode=r.parse=t("./decode"),r.encode=r.stringify=t("./encode")},{"./decode":225,"./encode":226}],228:[function(t,e,r){e.exports=t("./lib/_stream_duplex.js")},{"./lib/_stream_duplex.js":229}],229:[function(t,e,r){"use strict";var n=t("process-nextick-args"),i=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};e.exports=h;var o=t("core-util-is");o.inherits=t("inherits");var a=t("./_stream_readable"),s=t("./_stream_writable");o.inherits(h,a);for(var u=i(s.prototype),f=0;f<u.length;f++){var l=u[f];h.prototype[l]||(h.prototype[l]=s.prototype[l])}function h(t){if(!(this instanceof h))return new h(t);a.call(this,t),s.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",c)}function c(){this.allowHalfOpen||this._writableState.ended||n.nextTick(d,this)}function d(t){t.end()}Object.defineProperty(h.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(h.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}),h.prototype._destroy=function(t,e){this.push(null),this.end(),n.nextTick(e,t)}},{"./_stream_readable":231,"./_stream_writable":233,"core-util-is":150,inherits:166,"process-nextick-args":222}],230:[function(t,e,r){"use strict";e.exports=o;var n=t("./_stream_transform"),i=t("core-util-is");function o(t){if(!(this instanceof o))return new o(t);n.call(this,t)}i.inherits=t("inherits"),i.inherits(o,n),o.prototype._transform=function(t,e,r){r(null,t)}},{"./_stream_transform":232,"core-util-is":150,inherits:166}],231:[function(L,C,t){(function(b,t){"use strict";var g=L("process-nextick-args");C.exports=c;var a,_=L("isarray");c.ReadableState=o;L("events").EventEmitter;var y=function(t,e){return t.listeners(e).length},i=L("./internal/streams/stream"),f=L("safe-buffer").Buffer,l=t.Uint8Array||function(){};var e=L("core-util-is");e.inherits=L("inherits");var r=L("util"),v=void 0;v=r&&r.debuglog?r.debuglog("stream"):function(){};var s,u=L("./internal/streams/BufferList"),n=L("./internal/streams/destroy");e.inherits(c,i);var h=["error","close","destroy","pause","resume"];function o(t,e){t=t||{};var r=e instanceof(a=a||L("./_stream_duplex"));this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var n=t.highWaterMark,i=t.readableHighWaterMark,o=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:r&&(i||0===i)?i:o,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new u,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(s||(s=L("string_decoder/").StringDecoder),this.decoder=new s(t.encoding),this.encoding=t.encoding)}function c(t){if(a=a||L("./_stream_duplex"),!(this instanceof c))return new c(t);this._readableState=new o(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),i.call(this)}function d(t,e,r,n,i){var o,a,s,u=t._readableState;null===e?(u.reading=!1,function(t,e){if(e.ended)return;if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,E(t)}(t,u)):(i||(o=function(t,e){var r;n=e,f.isBuffer(n)||n instanceof l||"string"==typeof e||void 0===e||t.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));var n;return r}(u,e)),o?t.emit("error",o):u.objectMode||e&&0<e.length?("string"==typeof e||u.objectMode||Object.getPrototypeOf(e)===f.prototype||(a=e,e=f.from(a)),n?u.endEmitted?t.emit("error",new Error("stream.unshift() after end event")):p(t,u,e,!0):u.ended?t.emit("error",new Error("stream.push() after EOF")):(u.reading=!1,u.decoder&&!r?(e=u.decoder.write(e),u.objectMode||0!==e.length?p(t,u,e,!1):j(t,u)):p(t,u,e,!1))):n||(u.reading=!1));return!(s=u).ended&&(s.needReadable||s.length<s.highWaterMark||0===s.length)}function p(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(t.emit("data",r),t.read(0)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&E(t)),j(t,e)}Object.defineProperty(c.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),c.prototype.destroy=n.destroy,c.prototype._undestroy=n.undestroy,c.prototype._destroy=function(t,e){this.push(null),e(t)},c.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof t&&((e=e||n.defaultEncoding)!==n.encoding&&(t=f.from(t,e),e=""),r=!0),d(this,t,e,!1,r)},c.prototype.unshift=function(t){return d(this,t,null,!0,!1)},c.prototype.isPaused=function(){return!1===this._readableState.flowing},c.prototype.setEncoding=function(t){return s||(s=L("string_decoder/").StringDecoder),this._readableState.decoder=new s(t),this._readableState.encoding=t,this};var m=8388608;function w(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!=t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=(m<=(r=t)?r=m:(r--,r|=r>>>1,r|=r>>>2,r|=r>>>4,r|=r>>>8,r|=r>>>16,r++),r)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0));var r}function E(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(v("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?g.nextTick(x,t):x(t))}function x(t){v("emit readable"),t.emit("readable"),A(t)}function j(t,e){e.readingMore||(e.readingMore=!0,g.nextTick(k,t,e))}function k(t,e){for(var r=e.length;!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark&&(v("maybeReadMore read 0"),t.read(0),r!==e.length);)r=e.length;e.readingMore=!1}function S(t){v("readable nexttick read 0"),t.read(0)}function I(t,e){e.reading||(v("resume read 0"),t.read(0)),e.resumeScheduled=!1,e.awaitDrain=0,t.emit("resume"),A(t),e.flowing&&!e.reading&&t.read(0)}function A(t){var e=t._readableState;for(v("flow",e.flowing);e.flowing&&null!==t.read(););}function T(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.head.data:e.buffer.concat(e.length),e.buffer.clear()):r=function(t,e,r){var n;t<e.head.data.length?(n=e.head.data.slice(0,t),e.head.data=e.head.data.slice(t)):n=t===e.head.data.length?e.shift():r?function(t,e){var r=e.head,n=1,i=r.data;t-=i.length;for(;r=r.next;){var o=r.data,a=t>o.length?o.length:t;if(a===o.length?i+=o:i+=o.slice(0,t),0===(t-=a)){a===o.length?(++n,r.next?e.head=r.next:e.head=e.tail=null):(e.head=r).data=o.slice(a);break}++n}return e.length-=n,i}(t,e):function(t,e){var r=f.allocUnsafe(t),n=e.head,i=1;n.data.copy(r),t-=n.data.length;for(;n=n.next;){var o=n.data,a=t>o.length?o.length:t;if(o.copy(r,r.length-t,0,a),0===(t-=a)){a===o.length?(++i,n.next?e.head=n.next:e.head=e.tail=null):(e.head=n).data=o.slice(a);break}++i}return e.length-=i,r}(t,e);return n}(t,e.buffer,e.decoder),r);var r}function O(t){var e=t._readableState;if(0<e.length)throw new Error('"endReadable()" called on non-empty stream');e.endEmitted||(e.ended=!0,g.nextTick(P,e,t))}function P(t,e){t.endEmitted||0!==t.length||(t.endEmitted=!0,e.readable=!1,e.emit("end"))}function M(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}c.prototype.read=function(t){v("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return v("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?O(this):E(this),null;if(0===(t=w(t,e))&&e.ended)return 0===e.length&&O(this),null;var n,i=e.needReadable;return v("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&v("length less than watermark",i=!0),e.ended||e.reading?v("reading or ended",i=!1):i&&(v("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=w(r,e))),null===(n=0<t?T(t,e):null)?(e.needReadable=!0,t=0):e.length-=t,0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&O(this)),null!==n&&this.emit("data",n),n},c.prototype._read=function(t){this.emit("error",new Error("_read() is not implemented"))},c.prototype.pipe=function(r,t){var n=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=r;break;case 1:i.pipes=[i.pipes,r];break;default:i.pipes.push(r)}i.pipesCount+=1,v("pipe count=%d opts=%j",i.pipesCount,t);var e=(!t||!1!==t.end)&&r!==b.stdout&&r!==b.stderr?a:m;function o(t,e){v("onunpipe"),t===n&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,v("cleanup"),r.removeListener("close",d),r.removeListener("finish",p),r.removeListener("drain",u),r.removeListener("error",c),r.removeListener("unpipe",o),n.removeListener("end",a),n.removeListener("end",m),n.removeListener("data",h),f=!0,!i.awaitDrain||r._writableState&&!r._writableState.needDrain||u())}function a(){v("onend"),r.end()}i.endEmitted?g.nextTick(e):n.once("end",e),r.on("unpipe",o);var s,u=(s=n,function(){var t=s._readableState;v("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&y(s,"data")&&(t.flowing=!0,A(s))});r.on("drain",u);var f=!1;var l=!1;function h(t){v("ondata"),(l=!1)!==r.write(t)||l||((1===i.pipesCount&&i.pipes===r||1<i.pipesCount&&-1!==M(i.pipes,r))&&!f&&(v("false write response, pause",n._readableState.awaitDrain),n._readableState.awaitDrain++,l=!0),n.pause())}function c(t){v("onerror",t),m(),r.removeListener("error",c),0===y(r,"error")&&r.emit("error",t)}function d(){r.removeListener("finish",p),m()}function p(){v("onfinish"),r.removeListener("close",d),m()}function m(){v("unpipe"),n.unpipe(r)}return n.on("data",h),function(t,e,r){if("function"==typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?_(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}(r,"error",c),r.once("close",d),r.once("finish",p),r.emit("pipe",n),i.flowing||(v("pipe resume"),n.resume()),r},c.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,r);return this}var a=M(e.pipes,t);return-1===a||(e.pipes.splice(a,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},c.prototype.addListener=c.prototype.on=function(t,e){var r=i.prototype.on.call(this,t,e);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&E(this):g.nextTick(S,this))}return r},c.prototype.resume=function(){var t,e,r=this._readableState;return r.flowing||(v("resume"),r.flowing=!0,t=this,(e=r).resumeScheduled||(e.resumeScheduled=!0,g.nextTick(I,t,e))),this},c.prototype.pause=function(){return v("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(v("pause"),this._readableState.flowing=!1,this.emit("pause")),this},c.prototype.wrap=function(e){var r=this,n=this._readableState,i=!1;for(var t in e.on("end",function(){if(v("wrapped end"),n.decoder&&!n.ended){var t=n.decoder.end();t&&t.length&&r.push(t)}r.push(null)}),e.on("data",function(t){(v("wrapped data"),n.decoder&&(t=n.decoder.write(t)),n.objectMode&&null==t)||(n.objectMode||t&&t.length)&&(r.push(t)||(i=!0,e.pause()))}),e)void 0===this[t]&&"function"==typeof e[t]&&(this[t]=function(t){return function(){return e[t].apply(e,arguments)}}(t));for(var o=0;o<h.length;o++)e.on(h[o],this.emit.bind(this,h[o]));return this._read=function(t){v("wrapped _read",t),i&&(i=!1,e.resume())},this},Object.defineProperty(c.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),c._fromList=T}).call(this,L("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{"./_stream_duplex":229,"./internal/streams/BufferList":234,"./internal/streams/destroy":235,"./internal/streams/stream":236,_process:223,"core-util-is":150,events:151,inherits:166,isarray:170,"process-nextick-args":222,"safe-buffer":241,"string_decoder/":247,util:11}],232:[function(t,e,r){"use strict";e.exports=o;var n=t("./_stream_duplex"),i=t("core-util-is");function o(t){if(!(this instanceof o))return new o(t);n.call(this,t),this._transformState={afterTransform:function(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,(r.writecb=null)!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",a)}function a(){var r=this;"function"==typeof this._flush?this._flush(function(t,e){s(r,t,e)}):s(this,null,null)}function s(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(t._transformState.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}i.inherits=t("inherits"),i.inherits(o,n),o.prototype.push=function(t,e){return this._transformState.needTransform=!1,n.prototype.push.call(this,t,e)},o.prototype._transform=function(t,e,r){throw new Error("_transform() is not implemented")},o.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},o.prototype._read=function(t){var e=this._transformState;null!==e.writechunk&&e.writecb&&!e.transforming?(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform)):e.needTransform=!0},o.prototype._destroy=function(t,e){var r=this;n.prototype._destroy.call(this,t,function(t){e(t),r.emit("close")})}},{"./_stream_duplex":229,"core-util-is":150,inherits:166}],233:[function(x,j,t){(function(t,e,r){"use strict";var g=x("process-nextick-args");function h(t){var e=this;this.next=null,this.entry=null,this.finish=function(){!function(t,e,r){var n=t.entry;t.entry=null;for(;n;){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}(e,t)}}j.exports=l;var s,c=!t.browser&&-1<["v0.10","v0.9."].indexOf(t.version.slice(0,5))?r:g.nextTick;l.WritableState=f;var n=x("core-util-is");n.inherits=x("inherits");var i={deprecate:x("util-deprecate")},o=x("./internal/streams/stream"),_=x("safe-buffer").Buffer,y=e.Uint8Array||function(){};var a,u=x("./internal/streams/destroy");function v(){}function f(t,e){s=s||x("./_stream_duplex"),t=t||{};var r=e instanceof s;this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var n=t.highWaterMark,i=t.writableHighWaterMark,o=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:r&&(i||0===i)?i:o,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1;var a=(this.destroyed=!1)===t.decodeStrings;this.decodeStrings=!a,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){!function(t,e){var r=t._writableState,n=r.sync,i=r.writecb;if(h=r,h.writing=!1,h.writecb=null,h.length-=h.writelen,h.writelen=0,e)a=t,s=r,u=n,f=e,l=i,--s.pendingcb,u?(g.nextTick(l,f),g.nextTick(E,a,s),a._writableState.errorEmitted=!0,a.emit("error",f)):(l(f),a._writableState.errorEmitted=!0,a.emit("error",f),E(a,s));else{var o=m(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||p(t,r),n?c(d,t,r,o,i):d(t,r,o,i)}var a,s,u,f,l;var h}(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new h(this)}function l(t){if(s=s||x("./_stream_duplex"),!(a.call(l,this)||this instanceof s))return new l(t);this._writableState=new f(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),o.call(this)}function w(t,e,r,n,i,o,a){e.writelen=n,e.writecb=a,e.writing=!0,e.sync=!0,r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function d(t,e,r,n){var i,o;r||(i=t,0===(o=e).length&&o.needDrain&&(o.needDrain=!1,i.emit("drain"))),e.pendingcb--,n(),E(t,e)}function p(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var n=e.bufferedRequestCount,i=new Array(n),o=e.corkedRequestsFree;o.entry=r;for(var a=0,s=!0;r;)(i[a]=r).isBuf||(s=!1),r=r.next,a+=1;i.allBuffers=s,w(t,e,!0,e.length,i,"",o.finish),e.pendingcb++,e.lastBufferedRequest=null,o.next?(e.corkedRequestsFree=o.next,o.next=null):e.corkedRequestsFree=new h(e),e.bufferedRequestCount=0}else{for(;r;){var u=r.chunk,f=r.encoding,l=r.callback;if(w(t,e,!1,e.objectMode?1:u.length,u,f,l),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function m(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function b(e,r){e._final(function(t){r.pendingcb--,t&&e.emit("error",t),r.prefinished=!0,e.emit("prefinish"),E(e,r)})}function E(t,e){var r,n,i=m(e);return i&&(r=t,(n=e).prefinished||n.finalCalled||("function"==typeof r._final?(n.pendingcb++,n.finalCalled=!0,g.nextTick(b,r,n)):(n.prefinished=!0,r.emit("prefinish"))),0===e.pendingcb&&(e.finished=!0,t.emit("finish"))),i}n.inherits(l,o),f.prototype.getBuffer=function(){for(var t=this.bufferedRequest,e=[];t;)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(f.prototype,"buffer",{get:i.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(a=Function.prototype[Symbol.hasInstance],Object.defineProperty(l,Symbol.hasInstance,{value:function(t){return!!a.call(this,t)||this===l&&(t&&t._writableState instanceof f)}})):a=function(t){return t instanceof this},l.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},l.prototype.write=function(t,e,r){var n,i,o,a,s,u,f,l,h,c,d,p=this._writableState,m=!1,b=!p.objectMode&&(n=t,_.isBuffer(n)||n instanceof y);return b&&!_.isBuffer(t)&&(i=t,t=_.from(i)),"function"==typeof e&&(r=e,e=null),b?e="buffer":e||(e=p.defaultEncoding),"function"!=typeof r&&(r=v),p.ended?(h=this,c=r,d=new Error("write after end"),h.emit("error",d),g.nextTick(c,d)):(b||(o=this,a=p,u=r,l=!(f=!0),null===(s=t)?l=new TypeError("May not write null values to stream"):"string"==typeof s||void 0===s||a.objectMode||(l=new TypeError("Invalid non-string/buffer chunk")),l&&(o.emit("error",l),g.nextTick(u,l),f=!1),f))&&(p.pendingcb++,m=function(t,e,r,n,i,o){if(!r){var a=function(t,e,r){t.objectMode||!1===t.decodeStrings||"string"!=typeof e||(e=_.from(e,r));return e}(e,n,i);n!==a&&(r=!0,i="buffer",n=a)}var s=e.objectMode?1:n.length;e.length+=s;var u=e.length<e.highWaterMark;u||(e.needDrain=!0);if(e.writing||e.corked){var f=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},f?f.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else w(t,e,!1,s,n,i,o);return u}(this,p,b,t,e,r)),m},l.prototype.cork=function(){this._writableState.corked++},l.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.finished||t.bufferProcessing||!t.bufferedRequest||p(this,t))},l.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!(-1<["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())))throw new TypeError("Unknown encoding: "+t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(l.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),l.prototype._write=function(t,e,r){r(new Error("_write() is not implemented"))},l.prototype._writev=null,l.prototype.end=function(t,e,r){var n=this._writableState;"function"==typeof t?(r=t,e=t=null):"function"==typeof e&&(r=e,e=null),null!=t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||function(t,e,r){e.ending=!0,E(t,e),r&&(e.finished?g.nextTick(r):t.once("finish",r));e.ended=!0,t.writable=!1}(this,n,r)},Object.defineProperty(l.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),l.prototype.destroy=u.destroy,l.prototype._undestroy=u.undestroy,l.prototype._destroy=function(t,e){this.end(),e(t)}}).call(this,x("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{},x("timers").setImmediate)},{"./_stream_duplex":229,"./internal/streams/destroy":235,"./internal/streams/stream":236,_process:223,"core-util-is":150,inherits:166,"process-nextick-args":222,"safe-buffer":241,timers:248,"util-deprecate":256}],234:[function(t,e,r){"use strict";var s=t("safe-buffer").Buffer,n=t("util");e.exports=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.head=null,this.tail=null,this.length=0}return t.prototype.push=function(t){var e={data:t,next:null};0<this.length?this.tail.next=e:this.head=e,this.tail=e,++this.length},t.prototype.unshift=function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length},t.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},t.prototype.clear=function(){this.head=this.tail=null,this.length=0},t.prototype.join=function(t){if(0===this.length)return"";for(var e=this.head,r=""+e.data;e=e.next;)r+=t+e.data;return r},t.prototype.concat=function(t){if(0===this.length)return s.alloc(0);if(1===this.length)return this.head.data;for(var e,r,n,i=s.allocUnsafe(t>>>0),o=this.head,a=0;o;)e=o.data,r=i,n=a,e.copy(r,n),a+=o.data.length,o=o.next;return i},t}(),n&&n.inspect&&n.inspect.custom&&(e.exports.prototype[n.inspect.custom]=function(){var t=n.inspect({length:this.length});return this.constructor.name+" "+t})},{"safe-buffer":241,util:11}],235:[function(t,e,r){"use strict";var o=t("process-nextick-args");function a(t,e){t.emit("error",e)}e.exports={destroy:function(t,e){var r=this,n=this._readableState&&this._readableState.destroyed,i=this._writableState&&this._writableState.destroyed;return n||i?e?e(t):!t||this._writableState&&this._writableState.errorEmitted||o.nextTick(a,this,t):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,function(t){!e&&t?(o.nextTick(a,r,t),r._writableState&&(r._writableState.errorEmitted=!0)):e&&e(t)})),this},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},{"process-nextick-args":222}],236:[function(t,e,r){e.exports=t("events").EventEmitter},{events:151}],237:[function(t,e,r){e.exports=t("./readable").PassThrough},{"./readable":238}],238:[function(t,e,r){(((r=e.exports=t("./lib/_stream_readable.js")).Stream=r).Readable=r).Writable=t("./lib/_stream_writable.js"),r.Duplex=t("./lib/_stream_duplex.js"),r.Transform=t("./lib/_stream_transform.js"),r.PassThrough=t("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":229,"./lib/_stream_passthrough.js":230,"./lib/_stream_readable.js":231,"./lib/_stream_transform.js":232,"./lib/_stream_writable.js":233}],239:[function(t,e,r){e.exports=t("./readable").Transform},{"./readable":238}],240:[function(t,e,r){e.exports=t("./lib/_stream_writable.js")},{"./lib/_stream_writable.js":233}],241:[function(t,e,r){var n=t("buffer"),i=n.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function a(t,e,r){return i(t,e,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(o(n,r),r.Buffer=a),o(i,a),a.from=function(t,e,r){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,r)},a.alloc=function(t,e,r){if("number"!=typeof t)throw new TypeError("Argument must be a number");var n=i(t);return void 0!==e?"string"==typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},a.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},a.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},{buffer:16}],242:[function(t,e,r){e.exports=n;var l=t("events").EventEmitter;function n(){l.call(this)}t("inherits")(n,l),n.Readable=t("readable-stream/readable.js"),n.Writable=t("readable-stream/writable.js"),n.Duplex=t("readable-stream/duplex.js"),n.Transform=t("readable-stream/transform.js"),n.PassThrough=t("readable-stream/passthrough.js"),(n.Stream=n).prototype.pipe=function(e,t){var r=this;function n(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function i(){r.readable&&r.resume&&r.resume()}r.on("data",n),e.on("drain",i),e._isStdio||t&&!1===t.end||(r.on("end",a),r.on("close",s));var o=!1;function a(){o||(o=!0,e.end())}function s(){o||(o=!0,"function"==typeof e.destroy&&e.destroy())}function u(t){if(f(),0===l.listenerCount(this,"error"))throw t}function f(){r.removeListener("data",n),e.removeListener("drain",i),r.removeListener("end",a),r.removeListener("close",s),r.removeListener("error",u),e.removeListener("error",u),r.removeListener("end",f),r.removeListener("close",f),e.removeListener("close",f)}return r.on("error",u),e.on("error",u),r.on("end",f),r.on("close",f),e.on("close",f),e.emit("pipe",r),e}},{events:151,inherits:166,"readable-stream/duplex.js":228,"readable-stream/passthrough.js":237,"readable-stream/readable.js":238,"readable-stream/transform.js":239,"readable-stream/writable.js":240}],243:[function(r,t,i){(function(u){var f=r("./lib/request"),t=r("./lib/response"),l=r("xtend"),e=r("builtin-status-codes"),h=r("url"),n=i;n.request=function(t,e){t="string"==typeof t?h.parse(t):l(t);var r=-1===u.location.protocol.search(/^https?:$/)?"http:":"",n=t.protocol||r,i=t.hostname||t.host,o=t.port,a=t.path||"/";i&&-1!==i.indexOf(":")&&(i="["+i+"]"),t.url=(i?n+"//"+i:"")+(o?":"+o:"")+a,t.method=(t.method||"GET").toUpperCase(),t.headers=t.headers||{};var s=new f(t);return e&&s.on("response",e),s},n.get=function(t,e){var r=n.request(t,e);return r.end(),r},n.ClientRequest=f,n.IncomingMessage=t.IncomingMessage,n.Agent=function(){},n.Agent.defaultMaxSockets=4,n.globalAgent=new n.Agent,n.STATUS_CODES=e,n.METHODS=["CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REPORT","SEARCH","SUBSCRIBE","TRACE","UNLOCK","UNSUBSCRIBE"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{"./lib/request":245,"./lib/response":246,"builtin-status-codes":17,url:253,xtend:261}],244:[function(t,e,s){(function(t){s.fetch=a(t.fetch)&&a(t.ReadableStream),s.writableStream=a(t.WritableStream),s.abortController=a(t.AbortController),s.blobConstructor=!1;try{new Blob([new ArrayBuffer(1)]),s.blobConstructor=!0}catch(t){}var e;function r(){if(void 0!==e)return e;if(t.XMLHttpRequest){e=new t.XMLHttpRequest;try{e.open("GET",t.XDomainRequest?"/":"https://example.com")}catch(t){e=null}}else e=null;return e}function n(t){var e=r();if(!e)return!1;try{return e.responseType=t,e.responseType===t}catch(t){}return!1}var i=void 0!==t.ArrayBuffer,o=i&&a(t.ArrayBuffer.prototype.slice);function a(t){return"function"==typeof t}s.arraybuffer=s.fetch||i&&n("arraybuffer"),s.msstream=!s.fetch&&o&&n("ms-stream"),s.mozchunkedarraybuffer=!s.fetch&&i&&n("moz-chunked-arraybuffer"),s.overrideMimeType=s.fetch||!!r()&&a(r().overrideMimeType),s.vbArray=a(t.VBArray),e=null}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{}],245:[function(o,s,t){(function(u,f,l){var h=o("./capability"),t=o("inherits"),e=o("./response"),a=o("readable-stream"),c=o("to-arraybuffer"),r=e.IncomingMessage,d=e.readyStates;var n=s.exports=function(e){var t,r=this;a.Writable.call(r),r._opts=e,r._body=[],r._headers={},e.auth&&r.setHeader("Authorization","Basic "+new l(e.auth).toString("base64")),Object.keys(e.headers).forEach(function(t){r.setHeader(t,e.headers[t])});var n,i,o=!0;if("disable-fetch"===e.mode||"requestTimeout"in e&&!h.abortController)t=!(o=!1);else if("prefer-streaming"===e.mode)t=!1;else if("allow-wrong-content-type"===e.mode)t=!h.overrideMimeType;else{if(e.mode&&"default"!==e.mode&&"prefer-fast"!==e.mode)throw new Error("Invalid value for opts.mode");t=!0}r._mode=(n=t,i=o,h.fetch&&i?"fetch":h.mozchunkedarraybuffer?"moz-chunked-arraybuffer":h.msstream?"ms-stream":h.arraybuffer&&n?"arraybuffer":h.vbArray&&n?"text:vbarray":"text"),r._fetchTimer=null,r.on("finish",function(){r._onFinish()})};t(n,a.Writable),n.prototype.setHeader=function(t,e){var r=t.toLowerCase();-1===i.indexOf(r)&&(this._headers[r]={name:t,value:e})},n.prototype.getHeader=function(t){var e=this._headers[t.toLowerCase()];return e?e.value:null},n.prototype.removeHeader=function(t){delete this._headers[t.toLowerCase()]},n.prototype._onFinish=function(){var e=this;if(!e._destroyed){var t=e._opts,n=e._headers,r=null;"GET"!==t.method&&"HEAD"!==t.method&&(r=h.arraybuffer?c(l.concat(e._body)):h.blobConstructor?new f.Blob(e._body.map(function(t){return c(t)}),{type:(n["content-type"]||{}).value||""}):l.concat(e._body).toString());var i=[];if(Object.keys(n).forEach(function(t){var e=n[t].name,r=n[t].value;Array.isArray(r)?r.forEach(function(t){i.push([e,t])}):i.push([e,r])}),"fetch"===e._mode){var o=null;if(h.abortController){var a=new AbortController;o=a.signal,e._fetchAbortController=a,"requestTimeout"in t&&0!==t.requestTimeout&&(e._fetchTimer=f.setTimeout(function(){e.emit("requestTimeout"),e._fetchAbortController&&e._fetchAbortController.abort()},t.requestTimeout))}f.fetch(e._opts.url,{method:e._opts.method,headers:i,body:r||void 0,mode:"cors",credentials:t.withCredentials?"include":"same-origin",signal:o}).then(function(t){e._fetchResponse=t,e._connect()},function(t){f.clearTimeout(e._fetchTimer),e._destroyed||e.emit("error",t)})}else{var s=e._xhr=new f.XMLHttpRequest;try{s.open(e._opts.method,e._opts.url,!0)}catch(t){return void u.nextTick(function(){e.emit("error",t)})}"responseType"in s&&(s.responseType=e._mode.split(":")[0]),"withCredentials"in s&&(s.withCredentials=!!t.withCredentials),"text"===e._mode&&"overrideMimeType"in s&&s.overrideMimeType("text/plain; charset=x-user-defined"),"requestTimeout"in t&&(s.timeout=t.requestTimeout,s.ontimeout=function(){e.emit("requestTimeout")}),i.forEach(function(t){s.setRequestHeader(t[0],t[1])}),e._response=null,s.onreadystatechange=function(){switch(s.readyState){case d.LOADING:case d.DONE:e._onXHRProgress()}},"moz-chunked-arraybuffer"===e._mode&&(s.onprogress=function(){e._onXHRProgress()}),s.onerror=function(){e._destroyed||e.emit("error",new Error("XHR error"))};try{s.send(r)}catch(t){return void u.nextTick(function(){e.emit("error",t)})}}}},n.prototype._onXHRProgress=function(){(function(t){try{var e=t.status;return null!==e&&0!==e}catch(t){return!1}})(this._xhr)&&!this._destroyed&&(this._response||this._connect(),this._response._onXHRProgress())},n.prototype._connect=function(){var e=this;e._destroyed||(e._response=new r(e._xhr,e._fetchResponse,e._mode,e._fetchTimer),e._response.on("error",function(t){e.emit("error",t)}),e.emit("response",e._response))},n.prototype._write=function(t,e,r){this._body.push(t),r()},n.prototype.abort=n.prototype.destroy=function(){this._destroyed=!0,f.clearTimeout(this._fetchTimer),this._response&&(this._response._destroyed=!0),this._xhr?this._xhr.abort():this._fetchAbortController&&this._fetchAbortController.abort()},n.prototype.end=function(t,e,r){"function"==typeof t&&(r=t,t=void 0),a.Writable.prototype.end.call(this,t,e,r)},n.prototype.flushHeaders=function(){},n.prototype.setTimeout=function(){},n.prototype.setNoDelay=function(){},n.prototype.setSocketKeepAlive=function(){};var i=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"]}).call(this,o("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{},o("buffer").Buffer)},{"./capability":244,"./response":246,_process:223,buffer:16,inherits:166,"readable-stream":238,"to-arraybuffer":251}],246:[function(r,t,n){(function(f,l,h){var c=r("./capability"),t=r("inherits"),d=r("readable-stream"),s=n.readyStates={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},e=n.IncomingMessage=function(t,e,r,n){var i=this;if(d.Readable.call(i),i._mode=r,i.headers={},i.rawHeaders=[],i.trailers={},i.rawTrailers=[],i.on("end",function(){f.nextTick(function(){i.emit("close")})}),"fetch"===r){if(i._fetchResponse=e,i.url=e.url,i.statusCode=e.status,i.statusMessage=e.statusText,e.headers.forEach(function(t,e){i.headers[e.toLowerCase()]=t,i.rawHeaders.push(e,t)}),c.writableStream){var o=new WritableStream({write:function(r){return new Promise(function(t,e){i._destroyed?e():i.push(new h(r))?t():i._resumeFetch=t})},close:function(){l.clearTimeout(n),i._destroyed||i.push(null)},abort:function(t){i._destroyed||i.emit("error",t)}});try{return void e.body.pipeTo(o).catch(function(t){l.clearTimeout(n),i._destroyed||i.emit("error",t)})}catch(t){}}var a=e.body.getReader();!function e(){a.read().then(function(t){if(!i._destroyed){if(t.done)return l.clearTimeout(n),void i.push(null);i.push(new h(t.value)),e()}}).catch(function(t){l.clearTimeout(n),i._destroyed||i.emit("error",t)})}()}else{if(i._xhr=t,i._pos=0,i.url=t.responseURL,i.statusCode=t.status,i.statusMessage=t.statusText,t.getAllResponseHeaders().split(/\r?\n/).forEach(function(t){var e=t.match(/^([^:]+):\s*(.*)/);if(e){var r=e[1].toLowerCase();"set-cookie"===r?(void 0===i.headers[r]&&(i.headers[r]=[]),i.headers[r].push(e[2])):void 0!==i.headers[r]?i.headers[r]+=", "+e[2]:i.headers[r]=e[2],i.rawHeaders.push(e[1],e[2])}}),i._charset="x-user-defined",!c.overrideMimeType){var s=i.rawHeaders["mime-type"];if(s){var u=s.match(/;\s*charset=([^;])(;|$)/);u&&(i._charset=u[1].toLowerCase())}i._charset||(i._charset="utf-8")}}};t(e,d.Readable),e.prototype._read=function(){var t=this._resumeFetch;t&&(this._resumeFetch=null,t())},e.prototype._onXHRProgress=function(){var e=this,t=e._xhr,r=null;switch(e._mode){case"text:vbarray":if(t.readyState!==s.DONE)break;try{r=new l.VBArray(t.responseBody).toArray()}catch(t){}if(null!==r){e.push(new h(r));break}case"text":try{r=t.responseText}catch(t){e._mode="text:vbarray";break}if(r.length>e._pos){var n=r.substr(e._pos);if("x-user-defined"===e._charset){for(var i=new h(n.length),o=0;o<n.length;o++)i[o]=255&n.charCodeAt(o);e.push(i)}else e.push(n,e._charset);e._pos=r.length}break;case"arraybuffer":if(t.readyState!==s.DONE||!t.response)break;r=t.response,e.push(new h(new Uint8Array(r)));break;case"moz-chunked-arraybuffer":if(r=t.response,t.readyState!==s.LOADING||!r)break;e.push(new h(new Uint8Array(r)));break;case"ms-stream":if(r=t.response,t.readyState!==s.LOADING)break;var a=new l.MSStreamReader;a.onprogress=function(){a.result.byteLength>e._pos&&(e.push(new h(new Uint8Array(a.result.slice(e._pos)))),e._pos=a.result.byteLength)},a.onload=function(){e.push(null)},a.readAsArrayBuffer(r)}e._xhr.readyState===s.DONE&&"ms-stream"!==e._mode&&e.push(null)}}).call(this,r("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{},r("buffer").Buffer)},{"./capability":244,_process:223,buffer:16,inherits:166,"readable-stream":238}],247:[function(t,e,r){"use strict";var n=t("safe-buffer").Buffer,i=n.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!=typeof e&&(n.isEncoding===i||!i(t)))throw new Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=u,this.end=f,e=4;break;case"utf8":this.fillLast=s,e=4;break;case"base64":this.text=l,this.end=h,e=3;break;default:return this.write=c,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(e)}function a(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function s(t){var e=this.lastTotal-this.lastNeed,r=function(t,e,r){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(1<t.lastNeed&&1<e.length){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(2<t.lastNeed&&2<e.length&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==r?r:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function u(t,e){if((t.length-e)%2!=0)return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1);var r=t.toString("utf16le",e);if(r){var n=r.charCodeAt(r.length-1);if(55296<=n&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}function f(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function l(t,e){var r=(t.length-e)%3;return 0===r?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function h(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function c(t){return t.toString(this.encoding)}function d(t){return t&&t.length?this.write(t):""}(r.StringDecoder=o).prototype.write=function(t){if(0===t.length)return"";var e,r;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""},o.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},o.prototype.text=function(t,e){var r=function(t,e,r){var n=e.length-1;if(n<r)return 0;var i=a(e[n]);if(0<=i)return 0<i&&(t.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if(0<=(i=a(e[n])))return 0<i&&(t.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if(0<=(i=a(e[n])))return 0<i&&(2===i?i=0:t.lastNeed=i-3),i;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var n=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)},o.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},{"safe-buffer":241}],248:[function(u,t,f){(function(t,e){var n=u("process/browser.js").nextTick,r=Function.prototype.apply,i=Array.prototype.slice,o={},a=0;function s(t,e){this._id=t,this._clearFn=e}f.setTimeout=function(){return new s(r.call(setTimeout,window,arguments),clearTimeout)},f.setInterval=function(){return new s(r.call(setInterval,window,arguments),clearInterval)},f.clearTimeout=f.clearInterval=function(t){t.close()},s.prototype.unref=s.prototype.ref=function(){},s.prototype.close=function(){this._clearFn.call(window,this._id)},f.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},f.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},f._unrefActive=f.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;0<=e&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},e))},f.setImmediate="function"==typeof t?t:function(t){var e=a++,r=!(arguments.length<2)&&i.call(arguments,1);return o[e]=!0,n(function(){o[e]&&(r?t.apply(null,r):t.call(null),f.clearImmediate(e))}),e},f.clearImmediate="function"==typeof e?e:function(t){delete o[t]}}).call(this,u("timers").setImmediate,u("timers").clearImmediate)},{"process/browser.js":223,timers:248}],249:[function(t,e,I){(function(p){"use strict";Object.defineProperty(I,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};I.clone=_,I.addLast=t,I.addFirst=e,I.removeLast=n,I.removeFirst=i,I.insert=o,I.removeAt=a,I.replaceAt=s,I.getIn=h,I.set=u,I.setIn=c,I.update=l,I.updateIn=d,I.merge=w,I.mergeDeep=E,I.mergeIn=x,I.omit=j,I.addDefaults=k;var m="INVALID_ARGS";function b(t){throw new Error(t)}function g(t){var e=Object.keys(t);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e}var f={}.hasOwnProperty;function _(t){if(Array.isArray(t))return t.slice();for(var e=g(t),r={},n=0;n<e.length;n++){var i=e[n];r[i]=t[i]}return r}function y(t,e,r){var n=r;null==n&&b("production"!==p.env.NODE_ENV?"At least one object should be provided to merge()":m);for(var i=!1,o=arguments.length,a=Array(3<o?o-3:0),s=3;s<o;s++)a[s-3]=arguments[s];for(var u=0;u<a.length;u++){var f=a[u];if(null!=f){var l=g(f);if(l.length)for(var h=0;h<=l.length;h++){var c=l[h];if(!t||void 0===n[c]){var d=f[c];e&&v(n[c])&&v(d)&&(d=y(t,e,n[c],d)),void 0!==d&&d!==n[c]&&(i||(i=!0,n=_(n)),n[c]=d)}}}}return n}function v(t){var e=void 0===t?"undefined":r(t);return null!=t&&"object"===e}function t(t,e){return Array.isArray(e)?t.concat(e):t.concat([e])}function e(t,e){return Array.isArray(e)?e.concat(t):[e].concat(t)}function n(t){return t.length?t.slice(0,t.length-1):t}function i(t){return t.length?t.slice(1):t}function o(t,e,r){return t.slice(0,e).concat(Array.isArray(r)?r:[r]).concat(t.slice(e))}function a(t,e){return e>=t.length||e<0?t:t.slice(0,e).concat(t.slice(e+1))}function s(t,e,r){if(t[e]===r)return t;for(var n=t.length,i=Array(n),o=0;o<n;o++)i[o]=t[o];return i[e]=r,i}function h(t,e){if(!Array.isArray(e)&&b("production"!==p.env.NODE_ENV?"A path array should be provided when calling getIn()":m),null!=t){for(var r=t,n=0;n<e.length;n++){var i=e[n];if(void 0===(r=null!=r?r[i]:void 0))return r}return r}}function u(t,e,r){var n=null==t?"number"==typeof e?[]:{}:t;if(n[e]===r)return n;var i=_(n);return i[e]=r,i}function c(t,e,r){return e.length?function t(e,r,n,i){var o=void 0,a=r[i];o=i===r.length-1?n:t(v(e)&&v(e[a])?e[a]:"number"==typeof r[i+1]?[]:{},r,n,i+1);return u(e,a,o)}(t,e,r,0):r}function l(t,e,r){return u(t,e,r(null==t?void 0:t[e]))}function d(t,e,r){return c(t,e,r(h(t,e)))}function w(t,e,r,n,i,o){for(var a=arguments.length,s=Array(6<a?a-6:0),u=6;u<a;u++)s[u-6]=arguments[u];return s.length?y.call.apply(y,[null,!1,!1,t,e,r,n,i,o].concat(s)):y(!1,!1,t,e,r,n,i,o)}function E(t,e,r,n,i,o){for(var a=arguments.length,s=Array(6<a?a-6:0),u=6;u<a;u++)s[u-6]=arguments[u];return s.length?y.call.apply(y,[null,!1,!0,t,e,r,n,i,o].concat(s)):y(!1,!0,t,e,r,n,i,o)}function x(t,e,r,n,i,o,a){var s=h(t,e);null==s&&(s={});for(var u=arguments.length,f=Array(7<u?u-7:0),l=7;l<u;l++)f[l-7]=arguments[l];return c(t,e,f.length?y.call.apply(y,[null,!1,!1,s,r,n,i,o,a].concat(f)):y(!1,!1,s,r,n,i,o,a))}function j(t,e){for(var r=Array.isArray(e)?e:[e],n=!1,i=0;i<r.length;i++)if(f.call(t,r[i])){n=!0;break}if(!n)return t;for(var o={},a=g(t),s=0;s<a.length;s++){var u=a[s];0<=r.indexOf(u)||(o[u]=t[u])}return o}function k(t,e,r,n,i,o){for(var a=arguments.length,s=Array(6<a?a-6:0),u=6;u<a;u++)s[u-6]=arguments[u];return s.length?y.call.apply(y,[null,!0,!1,t,e,r,n,i,o].concat(s)):y(!0,!1,t,e,r,n,i,o)}var S={clone:_,addLast:t,addFirst:e,removeLast:n,removeFirst:i,insert:o,removeAt:a,replaceAt:s,getIn:h,set:u,setIn:c,update:l,updateIn:d,merge:w,mergeDeep:E,mergeIn:x,omit:j,addDefaults:k};I.default=S}).call(this,t("_process"))},{_process:223}],250:[function(t,z,e){!function(h){var c=/^\s+/,d=/\s+$/,n=0,a=h.round,p=h.min,m=h.max,t=h.random;function l(t,e){if(e=e||{},(t=t||"")instanceof l)return t;if(!(this instanceof l))return new l(t,e);var r=function(t){var e={r:0,g:0,b:0},r=1,n=null,i=null,o=null,a=!1,s=!1;"string"==typeof t&&(t=function(t){t=t.replace(c,"").replace(d,"").toLowerCase();var e,r=!1;if(S[t])t=S[t],r=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(e=U.rgb.exec(t))return{r:e[1],g:e[2],b:e[3]};if(e=U.rgba.exec(t))return{r:e[1],g:e[2],b:e[3],a:e[4]};if(e=U.hsl.exec(t))return{h:e[1],s:e[2],l:e[3]};if(e=U.hsla.exec(t))return{h:e[1],s:e[2],l:e[3],a:e[4]};if(e=U.hsv.exec(t))return{h:e[1],s:e[2],v:e[3]};if(e=U.hsva.exec(t))return{h:e[1],s:e[2],v:e[3],a:e[4]};if(e=U.hex8.exec(t))return{r:P(e[1]),g:P(e[2]),b:P(e[3]),a:R(e[4]),format:r?"name":"hex8"};if(e=U.hex6.exec(t))return{r:P(e[1]),g:P(e[2]),b:P(e[3]),format:r?"name":"hex"};if(e=U.hex4.exec(t))return{r:P(e[1]+""+e[1]),g:P(e[2]+""+e[2]),b:P(e[3]+""+e[3]),a:R(e[4]+""+e[4]),format:r?"name":"hex8"};if(e=U.hex3.exec(t))return{r:P(e[1]+""+e[1]),g:P(e[2]+""+e[2]),b:P(e[3]+""+e[3]),format:r?"name":"hex"};return!1}(t));"object"==typeof t&&(F(t.r)&&F(t.g)&&F(t.b)?(u=t.r,f=t.g,l=t.b,e={r:255*T(u,255),g:255*T(f,255),b:255*T(l,255)},a=!0,s="%"===String(t.r).substr(-1)?"prgb":"rgb"):F(t.h)&&F(t.s)&&F(t.v)?(n=L(t.s),i=L(t.v),e=function(t,e,r){t=6*T(t,360),e=T(e,100),r=T(r,100);var n=h.floor(t),i=t-n,o=r*(1-e),a=r*(1-i*e),s=r*(1-(1-i)*e),u=n%6;return{r:255*[r,a,o,o,s,r][u],g:255*[s,r,r,a,o,o][u],b:255*[o,o,s,r,r,a][u]}}(t.h,n,i),a=!0,s="hsv"):F(t.h)&&F(t.s)&&F(t.l)&&(n=L(t.s),o=L(t.l),e=function(t,e,r){var n,i,o;function a(t,e,r){return r<0&&(r+=1),1<r&&(r-=1),r<1/6?t+6*(e-t)*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}if(t=T(t,360),e=T(e,100),r=T(r,100),0===e)n=i=o=r;else{var s=r<.5?r*(1+e):r+e-r*e,u=2*r-s;n=a(u,s,t+1/3),i=a(u,s,t),o=a(u,s,t-1/3)}return{r:255*n,g:255*i,b:255*o}}(t.h,n,o),a=!0,s="hsl"),t.hasOwnProperty("a")&&(r=t.a));var u,f,l;return r=A(r),{ok:a,format:t.format||s,r:p(255,m(e.r,0)),g:p(255,m(e.g,0)),b:p(255,m(e.b,0)),a:r}}(t);this._originalInput=t,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=a(100*this._a)/100,this._format=e.format||r.format,this._gradientType=e.gradientType,this._r<1&&(this._r=a(this._r)),this._g<1&&(this._g=a(this._g)),this._b<1&&(this._b=a(this._b)),this._ok=r.ok,this._tc_id=n++}function i(t,e,r){t=T(t,255),e=T(e,255),r=T(r,255);var n,i,o=m(t,e,r),a=p(t,e,r),s=(o+a)/2;if(o==a)n=i=0;else{var u=o-a;switch(i=.5<s?u/(2-o-a):u/(o+a),o){case t:n=(e-r)/u+(e<r?6:0);break;case e:n=(r-t)/u+2;break;case r:n=(t-e)/u+4}n/=6}return{h:n,s:i,l:s}}function o(t,e,r){t=T(t,255),e=T(e,255),r=T(r,255);var n,i,o=m(t,e,r),a=p(t,e,r),s=o,u=o-a;if(i=0===o?0:u/o,o==a)n=0;else{switch(o){case t:n=(e-r)/u+(e<r?6:0);break;case e:n=(r-t)/u+2;break;case r:n=(t-e)/u+4}n/=6}return{h:n,s:i,v:s}}function e(t,e,r,n){var i=[M(a(t).toString(16)),M(a(e).toString(16)),M(a(r).toString(16))];return n&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function s(t,e,r,n){return[M(C(n)),M(a(t).toString(16)),M(a(e).toString(16)),M(a(r).toString(16))].join("")}function r(t,e){e=0===e?0:e||10;var r=l(t).toHsl();return r.s-=e/100,r.s=O(r.s),l(r)}function u(t,e){e=0===e?0:e||10;var r=l(t).toHsl();return r.s+=e/100,r.s=O(r.s),l(r)}function f(t){return l(t).desaturate(100)}function b(t,e){e=0===e?0:e||10;var r=l(t).toHsl();return r.l+=e/100,r.l=O(r.l),l(r)}function g(t,e){e=0===e?0:e||10;var r=l(t).toRgb();return r.r=m(0,p(255,r.r-a(-e/100*255))),r.g=m(0,p(255,r.g-a(-e/100*255))),r.b=m(0,p(255,r.b-a(-e/100*255))),l(r)}function _(t,e){e=0===e?0:e||10;var r=l(t).toHsl();return r.l-=e/100,r.l=O(r.l),l(r)}function y(t,e){var r=l(t).toHsl(),n=(r.h+e)%360;return r.h=n<0?360+n:n,l(r)}function v(t){var e=l(t).toHsl();return e.h=(e.h+180)%360,l(e)}function w(t){var e=l(t).toHsl(),r=e.h;return[l(t),l({h:(r+120)%360,s:e.s,l:e.l}),l({h:(r+240)%360,s:e.s,l:e.l})]}function E(t){var e=l(t).toHsl(),r=e.h;return[l(t),l({h:(r+90)%360,s:e.s,l:e.l}),l({h:(r+180)%360,s:e.s,l:e.l}),l({h:(r+270)%360,s:e.s,l:e.l})]}function x(t){var e=l(t).toHsl(),r=e.h;return[l(t),l({h:(r+72)%360,s:e.s,l:e.l}),l({h:(r+216)%360,s:e.s,l:e.l})]}function j(t,e,r){e=e||6,r=r||30;var n=l(t).toHsl(),i=360/r,o=[l(t)];for(n.h=(n.h-(i*e>>1)+720)%360;--e;)n.h=(n.h+i)%360,o.push(l(n));return o}function k(t,e){e=e||6;for(var r=l(t).toHsv(),n=r.h,i=r.s,o=r.v,a=[],s=1/e;e--;)a.push(l({h:n,s:i,v:o})),o=(o+s)%1;return a}l.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,e,r,n=this.toRgb();return t=n.r/255,e=n.g/255,r=n.b/255,.2126*(t<=.03928?t/12.92:h.pow((t+.055)/1.055,2.4))+.7152*(e<=.03928?e/12.92:h.pow((e+.055)/1.055,2.4))+.0722*(r<=.03928?r/12.92:h.pow((r+.055)/1.055,2.4))},setAlpha:function(t){return this._a=A(t),this._roundA=a(100*this._a)/100,this},toHsv:function(){var t=o(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=o(this._r,this._g,this._b),e=a(360*t.h),r=a(100*t.s),n=a(100*t.v);return 1==this._a?"hsv("+e+", "+r+"%, "+n+"%)":"hsva("+e+", "+r+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var t=i(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=i(this._r,this._g,this._b),e=a(360*t.h),r=a(100*t.s),n=a(100*t.l);return 1==this._a?"hsl("+e+", "+r+"%, "+n+"%)":"hsla("+e+", "+r+"%, "+n+"%, "+this._roundA+")"},toHex:function(t){return e(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,e,r,n,i){var o=[M(a(t).toString(16)),M(a(e).toString(16)),M(a(r).toString(16)),M(C(n))];if(i&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1))return o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0);return o.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:a(this._r),g:a(this._g),b:a(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+a(this._r)+", "+a(this._g)+", "+a(this._b)+")":"rgba("+a(this._r)+", "+a(this._g)+", "+a(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:a(100*T(this._r,255))+"%",g:a(100*T(this._g,255))+"%",b:a(100*T(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+a(100*T(this._r,255))+"%, "+a(100*T(this._g,255))+"%, "+a(100*T(this._b,255))+"%)":"rgba("+a(100*T(this._r,255))+"%, "+a(100*T(this._g,255))+"%, "+a(100*T(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(I[e(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+s(this._r,this._g,this._b,this._a),r=e,n=this._gradientType?"GradientType = 1, ":"";if(t){var i=l(t);r="#"+s(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+e+",endColorstr="+r+")"},toString:function(t){var e=!!t;t=t||this._format;var r=!1,n=this._a<1&&0<=this._a;return e||!n||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(r=this.toRgbString()),"prgb"===t&&(r=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(r=this.toHexString()),"hex3"===t&&(r=this.toHexString(!0)),"hex4"===t&&(r=this.toHex8String(!0)),"hex8"===t&&(r=this.toHex8String()),"name"===t&&(r=this.toName()),"hsl"===t&&(r=this.toHslString()),"hsv"===t&&(r=this.toHsvString()),r||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return l(this.toString())},_applyModification:function(t,e){var r=t.apply(null,[this].concat([].slice.call(e)));return this._r=r._r,this._g=r._g,this._b=r._b,this.setAlpha(r._a),this},lighten:function(){return this._applyModification(b,arguments)},brighten:function(){return this._applyModification(g,arguments)},darken:function(){return this._applyModification(_,arguments)},desaturate:function(){return this._applyModification(r,arguments)},saturate:function(){return this._applyModification(u,arguments)},greyscale:function(){return this._applyModification(f,arguments)},spin:function(){return this._applyModification(y,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(j,arguments)},complement:function(){return this._applyCombination(v,arguments)},monochromatic:function(){return this._applyCombination(k,arguments)},splitcomplement:function(){return this._applyCombination(x,arguments)},triad:function(){return this._applyCombination(w,arguments)},tetrad:function(){return this._applyCombination(E,arguments)}},l.fromRatio=function(t,e){if("object"==typeof t){var r={};for(var n in t)t.hasOwnProperty(n)&&(r[n]="a"===n?t[n]:L(t[n]));t=r}return l(t,e)},l.equals=function(t,e){return!(!t||!e)&&l(t).toRgbString()==l(e).toRgbString()},l.random=function(){return l.fromRatio({r:t(),g:t(),b:t()})},l.mix=function(t,e,r){r=0===r?0:r||50;var n=l(t).toRgb(),i=l(e).toRgb(),o=r/100;return l({r:(i.r-n.r)*o+n.r,g:(i.g-n.g)*o+n.g,b:(i.b-n.b)*o+n.b,a:(i.a-n.a)*o+n.a})},l.readability=function(t,e){var r=l(t),n=l(e);return(h.max(r.getLuminance(),n.getLuminance())+.05)/(h.min(r.getLuminance(),n.getLuminance())+.05)},l.isReadable=function(t,e,r){var n,i,o=l.readability(t,e);switch(i=!1,(n=function(t){var e,r;e=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),r=(t.size||"small").toLowerCase(),"AA"!==e&&"AAA"!==e&&(e="AA");"small"!==r&&"large"!==r&&(r="small");return{level:e,size:r}}(r)).level+n.size){case"AAsmall":case"AAAlarge":i=4.5<=o;break;case"AAlarge":i=3<=o;break;case"AAAsmall":i=7<=o}return i},l.mostReadable=function(t,e,r){var n,i,o,a,s=null,u=0;i=(r=r||{}).includeFallbackColors,o=r.level,a=r.size;for(var f=0;f<e.length;f++)u<(n=l.readability(t,e[f]))&&(u=n,s=l(e[f]));return l.isReadable(t,s,{level:o,size:a})||!i?s:(r.includeFallbackColors=!1,l.mostReadable(t,["#fff","#000"],r))};var S=l.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},I=l.hexNames=function(t){var e={};for(var r in t)t.hasOwnProperty(r)&&(e[t[r]]=r);return e}(S);function A(t){return t=parseFloat(t),(isNaN(t)||t<0||1<t)&&(t=1),t}function T(t,e){var r;"string"==typeof(r=t)&&-1!=r.indexOf(".")&&1===parseFloat(r)&&(t="100%");var n,i="string"==typeof(n=t)&&-1!=n.indexOf("%");return t=p(e,m(0,parseFloat(t))),i&&(t=parseInt(t*e,10)/100),h.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function O(t){return p(1,m(0,t))}function P(t){return parseInt(t,16)}function M(t){return 1==t.length?"0"+t:""+t}function L(t){return t<=1&&(t=100*t+"%"),t}function C(t){return h.round(255*parseFloat(t)).toString(16)}function R(t){return P(t)/255}var B,N,D,U=(N="[\\s|\\(]+("+(B="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+B+")[,|\\s]+("+B+")\\s*\\)?",D="[\\s|\\(]+("+B+")[,|\\s]+("+B+")[,|\\s]+("+B+")[,|\\s]+("+B+")\\s*\\)?",{CSS_UNIT:new RegExp(B),rgb:new RegExp("rgb"+N),rgba:new RegExp("rgba"+D),hsl:new RegExp("hsl"+N),hsla:new RegExp("hsla"+D),hsv:new RegExp("hsv"+N),hsva:new RegExp("hsva"+D),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function F(t){return!!U.CSS_UNIT.exec(t)}void 0!==z&&z.exports?z.exports=l:window.tinycolor=l}(Math)},{}],251:[function(t,e,r){var i=t("buffer").Buffer;e.exports=function(t){if(t instanceof Uint8Array){if(0===t.byteOffset&&t.byteLength===t.buffer.byteLength)return t.buffer;if("function"==typeof t.buffer.slice)return t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)}if(i.isBuffer(t)){for(var e=new Uint8Array(t.length),r=t.length,n=0;n<r;n++)e[n]=t[n];return e.buffer}throw new Error("Argument must be a Buffer")}},{buffer:16}],252:[function(t,e,r){(r=e.exports=function(t){return t.replace(/^\s*|\s*$/g,"")}).left=function(t){return t.replace(/^\s*/,"")},r.right=function(t){return t.replace(/\s*$/,"")}},{}],253:[function(t,e,r){"use strict";var L=t("punycode"),C=t("./util");function I(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}r.parse=o,r.resolve=function(t,e){return o(t,!1,!0).resolve(e)},r.resolveObject=function(t,e){return t?o(t,!1,!0).resolveObject(e):e},r.format=function(t){C.isString(t)&&(t=o(t));return t instanceof I?t.format():I.prototype.format.call(t)},r.Url=I;var R=/^([a-z0-9.+-]+:)/i,n=/:[0-9]*$/,B=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,i=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),N=["'"].concat(i),D=["%","/","?",";","#"].concat(N),U=["/","?","#"],F=/^[+a-z0-9A-Z_-]{0,63}$/,z=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,H={javascript:!0,"javascript:":!0},G={javascript:!0,"javascript:":!0},W={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},Z=t("querystring");function o(t,e,r){if(t&&C.isObject(t)&&t instanceof I)return t;var n=new I;return n.parse(t,e,r),n}I.prototype.parse=function(t,e,r){if(!C.isString(t))throw new TypeError("Parameter 'url' must be a string, not "+typeof t);var n=t.indexOf("?"),i=-1!==n&&n<t.indexOf("#")?"?":"#",o=t.split(i);o[0]=o[0].replace(/\\/g,"/");var a=t=o.join(i);if(a=a.trim(),!r&&1===t.split("#").length){var s=B.exec(a);if(s)return this.path=a,this.href=a,this.pathname=s[1],s[2]?(this.search=s[2],this.query=e?Z.parse(this.search.substr(1)):this.search.substr(1)):e&&(this.search="",this.query={}),this}var u=R.exec(a);if(u){var f=(u=u[0]).toLowerCase();this.protocol=f,a=a.substr(u.length)}if(r||u||a.match(/^\/\/[^@\/]+@[^@\/]+/)){var l="//"===a.substr(0,2);!l||u&&G[u]||(a=a.substr(2),this.slashes=!0)}if(!G[u]&&(l||u&&!W[u])){for(var h,c,d=-1,p=0;p<U.length;p++){-1!==(m=a.indexOf(U[p]))&&(-1===d||m<d)&&(d=m)}-1!==(c=-1===d?a.lastIndexOf("@"):a.lastIndexOf("@",d))&&(h=a.slice(0,c),a=a.slice(c+1),this.auth=decodeURIComponent(h)),d=-1;for(p=0;p<D.length;p++){var m;-1!==(m=a.indexOf(D[p]))&&(-1===d||m<d)&&(d=m)}-1===d&&(d=a.length),this.host=a.slice(0,d),a=a.slice(d),this.parseHost(),this.hostname=this.hostname||"";var b="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!b)for(var g=this.hostname.split(/\./),_=(p=0,g.length);p<_;p++){var y=g[p];if(y&&!y.match(F)){for(var v="",w=0,E=y.length;w<E;w++)127<y.charCodeAt(w)?v+="x":v+=y[w];if(!v.match(F)){var x=g.slice(0,p),j=g.slice(p+1),k=y.match(z);k&&(x.push(k[1]),j.unshift(k[2])),j.length&&(a="/"+j.join(".")+a),this.hostname=x.join(".");break}}}255<this.hostname.length?this.hostname="":this.hostname=this.hostname.toLowerCase(),b||(this.hostname=L.toASCII(this.hostname));var S=this.port?":"+this.port:"",I=this.hostname||"";this.host=I+S,this.href+=this.host,b&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==a[0]&&(a="/"+a))}if(!H[f])for(p=0,_=N.length;p<_;p++){var A=N[p];if(-1!==a.indexOf(A)){var T=encodeURIComponent(A);T===A&&(T=escape(A)),a=a.split(A).join(T)}}var O=a.indexOf("#");-1!==O&&(this.hash=a.substr(O),a=a.slice(0,O));var P=a.indexOf("?");if(-1!==P?(this.search=a.substr(P),this.query=a.substr(P+1),e&&(this.query=Z.parse(this.query)),a=a.slice(0,P)):e&&(this.search="",this.query={}),a&&(this.pathname=a),W[f]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){S=this.pathname||"";var M=this.search||"";this.path=S+M}return this.href=this.format(),this},I.prototype.format=function(){var t=this.auth||"";t&&(t=(t=encodeURIComponent(t)).replace(/%3A/i,":"),t+="@");var e=this.protocol||"",r=this.pathname||"",n=this.hash||"",i=!1,o="";this.host?i=t+this.host:this.hostname&&(i=t+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&C.isObject(this.query)&&Object.keys(this.query).length&&(o=Z.stringify(this.query));var a=this.search||o&&"?"+o||"";return e&&":"!==e.substr(-1)&&(e+=":"),this.slashes||(!e||W[e])&&!1!==i?(i="//"+(i||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):i||(i=""),n&&"#"!==n.charAt(0)&&(n="#"+n),a&&"?"!==a.charAt(0)&&(a="?"+a),e+i+(r=r.replace(/[?#]/g,function(t){return encodeURIComponent(t)}))+(a=a.replace("#","%23"))+n},I.prototype.resolve=function(t){return this.resolveObject(o(t,!1,!0)).format()},I.prototype.resolveObject=function(t){if(C.isString(t)){var e=new I;e.parse(t,!1,!0),t=e}for(var r=new I,n=Object.keys(this),i=0;i<n.length;i++){var o=n[i];r[o]=this[o]}if(r.hash=t.hash,""===t.href)return r.href=r.format(),r;if(t.slashes&&!t.protocol){for(var a=Object.keys(t),s=0;s<a.length;s++){var u=a[s];"protocol"!==u&&(r[u]=t[u])}return W[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(t.protocol&&t.protocol!==r.protocol){if(!W[t.protocol]){for(var f=Object.keys(t),l=0;l<f.length;l++){var h=f[l];r[h]=t[h]}return r.href=r.format(),r}if(r.protocol=t.protocol,t.host||G[t.protocol])r.pathname=t.pathname;else{for(var c=(t.pathname||"").split("/");c.length&&!(t.host=c.shift()););t.host||(t.host=""),t.hostname||(t.hostname=""),""!==c[0]&&c.unshift(""),c.length<2&&c.unshift(""),r.pathname=c.join("/")}if(r.search=t.search,r.query=t.query,r.host=t.host||"",r.auth=t.auth,r.hostname=t.hostname||t.host,r.port=t.port,r.pathname||r.search){var d=r.pathname||"",p=r.search||"";r.path=d+p}return r.slashes=r.slashes||t.slashes,r.href=r.format(),r}var m=r.pathname&&"/"===r.pathname.charAt(0),b=t.host||t.pathname&&"/"===t.pathname.charAt(0),g=b||m||r.host&&t.pathname,_=g,y=r.pathname&&r.pathname.split("/")||[],v=(c=t.pathname&&t.pathname.split("/")||[],r.protocol&&!W[r.protocol]);if(v&&(r.hostname="",r.port=null,r.host&&(""===y[0]?y[0]=r.host:y.unshift(r.host)),r.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(""===c[0]?c[0]=t.host:c.unshift(t.host)),t.host=null),g=g&&(""===c[0]||""===y[0])),b)r.host=t.host||""===t.host?t.host:r.host,r.hostname=t.hostname||""===t.hostname?t.hostname:r.hostname,r.search=t.search,r.query=t.query,y=c;else if(c.length)y||(y=[]),y.pop(),y=y.concat(c),r.search=t.search,r.query=t.query;else if(!C.isNullOrUndefined(t.search)){if(v)r.hostname=r.host=y.shift(),(k=!!(r.host&&0<r.host.indexOf("@"))&&r.host.split("@"))&&(r.auth=k.shift(),r.host=r.hostname=k.shift());return r.search=t.search,r.query=t.query,C.isNull(r.pathname)&&C.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!y.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var w=y.slice(-1)[0],E=(r.host||t.host||1<y.length)&&("."===w||".."===w)||""===w,x=0,j=y.length;0<=j;j--)"."===(w=y[j])?y.splice(j,1):".."===w?(y.splice(j,1),x++):x&&(y.splice(j,1),x--);if(!g&&!_)for(;x--;x)y.unshift("..");!g||""===y[0]||y[0]&&"/"===y[0].charAt(0)||y.unshift(""),E&&"/"!==y.join("/").substr(-1)&&y.push("");var k,S=""===y[0]||y[0]&&"/"===y[0].charAt(0);v&&(r.hostname=r.host=S?"":y.length?y.shift():"",(k=!!(r.host&&0<r.host.indexOf("@"))&&r.host.split("@"))&&(r.auth=k.shift(),r.host=r.hostname=k.shift()));return(g=g||r.host&&y.length)&&!S&&y.unshift(""),y.length?r.pathname=y.join("/"):(r.pathname=null,r.path=null),C.isNull(r.pathname)&&C.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=t.auth||r.auth,r.slashes=r.slashes||t.slashes,r.href=r.format(),r},I.prototype.parseHost=function(){var t=this.host,e=n.exec(t);e&&(":"!==(e=e[0])&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)}},{"./util":254,punycode:224,querystring:227}],254:[function(t,e,r){"use strict";e.exports={isString:function(t){return"string"==typeof t},isObject:function(t){return"object"==typeof t&&null!==t},isNull:function(t){return null===t},isNullOrUndefined:function(t){return null==t}}},{}],255:[function(n,i,t){(function(r){!function(){var t,S,g,R,B,N,e={};function M(){void 0!==r&&"development"!=r.env.NODE_ENV||console.log.apply(console,arguments)}"object"==typeof i?i.exports=e:self.UTIF=e,t="function"==typeof n?n("pako"):self.pako,S=e,g=t,R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},B=function(){function t(t){this.message="JPEG error: "+t}return(t.prototype=Error()).name="JpegError",t.constructor=t}(),N=function(){function t(t,e){this.message=t,this.g=e}return(t.prototype=Error()).name="DNLMarkerError",t.constructor=t}(),function(){function t(){this.M=null,this.B=-1}function x(t,e){for(var r,n,i=0,o=[],a=16;0<a&&!t[a-1];)a--;o.push({children:[],index:0});var s,u=o[0];for(r=0;r<a;r++){for(n=0;n<t[r];n++){for((u=o.pop()).children[u.index]=e[i];0<u.index;)u=o.pop();for(u.index++,o.push(u);o.length<=r;)o.push(s={children:[],index:0}),u.children[u.index]=s.children,u=s;i++}r+1<a&&(o.push(s={children:[],index:0}),u.children[u.index]=s.children,u=s)}return o[0].children}function j(r,n,i,t,e,a,s,o,u){function f(){if(0<j)return x>>--j&1;if(255===(x=r[n++])){var t=r[n++];if(t){if(220===t&&v){n+=2;var e=r[n++]<<8|r[n++];if(0<e&&e!==i.g)throw new N("Found DNL marker (0xFFDC) while parsing scan data",e)}throw new B("unexpected marker "+(x<<8|t).toString(16))}}return x>>>(j=7)}function l(t){for(;;){if("number"==typeof(t=t[f()]))return t;if("object"!==(void 0===t?"undefined":R(t)))throw new B("invalid huffman sequence")}}function h(t){for(var e=0;0<t;)e=e<<1|f(),t--;return e}function c(t){if(1===t)return 1===f()?1:-1;var e=h(t);return 1<<t-1<=e?e:e+(-1<<t)+1}for(var d,p,m,b,g,_,y,v=9<arguments.length&&void 0!==arguments[9]&&arguments[9],w=i.P,E=n,x=0,j=0,k=0,S=0,I=t.length,A=i.S?0===a?0===o?function(t,e){var r=l(t.D);r=0===r?0:c(r)<<u,t.a[e]=t.m+=r}:function(t,e){t.a[e]|=f()<<u}:0===o?function(t,e){if(0<k)k--;else for(var r=a;r<=s;){var n=l(t.o),i=15&n;if(n>>=4,0===i){if(n<15){k=h(n)+(1<<n)-1;break}r+=16}else r+=n,t.a[e+C[r]]=c(i)*(1<<u),r++}}:function(t,e){for(var r,n=a,i=0;n<=s;){r=e+C[n];var o=t.a[r]<0?-1:1;switch(S){case 0:if(r=15&(i=l(t.o)),i>>=4,0===r)S=i<15?(k=h(i)+(1<<i),4):(i=16,1);else{if(1!==r)throw new B("invalid ACn encoding");d=c(r),S=i?2:3}continue;case 1:case 2:t.a[r]?t.a[r]+=o*(f()<<u):0==--i&&(S=2===S?3:0);break;case 3:t.a[r]?t.a[r]+=o*(f()<<u):(t.a[r]=d<<u,S=0);break;case 4:t.a[r]&&(t.a[r]+=o*(f()<<u))}n++}4===S&&0==--k&&(S=0)}:function(t,e){var r=l(t.D);for(r=0===r?0:c(r),t.a[e]=t.m+=r,r=1;r<64;){var n=l(t.o),i=15&n;if(n>>=4,0===i){if(n<15)break;r+=16}else r+=n,t.a[e+C[r]]=c(i),r++}},T=0,O=1===I?t[0].c*t[0].l:w*i.O;T<O;){var P=e?Math.min(O-T,e):O;for(p=0;p<I;p++)t[p].m=0;if(k=0,1===I){var M=t[0];for(g=0;g<P;g++)A(M,64*((M.c+1)*(T/M.c|0)+T%M.c)),T++}else for(g=0;g<P;g++){for(p=0;p<I;p++)for(_=(M=t[p]).h,y=M.j,m=0;m<y;m++)for(b=0;b<_;b++)A(M,64*((M.c+1)*((T/w|0)*M.j+m)+(T%w*M.h+b)));T++}if(j=0,(M=L(r,n))&&M.f&&((0,_util.warn)("decodeScan - unexpected MCU data, current marker is: "+M.f),n=M.offset),!(M=M&&M.F)||M<=65280)throw new B("marker was not found");if(!(65488<=M&&M<=65495))break;n+=2}return(M=L(r,n))&&M.f&&((0,_util.warn)("decodeScan - unexpected Scan data, current marker is: "+M.f),n=M.offset),n-E}function k(t,e){for(var r=e.c,n=e.l,i=new Int16Array(64),o=0;o<n;o++)for(var a=0;a<r;a++){var s=64*((e.c+1)*o+a),u=i,f=e.G,l=e.a;if(!f)throw new B("missing required Quantization Table.");for(var h=0;h<64;h+=8){var c=l[s+h],d=l[s+h+1],p=l[s+h+2],m=l[s+h+3],b=l[s+h+4],g=l[s+h+5],_=l[s+h+6],y=l[s+h+7];if(c*=f[h],0==(d|p|m|b|g|_|y))c=5793*c+512>>10,u[h]=c,u[h+1]=c,u[h+2]=c,u[h+3]=c,u[h+4]=c,u[h+5]=c,u[h+6]=c,u[h+7]=c;else{d*=f[h+1],p*=f[h+2],m*=f[h+3],b*=f[h+4],g*=f[h+5];var v=5793*c+128>>8,w=5793*b+128>>8,E=p,x=_*=f[h+6];w=(v=v+w+1>>1)-w,c=3784*E+1567*x+128>>8,E=1567*E-3784*x+128>>8,g=(b=(b=2896*(d-(y*=f[h+7]))+128>>8)+(g<<=4)+1>>1)-g,m=(y=(y=2896*(d+y)+128>>8)+(m<<=4)+1>>1)-m,x=(v=v+(x=c)+1>>1)-x,E=(w=w+E+1>>1)-E,c=2276*b+3406*y+2048>>12,b=3406*b-2276*y+2048>>12,y=c,c=799*m+4017*g+2048>>12,m=4017*m-799*g+2048>>12,g=c,u[h]=v+y,u[h+7]=v-y,u[h+1]=w+g,u[h+6]=w-g,u[h+2]=E+m,u[h+5]=E-m,u[h+3]=x+b,u[h+4]=x-b}}for(f=0;f<8;++f)c=u[f],d=u[f+8],p=u[f+16],m=u[f+24],b=u[f+32],g=u[f+40],_=u[f+48],y=u[f+56],l[s+f+56]=0==(d|p|m|b|g|_|y)?(c=(c=5793*c+8192>>14)<-2040?0:2024<=c?255:c+2056>>4,l[s+f]=c,l[s+f+8]=c,l[s+f+16]=c,l[s+f+24]=c,l[s+f+32]=c,l[s+f+40]=c,l[s+f+48]=c):(v=5793*c+2048>>12,w=5793*b+2048>>12,c=3784*(E=p)+1567*(x=_)+2048>>12,E=1567*E-3784*x+2048>>12,x=c,g=(b=(b=2896*(d-y)+2048>>12)+g+1>>1)-g,m=(y=(y=2896*(d+y)+2048>>12)+m+1>>1)-m,c=2276*b+3406*y+2048>>12,b=3406*b-2276*y+2048>>12,y=c,c=799*m+4017*g+2048>>12,m=4017*m-799*g+2048>>12,d=(w=(w=(v=4112+(v+w+1>>1))-w)+E+1>>1)+(g=c),_=w-g,g=(E=w-E)-m,c=(c=(v=v+x+1>>1)+y)<16?0:4080<=c?255:c>>4,d=d<16?0:4080<=d?255:d>>4,p=(p=E+m)<16?0:4080<=p?255:p>>4,m=(m=(x=v-x)+b)<16?0:4080<=m?255:m>>4,b=(b=x-b)<16?0:4080<=b?255:b>>4,g=g<16?0:4080<=g?255:g>>4,_=_<16?0:4080<=_?255:_>>4,y=(y=v-y)<16?0:4080<=y?255:y>>4,l[s+f]=c,l[s+f+8]=d,l[s+f+16]=p,l[s+f+24]=m,l[s+f+32]=b,l[s+f+40]=g,l[s+f+48]=_,y)}return e.a}function L(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:e,n=t.length-1;if(r=r<e?r:e,n<=e)return null;var i=t[e]<<8|t[e+1];if(65472<=i&&i<=65534)return{f:null,F:i,offset:e};for(var o=t[r]<<8|t[r+1];!(65472<=o&&o<=65534);){if(++r>=n)return null;o=t[r]<<8|t[r+1]}return{f:i.toString(16),F:o,offset:r}}var C=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]);t.prototype={parse:function(e){function t(){var t=e[o]<<8|e[o+1];return o+=2,t}function r(t){for(var e=Math.ceil(t.v/8/t.s),r=Math.ceil(t.g/8/t.u),n=0;n<t.b.length;n++){w=t.b[n];var i=Math.ceil(Math.ceil(t.v/8)*w.h/t.s),o=Math.ceil(Math.ceil(t.g/8)*w.j/t.u);w.a=new Int16Array(64*r*w.j*(e*w.h+1)),w.c=i,w.l=o}t.P=e,t.O=r}var n=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).N,i=void 0===n?null:n,o=0,a=null,s=0;n=[];var u,f,l=[],h=[],c=t();if(65496!==c)throw new B("SOI not found");for(c=t();65497!==c;){switch(c){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var d=(u=void 0,u=t(),(f=L(e,u=o+u-2,o))&&f.f&&((0,_util.warn)("readDataBlock - incorrect length, current marker is: "+f.f),u=f.offset),u=e.subarray(o,u),o+=u.length,u);65518===c&&65===d[0]&&100===d[1]&&111===d[2]&&98===d[3]&&101===d[4]&&(a={version:d[5]<<8|d[6],Y:d[7]<<8|d[8],Z:d[9]<<8|d[10],W:d[11]});break;case 65499:c=t()+o-2;for(var p;o<c;){var m=e[o++],b=new Uint16Array(64);if(0==m>>4)for(d=0;d<64;d++)b[p=C[d]]=e[o++];else{if(1!=m>>4)throw new B("DQT - invalid table spec");for(d=0;d<64;d++)b[p=C[d]]=t()}n[15&m]=b}break;case 65472:case 65473:case 65474:if(g)throw new B("Only single frame JPEGs supported");t();var g={};for(g.X=65473===c,g.S=65474===c,g.precision=e[o++],c=t(),g.g=i||c,g.v=t(),g.b=[],g.C={},d=e[o++],c=b=m=0;c<d;c++){p=e[o];var _=e[o+1]>>4,y=15&e[o+1];m<_&&(m=_),b<y&&(b=y),_=g.b.push({h:_,j:y,T:e[o+2],G:null}),g.C[p]=_-1,o+=3}g.s=m,g.u=b,r(g);break;case 65476:for(p=t(),c=2;c<p;){for(m=e[o++],b=new Uint8Array(16),d=_=0;d<16;d++,o++)_+=b[d]=e[o];for(y=new Uint8Array(_),d=0;d<_;d++,o++)y[d]=e[o];c+=17+_,(0==m>>4?h:l)[15&m]=x(b,y)}break;case 65501:t();var v=t();break;case 65498:for(d=1==++s&&!i,t(),m=e[o++],p=[],c=0;c<m;c++){b=g.C[e[o++]];var w=g.b[b];b=e[o++],w.D=h[b>>4],w.o=l[15&b],p.push(w)}c=e[o++],m=e[o++],b=e[o++];try{var E=j(e,o,g,p,v,c,m,b>>4,15&b,d);o+=E}catch(t){if(t instanceof N)return(0,_util.warn)('Attempting to re-parse JPEG image using "scanLines" parameter found in DNL marker (0xFFDC) segment.'),this.parse(e,{N:t.g});throw t}break;case 65500:o+=4;break;case 65535:255!==e[o]&&o--;break;default:if(255===e[o-3]&&192<=e[o-2]&&e[o-2]<=254)o-=3;else{if(!(d=L(e,o-2))||!d.f)throw new B("unknown marker "+c.toString(16));(0,_util.warn)("JpegImage.parse - unexpected data, current marker is: "+d.f),o=d.offset}}c=t()}for(this.width=g.v,this.height=g.g,this.A=a,this.b=[],c=0;c<g.b.length;c++)(v=n[(w=g.b[c]).T])&&(w.G=v),this.b.push({R:k(0,w),U:w.h/g.s,V:w.j/g.u,c:w.c,l:w.l});this.i=this.b.length},L:function(t,e){var r,n,i=this.width/t,o=this.height/e,a=this.b.length,s=t*e*a,u=new Uint8ClampedArray(s),f=new Uint32Array(t);for(n=0;n<a;n++){var l=this.b[n],h=l.U*i,c=l.V*o,d=n,p=l.R,m=l.c+1<<3;for(r=0;r<t;r++)l=0|r*h,f[r]=(4294967288&l)<<3|7&l;for(h=0;h<e;h++)for(l=m*(4294967288&(l=0|h*c))|(7&l)<<3,r=0;r<t;r++)u[d]=p[l+f[r]],d+=a}if(o=this.M)for(n=0;n<s;)for(i=l=0;l<a;l++,n++,i+=2)u[n]=(u[n]*o[i]>>8)+o[i+1];return u},w:function(){return this.A?!!this.A.W:3===this.i?0!==this.B:1===this.B},I:function(t){for(var e,r,n,i=0,o=t.length;i<o;i+=3)e=t[i],r=t[i+1],n=t[i+2],t[i]=e-179.456+1.402*n,t[i+1]=e+135.459-.344*r-.714*n,t[i+2]=e-226.816+1.772*r;return t},K:function(t){for(var e,r,n,i,o=0,a=0,s=t.length;a<s;a+=4)e=t[a],r=t[a+1],n=t[a+2],i=t[a+3],t[o++]=r*(-660635669420364e-19*r+.000437130475926232*n-54080610064599e-18*e+.00048449797120281*i-.154362151871126)-122.67195406894+n*(-.000957964378445773*n+.000817076911346625*e-.00477271405408747*i+1.53380253221734)+e*(.000961250184130688*e-.00266257332283933*i+.48357088451265)+i*(-.000336197177618394*i+.484791561490776),t[o++]=107.268039397724+r*(219927104525741e-19*r-.000640992018297945*n+.000659397001245577*e+.000426105652938837*i-.176491792462875)+n*(-.000778269941513683*n+.00130872261408275*e+.000770482631801132*i-.151051492775562)+e*(.00126935368114843*e-.00265090189010898*i+.25802910206845)+i*(-.000318913117588328*i-.213742400323665),t[o++]=r*(-.000570115196973677*r-263409051004589e-19*n+.0020741088115012*e-.00288260236853442*i+.814272968359295)-20.810012546947+n*(-153496057440975e-19*n-.000132689043961446*e+.000560833691242812*i-.195152027534049)+e*(.00174418132927582*e-.00255243321439347*i+.116935020465145)+i*(-.000343531996510555*i+.24165260232407);return t.subarray(0,o)},J:function(t){for(var e,r,n,i=0,o=t.length;i<o;i+=4)e=t[i],r=t[i+1],n=t[i+2],t[i]=434.456-e-1.402*n,t[i+1]=119.541-e+.344*r+.714*n,t[i+2]=481.816-e-1.772*r;return t},H:function(t){for(var e,r,n,i,o=0,a=0,s=t.length;a<s;a+=4)e=t[a]*(1/255),r=t[a+1]*(1/255),n=t[a+2]*(1/255),i=t[a+3]*(1/255),t[o++]=255+e*(-4.387332384609988*e+54.48615194189176*r+18.82290502165302*n+212.25662451639585*i-285.2331026137004)+r*(1.7149763477362134*r-5.6096736904047315*n-17.873870861415444*i-5.497006427196366)+n*(-2.5217340131683033*n-21.248923337353073*i+17.5119270841813)-i*(21.86122147463605*i+189.48180835922747),t[o++]=255+e*(8.841041422036149*e+60.118027045597366*r+6.871425592049007*n+31.159100130055922*i-79.2970844816548)+r*(-15.310361306967817*r+17.575251261109482*n+131.35250912493976*i-190.9453302588951)+n*(4.444339102852739*n+9.8632861493405*i-24.86741582555878)-i*(20.737325471181034*i+187.80453709719578),t[o++]=255+e*(.8842522430003296*e+8.078677503112928*r+30.89978309703729*n-.23883238689178934*i-14.183576799673286)+r*(10.49593273432072*r+63.02378494754052*n+50.606957656360734*i-112.23884253719248)+n*(.03296041114873217*n+115.60384449646641*i-193.58209356861505)-i*(22.33816807309886*i+180.12613974708367);return t.subarray(0,o)},getData:function(t,e,r){if(4<this.i)throw new B("Unsupported color mode");if(t=this.L(t,e),1===this.i&&r){r=t.length,e=new Uint8ClampedArray(3*r);for(var n=0,i=0;i<r;i++){var o=t[i];e[n++]=o,e[n++]=o,e[n++]=o}return e}if(3===this.i&&this.w())return this.I(t);if(4===this.i){if(this.w())return r?this.K(t):this.J(t);if(r)return this.H(t)}return t}},S.JpegDecoder=t}(),S.encodeImage=function(t,e,r,n){var i={t256:[e],t257:[r],t258:[8,8,8,8],t259:[1],t262:[2],t273:[1e3],t277:[4],t278:[r],t279:[e*r*4],t282:[1],t283:[1],t284:[1],t286:[0],t287:[0],t296:[1],t305:["Photopea (UTIF.js)"],t338:[1]};if(n)for(var o in n)i[o]=n[o];var a=new Uint8Array(S.encode([i])),s=new Uint8Array(t),u=new Uint8Array(1e3+e*r*4);for(o=0;o<a.length;o++)u[o]=a[o];for(o=0;o<s.length;o++)u[1e3+o]=s[o];return u.buffer},S.encode=function(t){var e=new Uint8Array(2e4),r=4,n=S._binBE;e[0]=77,e[1]=77,e[3]=42;var i=8;n.writeUint(e,r,i),r+=4;for(var o=0;o<t.length;o++){var a=S._writeIFD(n,e,i,t[o]);i=a[1],o<t.length-1&&n.writeUint(e,a[0],i)}return e.slice(0,i).buffer},S.decode=function(t){S.decode._decodeG3.allow2D=null;var e=new Uint8Array(t),r=0,n=S._binBE.readASCII(e,r,2);r+=2;var i="II"==n?S._binLE:S._binBE;i.readUshort(e,r),r+=2;var o=i.readUint(e,r);r+=4;for(var a=[];;){var s=S._readIFD(i,e,o,a);if(0==(o=i.readUint(e,s)))break}return a},S.decodeImages=function(t,e){for(var r=new Uint8Array(t),n=S._binBE.readASCII(r,0,2),i=0;i<e.length;i++){var o=e[i];if(null!=o.t256){o.isLE="II"==n,o.width=o.t256[0],o.height=o.t257[0];var a=o.t259?o.t259[0]:1,s=o.t266?o.t266[0]:1;o.t284&&2==o.t284[0]&&M("PlanarConfiguration 2 should not be used!");var u=(o.t258?Math.min(32,o.t258[0]):1)*(o.t277?o.t277[0]:1),f=8*Math.ceil(o.width*u/8),l=o.t273;null==l&&(l=o.t324);var h=o.t279;1==a&&1==l.length&&(h=[o.height*(f>>>3)]),null==h&&(h=o.t325);var c=new Uint8Array(o.height*(f>>>3)),d=0;if(null!=o.t322){for(var p=o.t322[0],m=o.t323[0],b=Math.floor((o.width+p-1)/p),g=Math.floor((o.height+m-1)/m),_=new Uint8Array(0|Math.ceil(p*m*u/8)),y=0;y<g;y++)for(var v=0;v<b;v++){for(var w=y*b+v,E=0;E<_.length;E++)_[E]=0;S.decode._decompress(o,r,l[w],h[w],a,_,0,s),6==a?c=_:S._copyTile(_,0|Math.ceil(p*u/8),m,c,0|Math.ceil(o.width*u/8),o.height,0|Math.ceil(v*p*u/8),y*m)}d=8*c.length}else{var x=o.t278?o.t278[0]:o.height;for(x=Math.min(x,o.height),w=0;w<l.length;w++)S.decode._decompress(o,r,l[w],h[w],a,c,0|Math.ceil(d/8),s),d+=f*x;d=Math.min(d,8*c.length)}o.data=new Uint8Array(c.buffer,0,0|Math.ceil(d/8))}}},S.decode._decompress=function(t,e,r,n,i,o,a,s){if(1==i)for(var u=0;u<n;u++)o[a+u]=e[r+u];else if(3==i)S.decode._decodeG3(e,r,n,o,a,t.width,s);else if(4==i)S.decode._decodeG4(e,r,n,o,a,t.width,s);else if(5==i)S.decode._decodeLZW(e,r,o,a);else if(6==i)S.decode._decodeOldJPEG(t,e,r,n,o,a);else if(7==i)S.decode._decodeNewJPEG(t,e,r,n,o,a);else if(8==i)for(var f=new Uint8Array(e.buffer,r,n),l=g.inflate(f),h=0;h<l.length;h++)o[a+h]=l[h];else 32773==i?S.decode._decodePackBits(e,r,n,o,a):32809==i?S.decode._decodeThunder(e,r,n,o,a):M("Unknown compression",i);if(t.t317&&2==t.t317[0])for(var c=t.t277?t.t277[0]:1,d=t.t278?t.t278[0]:t.height,p=t.width*c,m=0;m<d;m++){var b=a+m*p;if(3==c)for(u=3;u<p;u+=3)o[b+u]=o[b+u]+o[b+u-3]&255,o[b+u+1]=o[b+u+1]+o[b+u-2]&255,o[b+u+2]=o[b+u+2]+o[b+u-1]&255;else for(u=c;u<p;u++)o[b+u]=o[b+u]+o[b+u-c]&255}},S.decode._decodeNikon=function(t,e,r,n,i){var o,a;M(t.slice(e,e+100)),o=t[e],a=t[++e],e++,M(o.toString(16),a.toString(16),r)},S.decode._decodeNewJPEG=function(t,e,r,n,i,o){var a=t.t347,s=a?a.length:0,u=new Uint8Array(s+n);if(a){for(var f=0,l=0;l<s-1&&(255!=a[l]||217!=a[l+1]);l++)u[f++]=a[l];var h=e[r],c=e[r+1];for(255==h&&216==c||(u[f++]=h,u[f++]=c),l=2;l<n;l++)u[f++]=e[r+l]}else for(l=0;l<n;l++)u[l]=e[r+l];if(32803==t.t262){var d=t.t258[0],p=(new LosslessJpegDecoder).decode(u),m=p.length;if(16==d)for(l=0;l<m;l++)i[o++]=255&p[l],i[o++]=p[l]>>>8;else{if(12!=d)throw new Error("unsupported bit depth "+d);for(l=0;l<m;l+=2)i[o++]=p[l]>>>4,i[o++]=255&(p[l]<<4|p[l+1]>>>8),i[o++]=255&p[l+1]}}else{var b=new S.JpegDecoder;b.parse(u);var g=b.getData(b.width,b.height);for(l=0;l<g.length;l++)i[o+l]=g[l]}6==t.t262[0]&&(t.t262[0]=2)},S.decode._decodeOldJPEGInit=function(t,e,r,n){var i,o,a,s,u,f=0,l=0,h=!1,c=t.t513,d=c?c[0]:0,p=t.t514,m=p?p[0]:0,b=t.t324||t.t273||c,g=t.t530,_=0,y=0,v=t.t277?t.t277[0]:1,w=t.t515;if(b&&(l=b[0],h=1<b.length),!h){if(255==e[r]&&216==e[r+1])return{jpegOffset:r};if(null!=c&&(255==e[r+d]&&216==e[r+d+1]?f=r+d:M("JPEGInterchangeFormat does not point to SOI"),null==p?M("JPEGInterchangeFormatLength field is missing"):(l<=d||d+m<=l)&&M("JPEGInterchangeFormatLength field value is invalid"),null!=f))return{jpegOffset:f}}if(null!=g&&(_=g[0],y=g[1]),null!=c&&null!=p)if(2<=m&&d+m<=l){for(i=255==e[r+d+m-2]&&216==e[r+d+m-1]?new Uint8Array(m-2):new Uint8Array(m),a=0;a<i.length;a++)i[a]=e[r+d+a];M("Incorrect JPEG interchange format: using JPEGInterchangeFormat offset to derive tables")}else M("JPEGInterchangeFormat+JPEGInterchangeFormatLength > offset to first strip or tile");if(null==i){var E=0,x=[];x[E++]=255,x[E++]=216;var j=t.t519;if(null==j)throw new Error("JPEGQTables tag is missing");for(a=0;a<j.length;a++)for(x[E++]=255,x[E++]=219,x[E++]=0,x[E++]=67,x[E++]=a,s=0;s<64;s++)x[E++]=e[r+j[a]+s];for(u=0;u<2;u++){var k=t[0==u?"t520":"t521"];if(null==k)throw new Error((0==u?"JPEGDCTables":"JPEGACTables")+" tag is missing");for(a=0;a<k.length;a++){x[E++]=255,x[E++]=196;var S=19;for(s=0;s<16;s++)S+=e[r+k[a]+s];for(x[E++]=S>>>8,x[E++]=255&S,x[E++]=a|u<<4,s=0;s<16;s++)x[E++]=e[r+k[a]+s];for(s=0;s<S;s++)x[E++]=e[r+k[a]+16+s]}}if(x[E++]=255,x[E++]=192,x[E++]=0,x[E++]=8+3*v,x[E++]=8,x[E++]=t.height>>>8&255,x[E++]=255&t.height,x[E++]=t.width>>>8&255,x[E++]=255&t.width,1==(x[E++]=v))x[E++]=1,x[E++]=17,x[E++]=0;else for(a=0;a<3;a++)x[E++]=a+1,x[E++]=0!=a?17:(15&_)<<4|15&y,x[E++]=a;null!=w&&0!=w[0]&&(x[E++]=255,x[E++]=221,x[E++]=0,x[E++]=4,x[E++]=w[0]>>>8&255,x[E++]=255&w[0]),i=new Uint8Array(x)}var I=-1;for(a=0;a<i.length-1;){if(255==i[a]&&192==i[a+1]){I=a;break}a++}if(-1==I){var A=new Uint8Array(i.length+10+3*v);A.set(i);var T=i.length;if(I=i.length,(i=A)[T++]=255,i[T++]=192,i[T++]=0,i[T++]=8+3*v,i[T++]=8,i[T++]=t.height>>>8&255,i[T++]=255&t.height,i[T++]=t.width>>>8&255,i[T++]=255&t.width,1==(i[T++]=v))i[T++]=1,i[T++]=17,i[T++]=0;else for(a=0;a<3;a++)i[T++]=a+1,i[T++]=0!=a?17:(15&_)<<4|15&y,i[T++]=a}if(255==e[l]&&218==e[l+1]){var O=e[l+2]<<8|e[l+3];for((o=new Uint8Array(O+2))[0]=e[l],o[1]=e[l+1],o[2]=e[l+2],o[3]=e[l+3],a=0;a<O-2;a++)o[a+4]=e[l+a+4]}else{var P=0;if((o=new Uint8Array(8+2*v))[P++]=255,o[P++]=218,o[P++]=0,o[P++]=6+2*v,1==(o[P++]=v))o[P++]=1,o[P++]=0;else for(a=0;a<3;a++)o[P++]=a+1,o[P++]=a<<4|a;o[P++]=0,o[P++]=63,o[P++]=0}return{jpegOffset:r,tables:i,sosMarker:o,sofPosition:I}},S.decode._decodeOldJPEG=function(t,e,r,n,i,o){var a,s,u,f=S.decode._decodeOldJPEGInit(t,e,r,n);if(null!=f.jpegOffset)for(a=r+n-f.jpegOffset,u=new Uint8Array(a),c=0;c<a;c++)u[c]=e[f.jpegOffset+c];else{for(s=f.tables.length,(u=new Uint8Array(s+f.sosMarker.length+n+2)).set(f.tables),u[f.sofPosition+5]=t.height>>>8&255,u[f.sofPosition+6]=255&t.height,u[f.sofPosition+7]=t.width>>>8&255,u[f.sofPosition+8]=255&t.width,255==e[r]&&e[r+1]==SOS||(u.set(f.sosMarker,bufoff),bufoff+=sosMarker.length),c=0;c<n;c++)u[bufoff++]=e[r+c];u[bufoff++]=255,u[bufoff++]=EOI}var l=new S.JpegDecoder;l.parse(u);for(var h=l.getData(l.width,l.height),c=0;c<h.length;c++)i[o+c]=h[c];6==t.t262[0]&&(t.t262[0]=2)},S.decode._decodePackBits=function(t,e,r,n,i){for(var o=new Int8Array(t.buffer),a=new Int8Array(n.buffer),s=e+r;e<s;){var u=o[e];if(e++,0<=u&&u<128)for(var f=0;f<u+1;f++)a[i]=o[e],i++,e++;if(-127<=u&&u<0){for(f=0;f<1-u;f++)a[i]=o[e],i++;e++}}},S.decode._decodeThunder=function(t,e,r,n,i){for(var o=[0,1,0,-1],a=[0,1,2,3,0,-3,-2,-1],s=e+r,u=2*i,f=0;e<s;){var l=t[e],h=l>>>6,c=63&l;if(e++,3==h&&(f=15&c,n[u>>>1]|=f<<4*(1-u&1),u++),0==h)for(var d=0;d<c;d++)n[u>>>1]|=f<<4*(1-u&1),u++;if(2==h)for(d=0;d<2;d++)4!=(p=c>>>3*(1-d)&7)&&(f+=a[p],n[u>>>1]|=f<<4*(1-u&1),u++);if(1==h)for(d=0;d<3;d++){var p;2!=(p=c>>>2*(2-d)&3)&&(f+=o[p],n[u>>>1]|=f<<4*(1-u&1),u++)}}},S.decode._dmap={1:0,"011":1,"000011":2,"0000011":3,"010":-1,"000010":-2,"0000010":-3},S.decode._lens=function(){var t=function(t,e,r,n){for(var i=0;i<e.length;i++)t[e[i]]=r+i*n},e="00110101,000111,0111,1000,1011,1100,1110,1111,10011,10100,00111,01000,001000,000011,110100,110101,101010,101011,0100111,0001100,0001000,0010111,0000011,0000100,0101000,0101011,0010011,0100100,0011000,00000010,00000011,00011010,00011011,00010010,00010011,00010100,00010101,00010110,00010111,00101000,00101001,00101010,00101011,00101100,00101101,00000100,00000101,00001010,00001011,01010010,01010011,01010100,01010101,00100100,00100101,01011000,01011001,01011010,01011011,01001010,01001011,00110010,00110011,00110100",r="0000110111,010,11,10,011,0011,0010,00011,000101,000100,0000100,0000101,0000111,00000100,00000111,000011000,0000010111,0000011000,0000001000,00001100111,00001101000,00001101100,00000110111,00000101000,00000010111,00000011000,000011001010,000011001011,000011001100,000011001101,000001101000,000001101001,000001101010,000001101011,000011010010,000011010011,000011010100,000011010101,000011010110,000011010111,000001101100,000001101101,000011011010,000011011011,000001010100,000001010101,000001010110,000001010111,000001100100,000001100101,000001010010,000001010011,000000100100,000000110111,000000111000,000000100111,000000101000,000001011000,000001011001,000000101011,000000101100,000001011010,000001100110,000001100111",n="11011,10010,010111,0110111,00110110,00110111,01100100,01100101,01101000,01100111,011001100,011001101,011010010,011010011,011010100,011010101,011010110,011010111,011011000,011011001,011011010,011011011,010011000,010011001,010011010,011000,010011011",i="0000001111,000011001000,000011001001,000001011011,000000110011,000000110100,000000110101,0000001101100,0000001101101,0000001001010,0000001001011,0000001001100,0000001001101,0000001110010,0000001110011,0000001110100,0000001110101,0000001110110,0000001110111,0000001010010,0000001010011,0000001010100,0000001010101,0000001011010,0000001011011,0000001100100,0000001100101",o="00000001000,00000001100,00000001101,000000010010,000000010011,000000010100,000000010101,000000010110,000000010111,000000011100,000000011101,000000011110,000000011111";e=e.split(","),r=r.split(","),n=n.split(","),i=i.split(","),o=o.split(",");var a={},s={};return t(a,e,0,1),t(a,n,64,64),t(a,o,1792,64),t(s,r,0,1),t(s,i,64,64),t(s,o,1792,64),[a,s]}(),S.decode._decodeG4=function(t,e,r,n,i,o,a){for(var s=S.decode,u=e<<3,f=0,l="",h=[],c=[],d=0;d<o;d++)c.push(0);c=s._makeDiff(c);for(var p=0,m=0,b=0,g=0,_=0,y=0,v="",w=0,E=8*Math.ceil(o/8);u>>>3<e+r;){b=s._findDiff(c,p+(0==p?0:1),1-_),g=s._findDiff(c,b,_);var x=0;if(1==a&&(x=t[u>>>3]>>>7-(7&u)&1),2==a&&(x=t[u>>>3]>>>(7&u)&1),u++,l+=x,"H"==v){if(null!=s._lens[_][l]){var j=s._lens[_][l];l="",f+=j,j<64&&(s._addNtimes(h,f,_),p+=f,_=1-_,(f=0)==--w&&(v=""))}}else"0001"==l&&(l="",s._addNtimes(h,g-p,_),p=g),"001"==l&&(l="",v="H",w=2),null!=s._dmap[l]&&(m=b+s._dmap[l],s._addNtimes(h,m-p,_),p=m,l="",_=1-_);h.length==o&&""==v&&(s._writeBits(h,n,8*i+y*E),y++,p=_=0,c=s._makeDiff(h),h=[])}},S.decode._findDiff=function(t,e,r){for(var n=0;n<t.length;n+=2)if(t[n]>=e&&t[n+1]==r)return t[n]},S.decode._makeDiff=function(t){var e=[];1==t[0]&&e.push(0,1);for(var r=1;r<t.length;r++)t[r-1]!=t[r]&&e.push(r,t[r]);return e.push(t.length,0,t.length,1),e},S.decode._decodeG3=function(t,e,r,n,i,o,a){for(var s=S.decode,u=e<<3,f=0,l="",h=[],c=[],d=0;d<o;d++)h.push(0);for(var p=0,m=0,b=0,g=0,_=0,y=-1,v="",w=0,E=!1,x=8*Math.ceil(o/8);u>>>3<e+r;){b=s._findDiff(c,p+(0==p?0:1),1-_),g=s._findDiff(c,b,_);var j=0;if(1==a&&(j=t[u>>>3]>>>7-(7&u)&1),2==a&&(j=t[u>>>3]>>>(7&u)&1),u++,l+=j,E){if(null!=s._lens[_][l]){var k=s._lens[_][l];l="",f+=k,k<64&&(s._addNtimes(h,f,_),_=1-_,f=0)}}else"H"==v?null!=s._lens[_][l]&&(k=s._lens[_][l],l="",f+=k,k<64&&(s._addNtimes(h,f,_),p+=f,_=1-_,(f=0)==--w&&(v=""))):("0001"==l&&(l="",s._addNtimes(h,g-p,_),p=g),"001"==l&&(l="",v="H",w=2),null!=s._dmap[l]&&(m=b+s._dmap[l],s._addNtimes(h,m-p,_),p=m,l="",_=1-_));l.endsWith("000000000001")&&(0<=y&&s._writeBits(h,n,8*i+y*x),1==a&&(E=1==(t[u>>>3]>>>7-(7&u)&1)),2==a&&(E=1==(t[u>>>3]>>>(7&u)&1)),u++,null==s._decodeG3.allow2D&&(s._decodeG3.allow2D=E),s._decodeG3.allow2D||(E=!0,u--),l="",y++,p=_=0,c=s._makeDiff(h),h=[])}h.length==o&&s._writeBits(h,n,8*i+y*x)},S.decode._addNtimes=function(t,e,r){for(var n=0;n<e;n++)t.push(r)},S.decode._writeBits=function(t,e,r){for(var n=0;n<t.length;n++)e[r+n>>>3]|=t[n]<<7-(r+n&7)},S.decode._decodeLZW=function(t,e,r,n){if(null==S.decode._lzwTab){for(var i=new Uint32Array(65535),o=new Uint16Array(65535),a=new Uint8Array(2e6),s=0;s<256;s++)i[a[s<<2]=s]=s<<2,o[s]=1;S.decode._lzwTab=[i,o,a]}for(var u=S.decode._copyData,f=S.decode._lzwTab[0],l=S.decode._lzwTab[1],h=(a=S.decode._lzwTab[2],258),c=1032,d=9,p=e<<3,m=0,b=0;m=(t[p>>>3]<<16|t[p+8>>>3]<<8|t[p+16>>>3])>>24-(7&p)-d&(1<<d)-1,p+=d,257!=m;){if(256==m){if(d=9,h=258,c=1032,m=(t[p>>>3]<<16|t[p+8>>>3]<<8|t[p+16>>>3])>>24-(7&p)-d&(1<<d)-1,p+=d,257==m)break;r[n]=m,n++}else if(m<h){var g=f[m],_=l[m];u(a,g,r,n,_),n+=_,h<=b?(f[h]=c,a[f[h]]=g[0],c=c+(l[h]=1)+3&-4,h++):(f[h]=c,u(a,f[b],a,c,y=l[b]),a[c+y]=a[g],y++,l[h]=y,h++,c=c+y+3&-4),h+1==1<<d&&d++}else{var y;h<=b?(f[h]=c,l[h]=0,h++):(f[h]=c,u(a,f[b],a,c,y=l[b]),a[c+y]=a[c],y++,l[h]=y,h++,u(a,c,r,n,y),n+=y,c=c+y+3&-4),h+1==1<<d&&d++}b=m}},S.decode._copyData=function(t,e,r,n,i){for(var o=0;o<i;o+=4)r[n+o]=t[e+o],r[n+o+1]=t[e+o+1],r[n+o+2]=t[e+o+2],r[n+o+3]=t[e+o+3]},S.tags={254:"NewSubfileType",255:"SubfileType",256:"ImageWidth",257:"ImageLength",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",266:"FillOrder",269:"DocumentName",270:"ImageDescription",271:"Make",272:"Model",273:"StripOffset",274:"Orientation",277:"SamplesPerPixel",278:"RowsPerStrip",279:"StripByteCounts",280:"MinSampleValue",281:"MaxSampleValue",282:"XResolution",283:"YResolution",284:"PlanarConfiguration",285:"PageName",286:"XPosition",287:"YPosition",292:"T4Options",296:"ResolutionUnit",297:"PageNumber",305:"Software",306:"DateTime",315:"Artist",316:"HostComputer",317:"Predictor",318:"WhitePoint",319:"PrimaryChromaticities",320:"ColorMap",321:"HalftoneHints",322:"TileWidth",323:"TileLength",324:"TileOffset",325:"TileByteCounts",330:"SubIFDs",336:"DotRange",338:"ExtraSample",339:"SampleFormat",347:"JPEGTables",512:"JPEGProc",513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength",519:"JPEGQTables",520:"JPEGDCTables",521:"JPEGACTables",529:"YCbCrCoefficients",530:"YCbCrSubSampling",531:"YCbCrPositioning",532:"ReferenceBlackWhite",700:"XMP",33421:"CFARepeatPatternDim",33422:"CFAPattern",33432:"Copyright",33434:"ExposureTime",33437:"FNumber",33723:"IPTC/NAA",34377:"Photoshop",34665:"ExifIFD",34675:"ICC Profile",34850:"ExposureProgram",34853:"GPSInfo",34855:"ISOSpeedRatings",34858:"TimeZoneOffset",34859:"SelfTimeMode",36867:"DateTimeOriginal",36868:"DateTimeDigitized",37377:"ShutterSpeedValue",37378:"ApertureValue",37380:"ExposureBiasValue",37383:"MeteringMode",37385:"Flash",37386:"FocalLength",37390:"FocalPlaneXResolution",37391:"FocalPlaneYResolution",37392:"FocalPlaneResolutionUnit",37393:"ImageNumber",37398:"TIFF/EPStandardID",37399:"SensingMethod",37500:"MakerNote",37510:"UserComment",37724:"ImageSourceData",40092:"XPComment",40094:"XPKeywords",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelXDimension",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",50706:"DNGVersion",50707:"DNGBackwardVersion",50708:"UniqueCameraModel",50709:"LocalizedCameraModel",50710:"CFAPlaneColor",50711:"CFALayout",50712:"LinearizationTable",50713:"BlackLevelRepeatDim",50714:"BlackLevel",50716:"BlackLevelDeltaV",50717:"WhiteLevel",50718:"DefaultScale",50719:"DefaultCropOrigin",50720:"DefaultCropSize",50733:"BayerGreenSplit",50738:"AntiAliasStrength",50721:"ColorMatrix1",50722:"ColorMatrix2",50723:"CameraCalibration1",50724:"CameraCalibration2",50727:"AnalogBalance",50728:"AsShotNeutral",50730:"BaselineExposure",50731:"BaselineNoise",50732:"BaselineSharpness",50734:"LinearResponseLimit",50735:"CameraSerialNumber",50736:"LensInfo",50739:"ShadowScale",50740:"DNGPrivateData",50741:"MakerNoteSafety",50778:"CalibrationIlluminant1",50779:"CalibrationIlluminant2",50780:"BestQualityScale",50781:"RawDataUniqueID",50827:"OriginalRawFileName",50829:"ActiveArea",50830:"MaskedAreas",50931:"CameraCalibrationSignature",50932:"ProfileCalibrationSignature",50935:"NoiseReductionApplied",50936:"ProfileName",50937:"ProfileHueSatMapDims",50938:"ProfileHueSatMapData1",50939:"ProfileHueSatMapData2",50940:"ProfileToneCurve",50941:"ProfileEmbedPolicy",50942:"ProfileCopyright",50964:"ForwardMatrix1",50965:"ForwardMatrix2",50966:"PreviewApplicationName",50967:"PreviewApplicationVersion",50969:"PreviewSettingsDigest",50970:"PreviewColorSpace",50971:"PreviewDateTime",50972:"RawImageDigest",51008:"OpcodeList1",51009:"OpcodeList2",51022:"OpcodeList3",51041:"NoiseProfile",51089:"OriginalDefaultFinalSize",51090:"OriginalBestQualityFinalSize",51091:"OriginalDefaultCropSize",51125:"DefaultUserCrop"},S.ttypes={256:3,257:3,258:3,259:3,262:3,273:4,274:3,277:3,278:4,279:4,282:5,283:5,284:3,286:5,287:5,296:3,305:2,306:2,338:3,513:4,514:4,34665:4},S._readIFD=function(t,e,r,n){var i=t.readUshort(e,r);r+=2;var o={};n.push(o);for(var a=0;a<i;a++){var s=t.readUshort(e,r);r+=2;var u=t.readUshort(e,r);r+=2;var f=t.readUint(e,r);r+=4;var l=t.readUint(e,r);r+=4;var h=[];if(o["t"+s]=h,1==u||7==u)for(var c=0;c<f;c++)h.push(e[(f<5?r-4:l)+c]);if(2==u&&h.push(t.readASCII(e,f<5?r-4:l,f-1)),3==u)for(c=0;c<f;c++)h.push(t.readUshort(e,(f<3?r-4:l)+2*c));if(4==u)for(c=0;c<f;c++)h.push(t.readUint(e,(f<2?r-4:l)+4*c));if(5==u)for(c=0;c<f;c++)h.push(t.readUint(e,l+8*c)/t.readUint(e,l+8*c+4));if(8==u)for(c=0;c<f;c++)h.push(t.readShort(e,(f<3?r-4:l)+2*c));if(9==u)for(c=0;c<f;c++)h.push(t.readInt(e,(f<2?r-4:l)+4*c));if(10==u)for(c=0;c<f;c++)h.push(t.readInt(e,l+8*c)/t.readInt(e,l+8*c+4));if(11==u)for(c=0;c<f;c++)h.push(t.readFloat(e,l+4*c));if(12==u)for(c=0;c<f;c++)h.push(t.readDouble(e,l+8*c));if(0!=f&&0==h.length&&M("unknown TIFF tag type: ",u,"num:",f),330==s)for(c=0;c<f;c++)S._readIFD(t,e,h[c],n)}return r},S._writeIFD=function(t,e,r,n){var i=Object.keys(n);t.writeUshort(e,r,i.length);for(var o=(r+=2)+12*i.length+4,a=0;a<i.length;a++){var s=i[a],u=parseInt(s.slice(1)),f=S.ttypes[u];if(null==f)throw new Error("unknown type of tag: "+u);var l=n[s];2==f&&(l=l[0]+"\0");var h=l.length;t.writeUshort(e,r,u),r+=2,t.writeUshort(e,r,f),r+=2,t.writeUint(e,r,h);var c=[-1,1,1,2,4,8,0,0,0,0,0,0,8][f]*h,d=r+=4;if(4<c&&(t.writeUint(e,r,o),d=o),2==f&&t.writeASCII(e,d,l),3==f)for(var p=0;p<h;p++)t.writeUshort(e,d+2*p,l[p]);if(4==f)for(p=0;p<h;p++)t.writeUint(e,d+4*p,l[p]);if(5==f)for(p=0;p<h;p++)t.writeUint(e,d+8*p,Math.round(1e4*l[p])),t.writeUint(e,d+8*p+4,1e4);if(12==f)for(p=0;p<h;p++)t.writeDouble(e,d+8*p,l[p]);4<c&&(o+=c+=1&c),r+=4}return[r,o]},S.toRGBA8=function(t){var e=t.width,r=t.height,n=e*r,i=4*n,o=t.data,a=new Uint8Array(4*n),s=t.t262[0],u=t.t258?Math.min(32,t.t258[0]):1,f=t.isLE?1:0;if(0==s)for(var l=Math.ceil(u*e/8),h=0;h<r;h++){var c=h*l,d=h*e;if(1==u)for(var p=0;p<e;p++){var m=d+p<<2,b=o[c+(p>>3)]>>7-(7&p)&1;a[m]=a[m+1]=a[m+2]=255*(1-b),a[m+3]=255}if(4==u)for(p=0;p<e;p++)m=d+p<<2,b=o[c+(p>>1)]>>4-4*(1&p)&15,a[m]=a[m+1]=a[m+2]=17*(15-b),a[m+3]=255;if(8==u)for(p=0;p<e;p++)m=d+p<<2,b=o[c+p],a[m]=a[m+1]=a[m+2]=255-b,a[m+3]=255}else if(1==s)for(l=Math.ceil(u*e/8),h=0;h<r;h++){if(c=h*l,d=h*e,1==u)for(p=0;p<e;p++)m=d+p<<2,b=o[c+(p>>3)]>>7-(7&p)&1,a[m]=a[m+1]=a[m+2]=255*b,a[m+3]=255;if(2==u)for(p=0;p<e;p++)m=d+p<<2,b=o[c+(p>>2)]>>6-2*(3&p)&3,a[m]=a[m+1]=a[m+2]=85*b,a[m+3]=255;if(8==u)for(p=0;p<e;p++)m=d+p<<2,b=o[c+p],a[m]=a[m+1]=a[m+2]=b,a[m+3]=255;if(16==u)for(p=0;p<e;p++)m=d+p<<2,b=o[c+(2*p+f)],a[m]=a[m+1]=a[m+2]=Math.min(255,b),a[m+3]=255}else if(2==s)if(8==u)if(t.t338)if(0<t.t338[0])for(p=0;p<i;p++)a[p]=o[p];else for(p=0;p<i;p+=4)a[p]=o[p],a[p+1]=o[p+1],a[p+2]=o[p+2],a[p+3]=255;else{if(4==(v=t.t258?t.t258.length:3))for(p=0;p<i;p++)a[p]=o[p];if(3==v)for(p=0;p<n;p++){var g=3*p;a[m=p<<2]=o[g],a[m+1]=o[g+1],a[m+2]=o[g+2],a[m+3]=255}}else for(p=0;p<n;p++)g=6*p,a[m=p<<2]=o[g],a[m+1]=o[g+2],a[m+2]=o[g+4],a[m+3]=255;else if(3==s){var _=t.t320;for(p=0;p<n;p++){m=p<<2;var y=o[p];a[m]=_[y]>>8,a[m+1]=_[256+y]>>8,a[m+2]=_[512+y]>>8,a[m+3]=255}}else if(5==s){var v,w=4<(v=t.t258?t.t258.length:4)?1:0;for(p=0;p<n;p++){m=p<<2;var E=p*v,x=255-o[E],j=255-o[E+1],k=255-o[E+2],S=(255-o[E+3])*(1/255);a[m]=~~(x*S+.5),a[m+1]=~~(j*S+.5),a[m+2]=~~(k*S+.5),a[m+3]=255*(1-w)+o[E+4]*w}}else M("Unknown Photometric interpretation: "+s);return a},S.replaceIMG=function(){for(var t=document.getElementsByTagName("img"),e=0;e<t.length;e++){var r=t[e],n=r.getAttribute("src");if(null!=n){var i=n.split(".").pop().toLowerCase();if("tif"==i||"tiff"==i){var o=new XMLHttpRequest;S._xhrs.push(o),S._imgs.push(r),o.open("GET",n),o.responseType="arraybuffer",o.onload=S._imgLoaded,o.send()}}}},S._xhrs=[],S._imgs=[],S._imgLoaded=function(t){var e=t.target.response,r=S.decode(e),n=r[0];S.decodeImages(e,r);var i=S.toRGBA8(n),o=n.width,a=n.height,s=S._xhrs.indexOf(t.target),u=S._imgs[s];S._xhrs.splice(s,1),S._imgs.splice(s,1);var f=document.createElement("canvas");f.width=o,f.height=a;for(var l=f.getContext("2d"),h=l.createImageData(o,a),c=0;c<i.length;c++)h.data[c]=i[c];l.putImageData(h,0,0);var d=["style","class","id"];for(c=0;c<d.length;c++)f.setAttribute(d[c],u.getAttribute(d[c]));u.parentNode.replaceChild(f,u)},S._binBE={nextZero:function(t,e){for(;0!=t[e];)e++;return e},readUshort:function(t,e){return t[e]<<8|t[e+1]},readShort:function(t,e){var r=S._binBE.ui8;return r[0]=t[e+1],r[1]=t[e+0],S._binBE.i16[0]},readInt:function(t,e){var r=S._binBE.ui8;return r[0]=t[e+3],r[1]=t[e+2],r[2]=t[e+1],r[3]=t[e+0],S._binBE.i32[0]},readUint:function(t,e){var r=S._binBE.ui8;return r[0]=t[e+3],r[1]=t[e+2],r[2]=t[e+1],r[3]=t[e+0],S._binBE.ui32[0]},readASCII:function(t,e,r){for(var n="",i=0;i<r;i++)n+=String.fromCharCode(t[e+i]);return n},readFloat:function(t,e){for(var r=S._binBE.ui8,n=0;n<4;n++)r[n]=t[e+3-n];return S._binBE.fl32[0]},readDouble:function(t,e){for(var r=S._binBE.ui8,n=0;n<8;n++)r[n]=t[e+7-n];return S._binBE.fl64[0]},writeUshort:function(t,e,r){t[e]=r>>8&255,t[e+1]=255&r},writeUint:function(t,e,r){t[e]=r>>24&255,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=r>>0&255},writeASCII:function(t,e,r){for(var n=0;n<r.length;n++)t[e+n]=r.charCodeAt(n)},writeDouble:function(t,e,r){S._binBE.fl64[0]=r;for(var n=0;n<8;n++)t[e+n]=S._binBE.ui8[7-n]}},S._binBE.ui8=new Uint8Array(8),S._binBE.i16=new Int16Array(S._binBE.ui8.buffer),S._binBE.i32=new Int32Array(S._binBE.ui8.buffer),S._binBE.ui32=new Uint32Array(S._binBE.ui8.buffer),S._binBE.fl32=new Float32Array(S._binBE.ui8.buffer),S._binBE.fl64=new Float64Array(S._binBE.ui8.buffer),S._binLE={nextZero:S._binBE.nextZero,readUshort:function(t,e){return t[e+1]<<8|t[e]},readShort:function(t,e){var r=S._binBE.ui8;return r[0]=t[e+0],r[1]=t[e+1],S._binBE.i16[0]},readInt:function(t,e){var r=S._binBE.ui8;return r[0]=t[e+0],r[1]=t[e+1],r[2]=t[e+2],r[3]=t[e+3],S._binBE.i32[0]},readUint:function(t,e){var r=S._binBE.ui8;return r[0]=t[e+0],r[1]=t[e+1],r[2]=t[e+2],r[3]=t[e+3],S._binBE.ui32[0]},readASCII:S._binBE.readASCII,readFloat:function(t,e){for(var r=S._binBE.ui8,n=0;n<4;n++)r[n]=t[e+n];return S._binBE.fl32[0]},readDouble:function(t,e){for(var r=S._binBE.ui8,n=0;n<8;n++)r[n]=t[e+n];return S._binBE.fl64[0]}},S._copyTile=function(t,e,r,n,i,o,a,s){for(var u=Math.min(e,i-a),f=Math.min(r,o-s),l=0;l<f;l++)for(var h=(s+l)*i+a,c=l*e,d=0;d<u;d++)n[h+d]=t[c+d]}}()}).call(this,n("_process"))},{_process:223,pako:176}],256:[function(t,e,r){(function(r){function n(t){try{if(!r.localStorage)return!1}catch(t){return!1}var e=r.localStorage[t];return null!=e&&"true"===String(e).toLowerCase()}e.exports=function(t,e){if(n("noDeprecation"))return t;var r=!1;return function(){if(!r){if(n("throwDeprecation"))throw new Error(e);n("traceDeprecation")?console.trace(e):console.warn(e),r=!0}return t.apply(this,arguments)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:void 0!==window?window:{})},{}],257:[function(t,e,r){arguments[4][5][0].apply(r,arguments)},{dup:5}],258:[function(t,e,r){arguments[4][6][0].apply(r,arguments)},{"./support/isBuffer":257,_process:223,dup:6,inherits:166}],259:[function(t,e,r){"use strict";var n=t("global/window"),i=t("is-function"),b=t("parse-headers"),o=t("xtend");function a(t,e,r){var n=t;return i(e)?(r=e,"string"==typeof t&&(n={uri:t})):n=o(e,{uri:t}),n.callback=r,n}function g(t,e,r){return s(e=a(t,e,r))}function s(n){if(void 0===n.callback)throw new Error("callback argument missing");var i=!1,o=function(t,e,r){i||(i=!0,n.callback(t,e,r))};function e(t){return clearTimeout(u),t instanceof Error||(t=new Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,o(t,m)}function t(){if(!a){var t;clearTimeout(u),t=n.useXDR&&void 0===s.status?200:1223===s.status?204:s.status;var e=m,r=null;return 0!==t?(e={body:function(){var t=void 0;if(t=s.response?s.response:s.responseText||function(t){try{if("document"===t.responseType)return t.responseXML;var e=t.responseXML&&"parsererror"===t.responseXML.documentElement.nodeName;if(""===t.responseType&&!e)return t.responseXML}catch(t){}return null}(s),p)try{t=JSON.parse(t)}catch(t){}return t}(),statusCode:t,method:l,headers:{},url:f,rawRequest:s},s.getAllResponseHeaders&&(e.headers=b(s.getAllResponseHeaders()))):r=new Error("Internal XMLHttpRequest Error"),o(r,e,e.body)}}var r,a,s=n.xhr||null;s||(s=n.cors||n.useXDR?new g.XDomainRequest:new g.XMLHttpRequest);var u,f=s.url=n.uri||n.url,l=s.method=n.method||"GET",h=n.body||n.data,c=s.headers=n.headers||{},d=!!n.sync,p=!1,m={body:void 0,headers:{},statusCode:0,method:l,url:f,rawRequest:s};if("json"in n&&!1!==n.json&&(p=!0,c.accept||c.Accept||(c.Accept="application/json"),"GET"!==l&&"HEAD"!==l&&(c["content-type"]||c["Content-Type"]||(c["Content-Type"]="application/json"),h=JSON.stringify(!0===n.json?h:n.json))),s.onreadystatechange=function(){4===s.readyState&&setTimeout(t,0)},s.onload=t,s.onerror=e,s.onprogress=function(){},s.onabort=function(){a=!0},s.ontimeout=e,s.open(l,f,!d,n.username,n.password),d||(s.withCredentials=!!n.withCredentials),!d&&0<n.timeout&&(u=setTimeout(function(){if(!a){a=!0,s.abort("timeout");var t=new Error("XMLHttpRequest timeout");t.code="ETIMEDOUT",e(t)}},n.timeout)),s.setRequestHeader)for(r in c)c.hasOwnProperty(r)&&s.setRequestHeader(r,c[r]);else if(n.headers&&!function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}(n.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in n&&(s.responseType=n.responseType),"beforeSend"in n&&"function"==typeof n.beforeSend&&n.beforeSend(s),s.send(h||null),s}e.exports=g,(e.exports.default=g).XMLHttpRequest=n.XMLHttpRequest||function(){},g.XDomainRequest="withCredentials"in new g.XMLHttpRequest?g.XMLHttpRequest:n.XDomainRequest,function(t,e){for(var r=0;r<t.length;r++)e(t[r])}(["get","put","post","patch","head","delete"],function(n){g["delete"===n?"del":n]=function(t,e,r){return(e=a(t,e,r)).method=n.toUpperCase(),s(e)}})},{"global/window":163,"is-function":169,"parse-headers":196,xtend:261}],260:[function(t,e,r){e.exports=void 0!==self.DOMParser?function(t){return(new self.DOMParser).parseFromString(t,"application/xml")}:void 0!==self.ActiveXObject&&new self.ActiveXObject("Microsoft.XMLDOM")?function(t){var e=new self.ActiveXObject("Microsoft.XMLDOM");return e.async="false",e.loadXML(t),e}:function(t){var e=document.createElement("div");return e.innerHTML=t,e}},{}],261:[function(t,e,r){e.exports=function(){for(var t={},e=0;e<arguments.length;e++){var r=arguments[e];for(var n in r)i.call(r,n)&&(t[n]=r[n])}return t};var i=Object.prototype.hasOwnProperty},{}],262:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.srcOver=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=(t.r*t.a+e.r*e.a*(1-t.a))/n,o=(t.g*t.a+e.g*e.a*(1-t.a))/n,a=(t.b*t.a+e.b*e.a*(1-t.a))/n;return{r:i,g:o,b:a,a:n}},r.dstOver=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=(e.r*e.a+t.r*t.a*(1-e.a))/n,o=(e.g*e.a+t.g*t.a*(1-e.a))/n,a=(e.b*e.a+t.b*t.a*(1-e.a))/n;return{r:i,g:o,b:a,a:n}},r.multiply=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(i*s+i*(1-e.a)+s*(1-t.a))/n,h=(o*u+o*(1-e.a)+u*(1-t.a))/n,c=(a*f+a*(1-e.a)+f*(1-t.a))/n;return{r:l,g:h,b:c,a:n}},r.screen=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(i*e.a+s*t.a-i*s+i*(1-e.a)+s*(1-t.a))/n,h=(o*e.a+u*t.a-o*u+o*(1-e.a)+u*(1-t.a))/n,c=(a*e.a+f*t.a-a*f+a*(1-e.a)+f*(1-t.a))/n;return{r:l,g:h,b:c,a:n}},r.overlay=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(2*s<=e.a?2*i*s+i*(1-e.a)+s*(1-t.a):i*(1+e.a)+s*(1+t.a)-2*s*i-e.a*t.a)/n,h=(2*u<=e.a?2*o*u+o*(1-e.a)+u*(1-t.a):o*(1+e.a)+u*(1+t.a)-2*u*o-e.a*t.a)/n,c=(2*f<=e.a?2*a*f+a*(1-e.a)+f*(1-t.a):a*(1+e.a)+f*(1+t.a)-2*f*a-e.a*t.a)/n;return{r:l,g:h,b:c,a:n}},r.darken=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(Math.min(i*e.a,s*t.a)+i*(1-e.a)+s*(1-t.a))/n,h=(Math.min(o*e.a,u*t.a)+o*(1-e.a)+u*(1-t.a))/n,c=(Math.min(a*e.a,f*t.a)+a*(1-e.a)+f*(1-t.a))/n;return{r:l,g:h,b:c,a:n}},r.lighten=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(Math.max(i*e.a,s*t.a)+i*(1-e.a)+s*(1-t.a))/n,h=(Math.max(o*e.a,u*t.a)+o*(1-e.a)+u*(1-t.a))/n,c=(Math.max(a*e.a,f*t.a)+a*(1-e.a)+f*(1-t.a))/n;return{r:l,g:h,b:c,a:n}},r.hardLight=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(2*i<=t.a?2*i*s+i*(1-e.a)+s*(1-t.a):i*(1+e.a)+s*(1+t.a)-2*s*i-e.a*t.a)/n,h=(2*o<=t.a?2*o*u+o*(1-e.a)+u*(1-t.a):o*(1+e.a)+u*(1+t.a)-2*u*o-e.a*t.a)/n,c=(2*a<=t.a?2*a*f+a*(1-e.a)+f*(1-t.a):a*(1+e.a)+f*(1+t.a)-2*f*a-e.a*t.a)/n;return{r:l,g:h,b:c,a:n}},r.difference=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(i+s-2*Math.min(i*e.a,s*t.a))/n,h=(o+u-2*Math.min(o*e.a,u*t.a))/n,c=(a+f-2*Math.min(a*e.a,f*t.a))/n;return{r:l,g:h,b:c,a:n}},r.exclusion=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1;t.a*=r;var n=e.a+t.a-e.a*t.a,i=t.r*t.a,o=t.g*t.a,a=t.b*t.a,s=e.r*e.a,u=e.g*e.a,f=e.b*e.a,l=(i*e.a+s*t.a-2*i*s+i*(1-e.a)+s*(1-t.a))/n,h=(o*e.a+u*t.a-2*o*u+o*(1-e.a)+u*(1-t.a))/n,c=(a*e.a+f*t.a-2*a*f+a*(1-e.a)+f*(1-t.a))/n;return{r:l,g:h,b:c,a:n}}},{"core-js/modules/es6.object.define-property":129}],263:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(t,o,a){var e=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{},r=4<arguments.length?arguments[4]:void 0;"function"==typeof e&&(r=e,e={});if(!(t instanceof this.constructor))return h.throwError.call(this,"The source must be a Jimp image",r);if("number"!=typeof o||"number"!=typeof a)return h.throwError.call(this,"x and y must be numbers",r);var n=e,i=n.mode,s=n.opacitySource,u=n.opacityDest;i||(i=c.BLEND_SOURCE_OVER);("number"!=typeof s||s<0||1<s)&&(s=1);("number"!=typeof u||u<0||1<u)&&(u=1);var f=d[i];o=Math.round(o),a=Math.round(a);var l=this;1!==u&&l.opacity(u);t.scanQuiet(0,0,t.bitmap.width,t.bitmap.height,function(t,e,r){var n=l.getPixelIndex(o+t,a+e,c.EDGE_CROP),i=f({r:this.bitmap.data[r+0]/255,g:this.bitmap.data[r+1]/255,b:this.bitmap.data[r+2]/255,a:this.bitmap.data[r+3]/255},{r:l.bitmap.data[n+0]/255,g:l.bitmap.data[n+1]/255,b:l.bitmap.data[n+2]/255,a:l.bitmap.data[n+3]/255},s);l.bitmap.data[n+0]=this.constructor.limit255(255*i.r),l.bitmap.data[n+1]=this.constructor.limit255(255*i.g),l.bitmap.data[n+2]=this.constructor.limit255(255*i.b),l.bitmap.data[n+3]=this.constructor.limit255(255*i.a)}),(0,h.isNodePattern)(r)&&r.call(this,null,this);return this};var h=t("@jimp/utils"),c=n(t("../constants")),d=n(t("./composite-modes"));function n(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(t,r):{};n.get||n.set?Object.defineProperty(e,r,n):e[r]=t[r]}return e.default=t,e}e.exports=r.default},{"../constants":264,"./composite-modes":262,"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],264:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.EDGE_CROP=r.EDGE_WRAP=r.EDGE_EXTEND=r.BLEND_EXCLUSION=r.BLEND_DIFFERENCE=r.BLEND_HARDLIGHT=r.BLEND_LIGHTEN=r.BLEND_DARKEN=r.BLEND_OVERLAY=r.BLEND_SCREEN=r.BLEND_MULTIPLY=r.BLEND_DESTINATION_OVER=r.BLEND_SOURCE_OVER=r.VERTICAL_ALIGN_BOTTOM=r.VERTICAL_ALIGN_MIDDLE=r.VERTICAL_ALIGN_TOP=r.HORIZONTAL_ALIGN_RIGHT=r.HORIZONTAL_ALIGN_CENTER=r.HORIZONTAL_ALIGN_LEFT=r.AUTO=void 0;r.AUTO=-1;r.HORIZONTAL_ALIGN_LEFT=1;r.HORIZONTAL_ALIGN_CENTER=2;r.HORIZONTAL_ALIGN_RIGHT=4;r.VERTICAL_ALIGN_TOP=8;r.VERTICAL_ALIGN_MIDDLE=16;r.VERTICAL_ALIGN_BOTTOM=32;r.BLEND_SOURCE_OVER="srcOver";r.BLEND_DESTINATION_OVER="dstOver";r.BLEND_MULTIPLY="multiply";r.BLEND_SCREEN="screen";r.BLEND_OVERLAY="overlay";r.BLEND_DARKEN="darken";r.BLEND_LIGHTEN="lighten";r.BLEND_HARDLIGHT="hardLight";r.BLEND_DIFFERENCE="difference";r.BLEND_EXCLUSION="exclusion";r.EDGE_EXTEND=1;r.EDGE_WRAP=2;r.EDGE_CROP=3},{"core-js/modules/es6.object.define-property":129}],265:[function(z,t,H){(function(w){"use strict";z("core-js/modules/es6.array.is-array"),Object.defineProperty(H,"__esModule",{value:!0}),H.addConstants=B,H.addJimpMethods=N,H.jimpEvMethod=D,H.jimpEvChange=U,Object.defineProperty(H,"addType",{enumerable:!0,get:function(){return l.addType}}),H.default=void 0,z("core-js/modules/es6.function.bind"),z("core-js/modules/es6.reflect.construct"),z("core-js/modules/es7.symbol.async-iterator"),z("core-js/modules/es6.symbol"),z("core-js/modules/es6.regexp.replace"),z("core-js/modules/es6.number.constructor"),z("core-js/modules/es6.array.iterator"),z("core-js/modules/es7.object.entries"),z("core-js/modules/web.dom.iterable"),z("core-js/modules/es6.array.for-each"),z("core-js/modules/es6.object.define-property"),z("core-js/modules/es6.object.create"),z("core-js/modules/es6.object.set-prototype-of"),z("core-js/modules/es6.object.assign"),z("core-js/modules/es6.promise"),z("core-js/modules/es6.array.find"),z("core-js/modules/es6.typed.uint8-clamped-array"),z("core-js/modules/es6.regexp.match"),z("core-js/modules/es6.typed.uint8-array"),z("core-js/modules/es6.regexp.to-string"),z("core-js/modules/es6.date.to-string"),z("core-js/modules/es6.array.index-of");var E=c(z("fs")),a=c(z("path")),i=c(z("events")),x=z("@jimp/utils"),o=c(z("any-base")),s=c(z("mkdirp")),u=c(z("pixelmatch")),e=c(z("tinycolor2")),f=c(z("./modules/phash")),t=c(z("./request")),r=c(z("./composite")),j=c(z("./utils/promisify")),l=h(z("./utils/mime")),k=z("./utils/image-bitmap"),n=h(z("./constants"));function h(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(t,r):{};n.get||n.set?Object.defineProperty(e,r,n):e[r]=t[r]}return e.default=t,e}function c(t){return t&&t.__esModule?t:{default:t}}function d(t,e,r){return(d=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}()?Reflect.construct:function(t,e,r){var n=[null];n.push.apply(n,e);var i=new(Function.bind.apply(t,n));return r&&b(i,r.prototype),i}).apply(null,arguments)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=[],n=!0,i=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==s.return||s.return()}finally{if(i)throw o}}return r}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function m(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function S(t,e){return!e||"object"!==O(e)&&"function"!=typeof e?A(t):e}function I(t){return(I=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function b(t,e){return(b=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function A(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function T(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}for(var g="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_",_=[NaN,NaN],y=2;y<65;y++){var v=(0,o.default)(o.default.BIN,g.slice(0,y))(new Array(65).join("1"));_.push(v.length)}function P(){}function M(i,o){(0,t.default)(i,function(t,e,r){if(t)return o(t);if("object"===O(r)&&w.isBuffer(r))return o(null,r);var n="Could not load Buffer from <"+i.url+"> (HTTP: "+e.statusCode+")";return new Error(n)})}var L,C={data:null,width:null,height:null},R=function(t){function v(){for(var n,t=arguments.length,i=new Array(t),e=0;e<t;e++)i[e]=arguments[e];!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,v),T(A(A(n=S(this,I(v).call(this)))),"bitmap",C),T(A(A(n)),"_background",0),T(A(A(n)),"_originalMime",v.MIME_PNG),T(A(A(n)),"_exif",null),T(A(A(n)),"_rgba",!0),T(A(A(n)),"writeAsync",function(t){return(0,j.default)(n.write,A(A(n)),t)}),T(A(A(n)),"getBase64Async",function(t){return(0,j.default)(n.getBase64,A(A(n)),t)}),T(A(A(n)),"getBuffer",k.getBuffer),T(A(A(n)),"getBufferAsync",k.getBufferAsync),T(A(A(n)),"getPixelColour",n.getPixelColor),T(A(A(n)),"setPixelColour",n.setPixelColor);var r,o,a,s,u=A(A(n)),f=P;function l(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];(n||{}).methodName="constructor",setTimeout(function(){var t;n&&f===P?u.emitError("constructor",n):n||u.emitMulti("constructor","initialized"),(t=f).call.apply(t,[u].concat(e))},1)}if(r=i[0],-1<Object.prototype.toString.call(r).toLowerCase().indexOf("arraybuffer")&&(i[0]=function(t){for(var e=w.alloc(t.byteLength),r=new Uint8Array(t),n=0;n<e.length;++n)e[n]=r[n];return e}(i[0])),"number"==typeof i[0]&&"number"==typeof i[1]||parseInt(i[0],10)&&parseInt(i[1],10)){var h=parseInt(i[0],10),c=parseInt(i[1],10);if(f=i[2],"number"==typeof i[2]&&(n._background=i[2],f=i[3]),"string"==typeof i[2]&&(n._background=v.cssColorToHex(i[2]),f=i[3]),void 0===f&&(f=P),"function"!=typeof f)return S(n,x.throwError.call(A(A(n)),"cb must be a function",l));n.bitmap={data:w.alloc(h*c*4),width:h,height:c};for(var d=0;d<n.bitmap.data.length;d+=4)n.bitmap.data.writeUInt32BE(n._background,d);l(null,A(A(n)))}else if("object"===O(i[0])&&i[0].url){if("function"!=typeof(f=i[1]||P))return S(n,x.throwError.call(A(A(n)),"cb must be a function",l));M(i[0],function(t,e){if(t)return x.throwError.call(A(A(n)),t,l);n.parseBitmap(e,i[0].url,l)})}else if(i[0]instanceof v){var p=i[0];if(void 0===(f=i[1])&&(f=P),"function"!=typeof f)return S(n,x.throwError.call(A(A(n)),"cb must be a function",l));n.bitmap={data:w.from(p.bitmap.data),width:p.bitmap.width,height:p.bitmap.height},n._quality=p._quality,n._deflateLevel=p._deflateLevel,n._deflateStrategy=p._deflateStrategy,n._filterType=p._filterType,n._rgba=p._rgba,n._background=p._background,n._originalMime=p._originalMime,l(null,A(A(n)))}else if((s=i[0])&&"object"===O(s)&&"number"==typeof s.width&&"number"==typeof s.height&&(w.isBuffer(s.data)||s.data instanceof Uint8Array||"function"==typeof Uint8ClampedArray&&s.data instanceof Uint8ClampedArray)&&(s.data.length===s.width*s.height*4||s.data.length===s.width*s.height*3)){var m=i[0];f=i[1]||P;var b=m.width*m.height*4===m.data.length?w.from(m.data):function(t){if(t.length%3!=0)throw new Error("Buffer length is incorrect");for(var e=w.allocUnsafe(t.length/3*4),r=0,n=0;n<t.length;n++)e[r]=t[n],(n+1)%3==0&&(e[++r]=255),r++;return e}(m.data);n.bitmap={data:b,width:m.width,height:m.height},l(null,A(A(n)))}else if("string"==typeof i[0]){var g=i[0];if(void 0===(f=i[1])&&(f=P),"function"!=typeof f)return S(n,x.throwError.call(A(A(n)),"cb must be a function",l));o=g,a=function(t,e){if(t)return x.throwError.call(A(A(n)),t,l);n.parseBitmap(e,g,l)},E.default&&"function"==typeof E.default.readFile&&!o.match(/^(http|ftp)s?:\/\/./)?E.default.readFile(o,a):M({url:o},a)}else if("object"===O(i[0])&&w.isBuffer(i[0])){var _=i[0];if("function"!=typeof(f=i[1]))return S(n,x.throwError.call(A(A(n)),"cb must be a function",l));n.parseBitmap(_,null,l)}else{"function"!=typeof(f=i[i.length-1])&&"function"!=typeof(f=i[i.length-2])&&(f=P);var y=v.__extraConstructors.find(function(t){return t.test.apply(t,i)});if(!y)return S(n,x.throwError.call(A(A(n)),"No matching constructor overloading was found. Please see the docs for how to call the Jimp constructor.",l));new Promise(function(t,e){var r;return(r=y.run).call.apply(r,[A(A(n)),t,e].concat(i))}).then(function(){return l(null,A(A(n)))}).catch(l)}return n}var e,r,n;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&b(t,e)}(v,i.default),e=v,(r=[{key:"parseBitmap",value:function(t,e,r){k.parseBitmap.call(this,t,null,r)}},{key:"rgba",value:function(t,e){return"boolean"!=typeof t?x.throwError.call(this,"bool must be a boolean, true for RGBA or false for RGB",e):(this._rgba=t,(0,x.isNodePattern)(e)&&e.call(this,null,this),this)}},{key:"emitMulti",value:function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};r=Object.assign(r,{methodName:t,eventName:e}),this.emit("any",r),t&&this.emit(t,r),this.emit(e,r)}},{key:"emitError",value:function(t,e){this.emitMulti(t,"error",e)}},{key:"getHeight",value:function(){return this.bitmap.height}},{key:"getWidth",value:function(){return this.bitmap.width}},{key:"inspect",value:function(){return"<Jimp "+(this.bitmap===C?"pending...":this.bitmap.width+"x"+this.bitmap.height)+">"}},{key:"toString",value:function(){return"[object Jimp]"}},{key:"getMIME",value:function(){return this._originalMime||v.MIME_PNG}},{key:"getExtension",value:function(){var t=this.getMIME();return l.getExtension(t)}},{key:"write",value:function(n,i){var o=this;if(!E.default||!E.default.createWriteStream)throw new Error("Cant access the filesystem. You can use the getBase64 method.");if("string"!=typeof n)return x.throwError.call(this,"path must be a string",i);if(void 0===i&&(i=P),"function"!=typeof i)return x.throwError.call(this,"cb must be a function",i);var t=l.getType(n)||this.getMIME(),e=a.default.parse(n);return e.dir&&s.default.sync(e.dir),this.getBuffer(t,function(t,e){if(t)return x.throwError.call(o,t,i);var r=E.default.createWriteStream(n);r.on("open",function(){r.write(e),r.end()}).on("error",function(t){return x.throwError.call(o,t,i)}),r.on("finish",function(){i.call(o,null,o)})}),this}},{key:"getBase64",value:function(n,i){return n===v.AUTO&&(n=this.getMIME()),"string"!=typeof n?x.throwError.call(this,"mime must be a string",i):"function"!=typeof i?x.throwError.call(this,"cb must be a function",i):(this.getBuffer(n,function(t,e){if(t)return x.throwError.call(this,t,i);var r="data:"+n+";base64,"+e.toString("base64");i.call(this,null,r)}),this)}},{key:"hash",value:function(t,e){if("function"==typeof(t=t||64)&&(e=t,t=64),"number"!=typeof t)return x.throwError.call(this,"base must be a number",e);if(t<2||64<t)return x.throwError.call(this,"base must be a number between 2 and 64",e);var r=this.pHash();for(r=(0,o.default)(o.default.BIN,g.slice(0,t))(r);r.length<_[t];)r="0"+r;return(0,x.isNodePattern)(e)&&e.call(this,null,r),r}},{key:"pHash",value:function(){var t=new f.default;return t.getHash(this)}},{key:"distanceFromHash",value:function(t){var e=new f.default,r=e.getHash(this);return e.distance(r,t)}},{key:"getPixelIndex",value:function(t,e,r,n){var i,o;if("function"==typeof r&&void 0===n&&(n=r,r=null),r||(r=v.EDGE_EXTEND),"number"!=typeof t||"number"!=typeof e)return x.throwError.call(this,"x and y must be numbers",n);i=t=Math.round(t),o=e=Math.round(e),r===v.EDGE_EXTEND&&(t<0&&(i=0),t>=this.bitmap.width&&(i=this.bitmap.width-1),e<0&&(o=0),e>=this.bitmap.height&&(o=this.bitmap.height-1)),r===v.EDGE_WRAP&&(t<0&&(i=this.bitmap.width+t),t>=this.bitmap.width&&(i=t%this.bitmap.width),e<0&&(i=this.bitmap.height+e),e>=this.bitmap.height&&(o=e%this.bitmap.height));var a=this.bitmap.width*o+i<<2;return(i<0||i>=this.bitmap.width)&&(a=-1),(o<0||o>=this.bitmap.height)&&(a=-1),(0,x.isNodePattern)(n)&&n.call(this,null,a),a}},{key:"getPixelColor",value:function(t,e,r){if("number"!=typeof t||"number"!=typeof e)return x.throwError.call(this,"x and y must be numbers",r);t=Math.round(t),e=Math.round(e);var n=this.getPixelIndex(t,e),i=this.bitmap.data.readUInt32BE(n);return(0,x.isNodePattern)(r)&&r.call(this,null,i),i}},{key:"setPixelColor",value:function(t,e,r,n){if("number"!=typeof t||"number"!=typeof e||"number"!=typeof r)return x.throwError.call(this,"hex, x and y must be numbers",n);e=Math.round(e),r=Math.round(r);var i=this.getPixelIndex(e,r);return this.bitmap.data.writeUInt32BE(t,i),(0,x.isNodePattern)(n)&&n.call(this,null,this),this}},{key:"hasAlpha",value:function(){for(var t=0;t<this.bitmap.height;t++)for(var e=0;e<this.bitmap.width;e++){var r=this.bitmap.width*t+e<<2;if(255!==this.bitmap.data[r+3])return!0}return!1}}])&&m(e.prototype,r),n&&m(e,n),v}();function B(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:R;Object.entries(t).forEach(function(t){var e=p(t,2),r=e[0],n=e[1];i[r]=n})}function N(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:R;Object.entries(t).forEach(function(t){var e=p(t,2),r=e[0],n=e[1];i.prototype[r]=n})}function D(s,t,u){var f="before-"+t,l=t.replace(/e$/,"")+"ed";R.prototype[s]=function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var i,o=r[u.length-1],a=this;"function"==typeof o?(t=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],i=e[1];n?a.emitError(s,n):a.emitMulti(s,l,T({},s,i)),o.apply(this,e)},r[r.length-1]=t):t=!1,this.emitMulti(s,f);try{i=u.apply(this,r),t||this.emitMulti(s,l,T({},s,i))}catch(t){t.methodName=s,this.emitError(s,t)}return i},R.prototype[s+"Quiet"]=u}function U(t,e){D(t,"change",e)}B(n),N({composite:r.default}),R.__extraConstructors=[],R.appendConstructorOption=function(t,e,r){R.__extraConstructors.push({name:t,test:e,run:r})},R.read=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return new Promise(function(r,n){d(R,e.concat([function(t,e){t?n(t):r(e)}]))})},R.create=R.read,R.rgbaToInt=function(t,e,r,n,i){if("number"!=typeof t||"number"!=typeof e||"number"!=typeof r||"number"!=typeof n)return x.throwError.call(this,"r, g, b and a must be numbers",i);if(t<0||255<t)return x.throwError.call(this,"r must be between 0 and 255",i);if((e<0||255<e)&&x.throwError.call(this,"g must be between 0 and 255",i),r<0||255<r)return x.throwError.call(this,"b must be between 0 and 255",i);if(n<0||255<n)return x.throwError.call(this,"a must be between 0 and 255",i);t=Math.round(t),r=Math.round(r),e=Math.round(e),n=Math.round(n);var o=t*Math.pow(256,3)+e*Math.pow(256,2)+r*Math.pow(256,1)+n*Math.pow(256,0);return(0,x.isNodePattern)(i)&&i.call(this,null,o),o},R.intToRGBA=function(t,e){if("number"!=typeof t)return x.throwError.call(this,"i must be a number",e);var r={};return r.r=Math.floor(t/Math.pow(256,3)),r.g=Math.floor((t-r.r*Math.pow(256,3))/Math.pow(256,2)),r.b=Math.floor((t-r.r*Math.pow(256,3)-r.g*Math.pow(256,2))/Math.pow(256,1)),r.a=Math.floor((t-r.r*Math.pow(256,3)-r.g*Math.pow(256,2)-r.b*Math.pow(256,1))/Math.pow(256,0)),(0,x.isNodePattern)(e)&&e.call(this,null,r),r},R.cssColorToHex=function(t){return"number"==typeof(t=t||0)?Number(t):parseInt((0,e.default)(t).toHex8(),16)},R.limit255=function(t){return t=Math.max(t,0),t=Math.min(t,255)},R.diff=function(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:.1;if(!(t instanceof R&&e instanceof R))return x.throwError.call(this,"img1 and img2 must be an Jimp images");var n=t.bitmap,i=e.bitmap;if(n.width===i.width&&n.height===i.height||(n.width*n.height>i.width*i.height?t=t.cloneQuiet().resize(i.width,i.height):e=e.cloneQuiet().resize(n.width,n.height)),"number"!=typeof r||r<0||1<r)return x.throwError.call(this,"threshold must be a number between 0 and 1");var o=new R(n.width,n.height,4294967295);return{percent:(0,u.default)(n.data,i.data,o.bitmap.data,o.bitmap.width,o.bitmap.height,{threshold:r})/(o.bitmap.width*o.bitmap.height),image:o}},R.distance=function(t,e){var r=new f.default,n=r.getHash(t),i=r.getHash(e);return r.distance(n,i)},R.compareHashes=function(t,e){return(new f.default).distance(t,e)},R.colorDiff=function(t,e){var r=function(t){return Math.pow(t,2)},n=Math.max;return 0===t.a||t.a||(t.a=255),0===e.a||e.a||(e.a=255),(n(r(t.r-e.r),r(t.r-e.r-t.a+e.a))+n(r(t.g-e.g),r(t.g-e.g-t.a+e.a))+n(r(t.b-e.b),r(t.b-e.b-t.a+e.a)))/195075},D("clone","clone",function(t){var e=new R(this);return(0,x.isNodePattern)(t)&&t.call(e,null,e),e}),U("background",function(t,e){return"number"!=typeof t?x.throwError.call(this,"hex must be a hexadecimal rgba value",e):(this._background=t,(0,x.isNodePattern)(e)&&e.call(this,null,this),this)}),U("scan",function(t,e,r,n,i,o){if("number"!=typeof t||"number"!=typeof e)return x.throwError.call(this,"x and y must be numbers",o);if("number"!=typeof r||"number"!=typeof n)return x.throwError.call(this,"w and h must be numbers",o);if("function"!=typeof i)return x.throwError.call(this,"f must be a function",o);var a=(0,x.scan)(this,t,e,r,n,i);return(0,x.isNodePattern)(o)&&o.call(this,null,a),a}),void 0!==window&&"object"===(void 0===window?"undefined":O(window))&&(L=window),"undefined"!=typeof self&&"object"===("undefined"==typeof self?"undefined":O(self))&&(L=self),L.Jimp=R,L.Buffer=w;var F=R;H.default=F}).call(this,z("buffer").Buffer)},{"./composite":263,"./constants":264,"./modules/phash":266,"./request":267,"./utils/image-bitmap":268,"./utils/mime":269,"./utils/promisify":270,"@jimp/utils":303,"any-base":1,buffer:16,"core-js/modules/es6.array.find":117,"core-js/modules/es6.array.for-each":118,"core-js/modules/es6.array.index-of":120,"core-js/modules/es6.array.is-array":121,"core-js/modules/es6.array.iterator":122,"core-js/modules/es6.date.to-string":124,"core-js/modules/es6.function.bind":125,"core-js/modules/es6.number.constructor":126,"core-js/modules/es6.object.assign":127,"core-js/modules/es6.object.create":128,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.object.set-prototype-of":131,"core-js/modules/es6.promise":132,"core-js/modules/es6.reflect.construct":133,"core-js/modules/es6.regexp.match":135,"core-js/modules/es6.regexp.replace":136,"core-js/modules/es6.regexp.to-string":138,"core-js/modules/es6.symbol":141,"core-js/modules/es6.typed.uint8-array":144,"core-js/modules/es6.typed.uint8-clamped-array":145,"core-js/modules/es7.object.entries":147,"core-js/modules/es7.symbol.async-iterator":148,"core-js/modules/web.dom.iterable":149,events:151,fs:14,mkdirp:174,path:197,pixelmatch:199,tinycolor2:250}],266:[function(t,e,r){"use strict";function n(t,e){this.size=this.size||t,this.smallerSize=this.smallerSize||e,function(t){for(var e=1;e<t;e++)d[e]=1;d[0]=1/Math.sqrt(2)}(this.size)}function c(t){var e={};return e.r=Math.floor(t/Math.pow(256,3)),e.g=Math.floor((t-e.r*Math.pow(256,3))/Math.pow(256,2)),e.b=Math.floor((t-e.r*Math.pow(256,3)-e.g*Math.pow(256,2))/Math.pow(256,1)),e.a=Math.floor((t-e.r*Math.pow(256,3)-e.g*Math.pow(256,2)-e.b*Math.pow(256,1))/Math.pow(256,0)),e}n.prototype.size=32,n.prototype.smallerSize=8,n.prototype.distance=function(t,e){for(var r=0,n=0;n<t.length;n++)t[n]!==e[n]&&r++;return r/t.length},n.prototype.getHash=function(t){(t=t.clone().resize(this.size,this.size)).grayscale();for(var e=[],r=0;r<t.bitmap.width;r++){e[r]=[];for(var n=0;n<t.bitmap.height;n++)e[r][n]=c(t.getPixelColor(r,n)).b}for(var i=function(t,e){for(var r=e,n=[],i=0;i<r;i++){n[i]=[];for(var o=0;o<r;o++){for(var a=0,s=0;s<r;s++)for(var u=0;u<r;u++)a+=Math.cos((2*s+1)/(2*r)*i*Math.PI)*Math.cos((2*u+1)/(2*r)*o*Math.PI)*t[s][u];a*=d[i]*d[o]/4,n[i][o]=a}}return n}(e,this.size),o=0,a=0;a<this.smallerSize;a++)for(var s=0;s<this.smallerSize;s++)o+=i[a][s];for(var u=o/(this.smallerSize*this.smallerSize),f="",l=0;l<this.smallerSize;l++)for(var h=0;h<this.smallerSize;h++)f+=i[l][h]>u?"1":"0";return f};var d=[];e.exports=n},{}],267:[function(e,r,t){(function(t,i){"use strict";e("core-js/modules/es6.array.for-each"),e("core-js/modules/es6.array.filter"),e("core-js/modules/web.dom.iterable"),e("core-js/modules/es6.array.iterator"),e("core-js/modules/es6.object.keys"),e("core-js/modules/es6.object.define-property"),e("core-js/modules/es6.object.assign"),t.browser,r.exports=function(e,r){var n=new XMLHttpRequest;n.open("GET",e.url,!0),n.responseType="arraybuffer",n.addEventListener("load",function(){if(n.status<400)try{var t=i.from(this.response);r(null,n,t)}catch(t){return r(new Error("Response is not a buffer for url "+e.url+". Error: "+t.message))}else r(new Error("HTTP Status "+n.status+" for url "+e.url))}),n.addEventListener("error",function(t){r(t)}),n.send()}}).call(this,e("_process"),e("buffer").Buffer)},{_process:223,buffer:16,"core-js/modules/es6.array.filter":115,"core-js/modules/es6.array.for-each":118,"core-js/modules/es6.array.iterator":122,"core-js/modules/es6.object.assign":127,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.object.keys":130,"core-js/modules/web.dom.iterable":149,phin:198}],268:[function(i,t,h){(function(o){"use strict";i("core-js/modules/es6.object.define-property"),Object.defineProperty(h,"__esModule",{value:!0}),h.parseBitmap=function(t,e,r){var n=function(t,e){var r=(0,a.default)(t);if(r)return r.mime;if(e)return l.getType(e);return null}(t,e);if("string"!=typeof n)return r(new Error("Could not find MIME for Buffer <"+e+">"));this._originalMime=n.toLowerCase();try{var i=this.getMIME();if(!this.constructor.decoders[i])return u.throwError.call(this,"Unsupported MIME type: "+i,r);this.bitmap=this.constructor.decoders[i](t)}catch(t){return r.call(this,t,this)}try{this._exif=s.default.create(t).parse(),function(t){var e=t._exif;if(e&&e.tags&&e.tags.Orientation)switch(t._exif.tags.Orientation){case 1:break;case 2:t.mirror(!0,!1);break;case 3:t.rotate(180,!1);break;case 4:t.mirror(!1,!0);break;case 5:t.rotate(-90,!1).mirror(!0,!1);break;case 6:t.rotate(-90,!1);break;case 7:t.rotate(90,!1).mirror(!0,!1);break;case 8:t.rotate(-270,!1)}}(this)}catch(t){}return r.call(this,null,this),this},h.getBuffer=n,h.getBufferAsync=function(t){return(0,e.default)(n,this,t)};var a=r(i("file-type")),s=r(i("exif-parser")),u=i("@jimp/utils"),f=t(i("../constants")),l=t(i("./mime")),e=r(i("./promisify"));function t(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(t,r):{};n.get||n.set?Object.defineProperty(e,r,n):e[r]=t[r]}return e.default=t,e}function r(t){return t&&t.__esModule?t:{default:t}}function n(t,e){if(t===f.AUTO&&(t=this.getMIME()),"string"!=typeof t)return u.throwError.call(this,"mime must be a string",e);if("function"!=typeof e)return u.throwError.call(this,"cb must be a function",e);var r,n;if(t=t.toLowerCase(),this._rgba&&this.constructor.hasAlpha[t]?this.bitmap.data=o.from(this.bitmap.data):this.bitmap.data=(r=this.constructor,n=this,new r(n.bitmap.width,n.bitmap.height,n._background).composite(n,0,0).bitmap).data,this.constructor.encoders[t]){var i=this.constructor.encoders[t](this);e.call(this,null,i)}else e.call(this,"Unsupported MIME type: "+t);return this}}).call(this,i("buffer").Buffer)},{"../constants":264,"./mime":269,"./promisify":270,"@jimp/utils":303,buffer:16,"core-js/modules/es6.object.define-property":129,"exif-parser":152,"file-type":161}],269:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.getExtension=r.getType=r.addType=void 0,t("core-js/modules/es6.regexp.split"),t("core-js/modules/es7.array.includes"),t("core-js/modules/es6.string.includes"),t("core-js/modules/web.dom.iterable"),t("core-js/modules/es6.array.iterator"),t("core-js/modules/es7.object.entries"),t("core-js/modules/es6.array.find");var i={};r.addType=function(t,e){i[t]=e};r.getType=function(t){var e,r=t.split("/").slice(-1),n=r[r.length-1].split(".")[1];return(e=n,Object.entries(i).find(function(t){return t[1].includes(e)})||[])[0]};r.getExtension=function(t){return(i[t.toLowerCase()]||[])[0]}},{"core-js/modules/es6.array.find":117,"core-js/modules/es6.array.iterator":122,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.regexp.split":137,"core-js/modules/es6.string.includes":139,"core-js/modules/es7.array.includes":146,"core-js/modules/es7.object.entries":147,"core-js/modules/web.dom.iterable":149}],270:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es6.function.bind"),t("core-js/modules/es6.promise");var n=function(t,e){for(var r=arguments.length,i=new Array(2<r?r-2:0),n=2;n<r;n++)i[n-2]=arguments[n];return new Promise(function(r,n){i.push(function(t,e){t&&n(t),r(e)}),t.bind(e).apply(void 0,i)})};r.default=n,e.exports=r.default},{"core-js/modules/es6.function.bind":125,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.promise":132}],271:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:n.default,a={hasAlpha:{},encoders:{},decoders:{},class:{},constants:{}};function r(t){Object.entries(t).forEach(function(t){var e,r,n=(r=2,function(t){if(Array.isArray(t))return t}(e=t)||function(t,e){var r=[],n=!0,i=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==s.return||s.return()}finally{if(i)throw o}}return r}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()),i=n[0],o=n[1];a[i]=s({},a[i],o)})}t.types&&(t.types.forEach(function(t){var e=t();Array.isArray(e.mime)?n.addType.apply(void 0,i(e.mime)):Object.entries(e.mime).forEach(function(t){return n.addType.apply(void 0,i(t))});delete e.mime,r(e)}),e.decoders=s({},e.decoders,a.decoders),e.encoders=s({},e.encoders,a.encoders),e.hasAlpha=s({},e.hasAlpha,a.hasAlpha));t.plugins&&t.plugins.forEach(function(t){var e=t(n.jimpEvChange)||{};e.class||e.constants?r(e):r({class:e})});return(0,n.addJimpMethods)(a.class,e),(0,n.addConstants)(a.constants,e),n.default},t("core-js/modules/es6.string.iterator"),t("core-js/modules/es6.array.from"),t("core-js/modules/es6.regexp.to-string"),t("core-js/modules/es6.date.to-string"),t("core-js/modules/es6.array.filter"),t("core-js/modules/es6.object.keys"),t("core-js/modules/es6.object.define-property"),t("core-js/modules/es7.symbol.async-iterator"),t("core-js/modules/es6.symbol"),t("core-js/modules/es6.array.is-array"),t("core-js/modules/es6.array.iterator"),t("core-js/modules/es7.object.entries"),t("core-js/modules/web.dom.iterable"),t("core-js/modules/es6.array.for-each");var n=function(t){{if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){var n=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(t,r):{};n.get||n.set?Object.defineProperty(e,r,n):e[r]=t[r]}return e.default=t,e}}(t("@jimp/core"));function i(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),n.forEach(function(t){o(e,t,r[t])})}return e}function o(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}e.exports=r.default},{"@jimp/core":265,"core-js/modules/es6.array.filter":115,"core-js/modules/es6.array.for-each":118,"core-js/modules/es6.array.from":119,"core-js/modules/es6.array.is-array":121,"core-js/modules/es6.array.iterator":122,"core-js/modules/es6.date.to-string":124,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.object.keys":130,"core-js/modules/es6.regexp.to-string":138,"core-js/modules/es6.string.iterator":140,"core-js/modules/es6.symbol":141,"core-js/modules/es7.object.entries":147,"core-js/modules/es7.symbol.async-iterator":148,"core-js/modules/web.dom.iterable":149}],272:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(t("@jimp/custom")),i=a(t("@jimp/types")),o=a(t("@jimp/plugins"));function a(t){return t&&t.__esModule?t:{default:t}}var s=(0,n.default)({types:[i.default],plugins:[o.default]});r.default=s,e.exports=r.default},{"@jimp/custom":271,"@jimp/plugins":296,"@jimp/types":302,"core-js/modules/es6.object.define-property":129}],273:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es7.symbol.async-iterator"),t("core-js/modules/es6.symbol");var i=t("@jimp/utils");function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.default=function(){return{blit:function(t,p,m,b,g,e,r,n){if(!(t instanceof this.constructor))return i.throwError.call(this,"The source must be a Jimp image",n);if("number"!=typeof p||"number"!=typeof m)return i.throwError.call(this,"x and y must be numbers",n);if("function"==typeof b)n=b,g=b=0,e=t.bitmap.width,r=t.bitmap.height;else{if(o(b)!==o(g)||o(g)!==o(e)||o(e)!==o(r))return i.throwError.call(this,"srcx, srcy, srcw, srch must be numbers",n);b=b||0,g=g||0,e=e||t.bitmap.width,r=r||t.bitmap.height}p=Math.round(p),m=Math.round(m),b=Math.round(b),g=Math.round(g),e=Math.round(e),r=Math.round(r);var _=this.bitmap.width,y=this.bitmap.height,v=this;return t.scanQuiet(b,g,e,r,function(t,e,r){var n=p+t-b,i=m+e-g;if(0<=n&&0<=i&&0<_-n&&0<y-i){var o=v.getPixelIndex(n,i),a=this.bitmap.data[r],s=this.bitmap.data[r+1],u=this.bitmap.data[r+2],f=this.bitmap.data[r+3],l=v.bitmap.data[o],h=v.bitmap.data[o+1],c=v.bitmap.data[o+2],d=v.bitmap.data[o+3];v.bitmap.data[o]=(f*(a-l)-l+255>>8)+l,v.bitmap.data[o+1]=(f*(s-h)-h+255>>8)+h,v.bitmap.data[o+2]=(f*(u-c)-c+255>>8)+c,v.bitmap.data[o+3]=this.constructor.limit255(d+f)}}),(0,i.isNodePattern)(n)&&n.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.symbol":141,"core-js/modules/es7.symbol.async-iterator":148}],274:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.shgTable=r.mulTable=void 0;r.mulTable=[1,57,41,21,203,34,97,73,227,91,149,62,105,45,39,137,241,107,3,173,39,71,65,238,219,101,187,87,81,151,141,133,249,117,221,209,197,187,177,169,5,153,73,139,133,127,243,233,223,107,103,99,191,23,177,171,165,159,77,149,9,139,135,131,253,245,119,231,224,109,211,103,25,195,189,23,45,175,171,83,81,79,155,151,147,9,141,137,67,131,129,251,123,30,235,115,113,221,217,53,13,51,50,49,193,189,185,91,179,175,43,169,83,163,5,79,155,19,75,147,145,143,35,69,17,67,33,65,255,251,247,243,239,59,29,229,113,111,219,27,213,105,207,51,201,199,49,193,191,47,93,183,181,179,11,87,43,85,167,165,163,161,159,157,155,77,19,75,37,73,145,143,141,35,138,137,135,67,33,131,129,255,63,250,247,61,121,239,237,117,29,229,227,225,111,55,109,216,213,211,209,207,205,203,201,199,197,195,193,48,190,47,93,185,183,181,179,178,176,175,173,171,85,21,167,165,41,163,161,5,79,157,78,154,153,19,75,149,74,147,73,144,143,71,141,140,139,137,17,135,134,133,66,131,65,129,1];r.shgTable=[0,9,10,10,14,12,14,14,16,15,16,15,16,15,15,17,18,17,12,18,16,17,17,19,19,18,19,18,18,19,19,19,20,19,20,20,20,20,20,20,15,20,19,20,20,20,21,21,21,20,20,20,21,18,21,21,21,21,20,21,17,21,21,21,22,22,21,22,22,21,22,21,19,22,22,19,20,22,22,21,21,21,22,22,22,18,22,22,21,22,22,23,22,20,23,22,22,23,23,21,19,21,21,21,23,23,23,22,23,23,21,23,22,23,18,22,23,20,22,23,23,23,21,22,20,22,21,22,24,24,24,24,24,22,21,24,23,23,24,21,24,23,24,22,24,24,22,24,24,22,23,24,24,24,20,23,22,23,24,24,24,24,24,24,24,23,21,23,22,23,24,24,24,22,24,24,24,23,22,24,24,25,23,25,25,23,24,25,25,24,22,25,25,25,24,23,24,25,25,25,25,25,25,25,25,25,25,25,25,23,25,23,24,25,25,25,25,25,25,25,25,25,24,22,25,25,23,25,25,20,24,25,24,25,25,22,24,25,24,25,24,25,25,24,25,25,25,25,22,25,25,25,24,25,24,25,18]},{"core-js/modules/es6.object.define-property":129}],275:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var A=t("@jimp/utils"),T=t("./blur-tables");r.default=function(){return{blur:function(t,e){if("number"!=typeof t)return A.throwError.call(this,"r must be a number",e);if(t<1)return A.throwError.call(this,"r must be greater than 0",e);for(var r,n,i,o,a,s,u,f,l,h,c,d,p,m,b=this.bitmap.width-1,g=this.bitmap.height-1,_=t+1,y=T.mulTable[t],v=T.shgTable[t],w=[],E=[],x=[],j=[],k=[],S=[],I=2;0<I--;){for(s=p=d=0;s<this.bitmap.height;s++){for(r=this.bitmap.data[p]*_,n=this.bitmap.data[p+1]*_,i=this.bitmap.data[p+2]*_,o=this.bitmap.data[p+3]*_,u=1;u<=t;u++)f=p+((b<u?b:u)<<2),r+=this.bitmap.data[f++],n+=this.bitmap.data[f++],i+=this.bitmap.data[f++],o+=this.bitmap.data[f];for(a=0;a<this.bitmap.width;a++)w[d]=r,E[d]=n,x[d]=i,j[d]=o,0===s&&(k[a]=((f=a+_)<b?f:b)<<2,S[a]=0<(f=a-t)?f<<2:0),l=p+k[a],h=p+S[a],r+=this.bitmap.data[l++]-this.bitmap.data[h++],n+=this.bitmap.data[l++]-this.bitmap.data[h++],i+=this.bitmap.data[l++]-this.bitmap.data[h++],o+=this.bitmap.data[l]-this.bitmap.data[h],d++;p+=this.bitmap.width<<2}for(a=0;a<this.bitmap.width;a++){for(r=w[c=a]*_,n=E[c]*_,i=x[c]*_,o=j[c]*_,u=1;u<=t;u++)r+=w[c+=g<u?0:this.bitmap.width],n+=E[c],i+=x[c],o+=j[c];for(d=a<<2,s=0;s<this.bitmap.height;s++)m=o*y>>>v,255<(this.bitmap.data[d+3]=m)&&(this.bitmap.data[d+3]=255),0<m?(m=255/m,this.bitmap.data[d]=(r*y>>>v)*m,this.bitmap.data[d+1]=(n*y>>>v)*m,this.bitmap.data[d+2]=(i*y>>>v)*m):(this.bitmap.data[d+2]=0,this.bitmap.data[d+1]=0,this.bitmap.data[d]=0),0===a&&(k[s]=((f=s+_)<g?f:g)*this.bitmap.width,S[s]=0<(f=s-t)?f*this.bitmap.width:0),l=a+k[s],h=a+S[s],r+=w[l]-w[h],n+=E[l]-E[h],i+=x[l]-x[h],o+=j[l]-j[h],d+=this.bitmap.width<<2}}return(0,A.isNodePattern)(e)&&e.call(this,null,this),this}}},e.exports=r.default},{"./blur-tables":274,"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],276:[function(i,o,a){(function(n){"use strict";i("core-js/modules/es6.object.define-property"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,i("core-js/modules/es6.string.iterator"),i("core-js/modules/es6.array.from"),i("core-js/modules/es6.regexp.to-string"),i("core-js/modules/es6.date.to-string"),i("core-js/modules/es7.symbol.async-iterator"),i("core-js/modules/es6.symbol"),i("core-js/modules/web.dom.iterable"),i("core-js/modules/es6.array.for-each"),i("core-js/modules/es6.array.map"),i("core-js/modules/es6.array.is-array");var t,u=(t=i("tinycolor2"))&&t.__esModule?t:{default:t},E=i("@jimp/utils");function f(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function l(t,e,r,n){for(var i=[0,0,0],o=(e.length-1)/2,a=0;a<e.length;a+=1)for(var s=0;s<e[a].length;s+=1){var u=t.getPixelIndex(r+a-o,n+s-o);i[0]+=t.bitmap.data[u]*e[a][s],i[1]+=t.bitmap.data[u+1]*e[a][s],i[2]+=t.bitmap.data[u+2]*e[a][s]}return i}var h=function(t){return null!=t};function e(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=parseInt(.2126*this.bitmap.data[r]+.7152*this.bitmap.data[r+1]+.0722*this.bitmap.data[r+2],10);this.bitmap.data[r]=n,this.bitmap.data[r+1]=n,this.bitmap.data[r+2]=n}),(0,E.isNodePattern)(t)&&t.call(this,null,this),this}function c(t,e){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:50;return{r:(e.r-t.r)*(r/100)+t.r,g:(e.g-t.g)*(r/100)+t.g,b:(e.b-t.b)*(r/100)+t.b}}function r(o,a){var s=this;return o&&Array.isArray(o)?(o=o.map(function(t){return"xor"!==t.apply&&"mix"!==t.apply||(t.params[0]=(0,u.default)(t.params[0]).toRgb()),t}),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n={r:s.bitmap.data[r],g:s.bitmap.data[r+1],b:s.bitmap.data[r+2]},i=function(t,e){return s.constructor.limit255(n[t]+e)};o.forEach(function(t){if("mix"===t.apply)n=c(n,t.params[0],t.params[1]);else if("tint"===t.apply)n=c(n,{r:255,g:255,b:255},t.params[0]);else if("shade"===t.apply)n=c(n,{r:0,g:0,b:0},t.params[0]);else if("xor"===t.apply)n={r:n.r^t.params[0].r,g:n.g^t.params[0].g,b:n.b^t.params[0].b};else if("red"===t.apply)n.r=i("r",t.params[0]);else if("green"===t.apply)n.g=i("g",t.params[0]);else if("blue"===t.apply)n.b=i("b",t.params[0]);else{var e;if("hue"===t.apply&&(t.apply="spin"),!(n=(0,u.default)(n))[t.apply])return E.throwError.call(s,"action "+t.apply+" not supported",a);n=(e=n)[t.apply].apply(e,f(t.params)).toRgb()}}),s.bitmap.data[r]=n.r,s.bitmap.data[r+1]=n.g,s.bitmap.data[r+2]=n.b}),(0,E.isNodePattern)(a)&&a.call(this,null,this),this):E.throwError.call(this,"actions must be an array",a)}a.default=function(){return{brightness:function(n,t){return"number"!=typeof n?E.throwError.call(this,"val must be numbers",t):n<-1||1<n?E.throwError.call(this,"val must be a number between -1 and +1",t):(this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data[r+2]=n<0?(this.bitmap.data[r]=this.bitmap.data[r]*(1+n),this.bitmap.data[r+1]=this.bitmap.data[r+1]*(1+n),this.bitmap.data[r+2]*(1+n)):(this.bitmap.data[r]=this.bitmap.data[r]+(255-this.bitmap.data[r])*n,this.bitmap.data[r+1]=this.bitmap.data[r+1]+(255-this.bitmap.data[r+1])*n,this.bitmap.data[r+2]+(255-this.bitmap.data[r+2])*n)}),(0,E.isNodePattern)(t)&&t.call(this,null,this),this)},contrast:function(t,e){if("number"!=typeof t)return E.throwError.call(this,"val must be numbers",e);if(t<-1||1<t)return E.throwError.call(this,"val must be a number between -1 and +1",e);var r=(t+1)/(1-t);function n(t){return(t=Math.floor(r*(t-127)+127))<0?0:255<t?255:t}return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data[r]=n(this.bitmap.data[r]),this.bitmap.data[r+1]=n(this.bitmap.data[r+1]),this.bitmap.data[r+2]=n(this.bitmap.data[r+2])}),(0,E.isNodePattern)(e)&&e.call(this,null,this),this},posterize:function(n,t){return"number"!=typeof n?E.throwError.call(this,"n must be numbers",t):(n<2&&(n=2),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data[r]=Math.floor(this.bitmap.data[r]/255*(n-1))/(n-1)*255,this.bitmap.data[r+1]=Math.floor(this.bitmap.data[r+1]/255*(n-1))/(n-1)*255,this.bitmap.data[r+2]=Math.floor(this.bitmap.data[r+2]/255*(n-1))/(n-1)*255}),(0,E.isNodePattern)(t)&&t.call(this,null,this),this)},greyscale:e,grayscale:e,opacity:function(i,t){return"number"!=typeof i?E.throwError.call(this,"f must be a number",t):i<0||1<i?E.throwError.call(this,"f must be a number from 0 to 1",t):(this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=this.bitmap.data[r+3]*i;this.bitmap.data[r+3]=n}),(0,E.isNodePattern)(t)&&t.call(this,null,this),this)},sepia:function(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=this.bitmap.data[r],i=this.bitmap.data[r+1],o=this.bitmap.data[r+2];o=.272*(n=.393*n+.769*i+.189*o)+.534*(i=.349*n+.686*i+.168*o)+.131*o,this.bitmap.data[r]=n<255?n:255,this.bitmap.data[r+1]=i<255?i:255,this.bitmap.data[r+2]=o<255?o:255}),(0,E.isNodePattern)(t)&&t.call(this,null,this),this},fade:function(t,e){return"number"!=typeof t?E.throwError.call(this,"f must be a number",e):t<0||1<t?E.throwError.call(this,"f must be a number from 0 to 1",e):(this.opacity(1-t),(0,E.isNodePattern)(e)&&e.call(this,null,this),this)},convolution:function(o,a,t){"function"==typeof a&&void 0===t&&(t=a,a=null),a||(a=this.constructor.EDGE_EXTEND);var s,u,f,l,h,c,d,p,m,b,g=n.from(this.bitmap.data),e=o.length,r=o[0].length,_=Math.floor(e/2),y=Math.floor(r/2),v=-_,w=-y;return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){u=f=l=0;for(var n=v;n<=_;n++)for(var i=w;i<=y;i++)p=t+i,m=e+n,s=o[n+_][i+y],-1===(b=this.getPixelIndex(p,m,a))?h=c=d=0:(h=this.bitmap.data[b+0],c=this.bitmap.data[b+1],d=this.bitmap.data[b+2]),u+=s*h,f+=s*c,l+=s*d;u<0&&(u=0),f<0&&(f=0),l<0&&(l=0),255<u&&(u=255),255<f&&(f=255),255<l&&(l=255),g[r+0]=u,g[r+1]=f,g[r+2]=l}),this.bitmap.data=g,(0,E.isNodePattern)(t)&&t.call(this,null,this),this},opaque:function(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data[r+3]=255}),(0,E.isNodePattern)(t)&&t.call(this,null,this),this},pixelate:function(i,t,e,r,n,o){if("function"==typeof t)o=t,t=e=r=n=null;else{if("number"!=typeof i)return E.throwError.call(this,"size must be a number",o);if(h(t)&&"number"!=typeof t)return E.throwError.call(this,"x must be a number",o);if(h(e)&&"number"!=typeof e)return E.throwError.call(this,"y must be a number",o);if(h(r)&&"number"!=typeof r)return E.throwError.call(this,"w must be a number",o);if(h(n)&&"number"!=typeof n)return E.throwError.call(this,"h must be a number",o)}var a=[[1/16,.125,1/16],[.125,.25,.125],[1/16,.125,1/16]];t=t||0,e=e||0,r=h(r)?r:this.bitmap.width-t,n=h(n)?n:this.bitmap.height-e;var s=this.cloneQuiet();return this.scanQuiet(t,e,r,n,function(t,e,r){t=i*Math.floor(t/i),e=i*Math.floor(e/i);var n=l(s,a,t,e);this.bitmap.data[r]=n[0],this.bitmap.data[r+1]=n[1],this.bitmap.data[r+2]=n[2]}),(0,E.isNodePattern)(o)&&o.call(this,null,this),this},convolute:function(i,t,e,r,n,o){if(!Array.isArray(i))return E.throwError.call(this,"the kernel must be an array",o);if("function"==typeof t)o=t,n=r=e=t=null;else{if(h(t)&&"number"!=typeof t)return E.throwError.call(this,"x must be a number",o);if(h(e)&&"number"!=typeof e)return E.throwError.call(this,"y must be a number",o);if(h(r)&&"number"!=typeof r)return E.throwError.call(this,"w must be a number",o);if(h(n)&&"number"!=typeof n)return E.throwError.call(this,"h must be a number",o)}var a=(i.length-1)/2;t=h(t)?t:a,e=h(e)?e:a,r=h(r)?r:this.bitmap.width-t,n=h(n)?n:this.bitmap.height-e;var s=this.cloneQuiet();return this.scanQuiet(t,e,r,n,function(t,e,r){var n=l(s,i,t,e);this.bitmap.data[r]=this.constructor.limit255(n[0]),this.bitmap.data[r+1]=this.constructor.limit255(n[1]),this.bitmap.data[r+2]=this.constructor.limit255(n[2])}),(0,E.isNodePattern)(o)&&o.call(this,null,this),this},color:r,colour:r}},o.exports=a.default}).call(this,i("buffer").Buffer)},{"@jimp/utils":303,buffer:16,"core-js/modules/es6.array.for-each":118,"core-js/modules/es6.array.from":119,"core-js/modules/es6.array.is-array":121,"core-js/modules/es6.array.map":123,"core-js/modules/es6.date.to-string":124,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.regexp.to-string":138,"core-js/modules/es6.string.iterator":140,"core-js/modules/es6.symbol":141,"core-js/modules/es7.symbol.async-iterator":148,"core-js/modules/web.dom.iterable":149,tinycolor2:250}],277:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var h=t("@jimp/utils");r.default=function(){return{contain:function(t,e,r,n,i){if("number"!=typeof t||"number"!=typeof e)return h.throwError.call(this,"w and h must be numbers",i);"string"==typeof r&&("function"==typeof n&&void 0===i&&(i=n),n=r,r=null),"function"==typeof r&&(void 0===i&&(i=r),r=n=null),"function"==typeof n&&void 0===i&&(i=n,n=null);var o=7&(r=r||this.constructor.HORIZONTAL_ALIGN_CENTER|this.constructor.VERTICAL_ALIGN_MIDDLE),a=r>>3;if((0===o||o&o-1)&&(0===a||a&a-1))return h.throwError.call(this,"only use one flag per alignment direction",i);var s=o>>1,u=a>>1,f=t/e>this.bitmap.width/this.bitmap.height?e/this.bitmap.height:t/this.bitmap.width,l=this.cloneQuiet().scale(f,n);return this.resize(t,e,n),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data.writeUInt32BE(this._background,r)}),this.blit(l,(this.bitmap.width-l.bitmap.width)/2*s,(this.bitmap.height-l.bitmap.height)/2*u),(0,h.isNodePattern)(i)&&i.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],278:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l=t("@jimp/utils");r.default=function(){return{cover:function(t,e,r,n,i){if("number"!=typeof t||"number"!=typeof e)return l.throwError.call(this,"w and h must be numbers",i);r&&"function"==typeof r&&void 0===i?(i=r,n=r=null):"function"==typeof n&&void 0===i&&(i=n,n=null);var o=7&(r=r||this.constructor.HORIZONTAL_ALIGN_CENTER|this.constructor.VERTICAL_ALIGN_MIDDLE),a=r>>3;if((0===o||o&o-1)&&(0===a||a&a-1))return l.throwError.call(this,"only use one flag per alignment direction",i);var s=o>>1,u=a>>1,f=t/e>this.bitmap.width/this.bitmap.height?t/this.bitmap.width:e/this.bitmap.height;return this.scale(f,n),this.crop((this.bitmap.width-t)/2*s,(this.bitmap.height-e)/2*u,t,e),(0,l.isNodePattern)(i)&&i.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],279:[function(t,e,r){(function(f){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(t){return t("crop",function(t,e,r,n,i){if("number"!=typeof t||"number"!=typeof e)return B.throwError.call(this,"x and y must be numbers",i);if("number"!=typeof r||"number"!=typeof n)return B.throwError.call(this,"w and h must be numbers",i);if(t=Math.round(t),e=Math.round(e),r=Math.round(r),n=Math.round(n),0===t&&r===this.bitmap.width){var o=r*e+t<<2,a=o+n*r<<3;this.bitmap.data=this.bitmap.data.slice(o,a)}else{var s=f.allocUnsafe(r*n*4),u=0;this.scanQuiet(t,e,r,n,function(t,e,r){var n=this.bitmap.data.readUInt32BE(r,!0);s.writeUInt32BE(n,u,!0),u+=4}),this.bitmap.data=s}return this.bitmap.width=r,this.bitmap.height=n,(0,B.isNodePattern)(i)&&i.call(this,null,this),this}),{class:{autocrop:function(){for(var t,e=this.bitmap.width,r=this.bitmap.height,n=0,i=2e-4,o=!0,a=!1,s=0,u=arguments.length;s<u;s++)if("number"==typeof(s<0||arguments.length<=s?void 0:arguments[s])&&(i=s<0||arguments.length<=s?void 0:arguments[s]),"boolean"==typeof(s<0||arguments.length<=s?void 0:arguments[s])&&(o=s<0||arguments.length<=s?void 0:arguments[s]),"function"==typeof(s<0||arguments.length<=s?void 0:arguments[s])&&(t=s<0||arguments.length<=s?void 0:arguments[s]),"object"===N(s<0||arguments.length<=s?void 0:arguments[s])){var f=s<0||arguments.length<=s?void 0:arguments[s];void 0!==f.tolerance&&(i=f.tolerance),void 0!==f.cropOnlyFrames&&(o=f.cropOnlyFrames),void 0!==f.cropSymmetric&&(a=f.cropSymmetric),void 0!==f.leaveBorder&&(n=f.leaveBorder)}var l=this.getPixelColor(0,0),h=this.constructor.intToRGBA(l),c=0,d=0,p=0,m=0;l=this.getPixelColor(0,0);t:for(var b=0;b<r-1;b++){for(var g=0;g<e;g++){var _=this.getPixelColor(g,b),y=this.constructor.intToRGBA(_);if(this.constructor.colorDiff(h,y)>i)break t}c++}l=this.getPixelColor(e,0);t:for(var v=0;v<e-1;v++){for(var w=0+c;w<r;w++){var E=this.getPixelColor(v,w),x=this.constructor.intToRGBA(E);if(this.constructor.colorDiff(h,x)>i)break t}d++}l=this.getPixelColor(0,r);t:for(var j=r-1;c+1<=j;j--){for(var k=e-d-1;0<=k;k--){var S=this.getPixelColor(k,j),I=this.constructor.intToRGBA(S);if(this.constructor.colorDiff(h,I)>i)break t}p++}l=this.getPixelColor(e,r);t:for(var A=e-1;0+d+1<=A;A--){for(var T=r-1;0+c<=T;T--){var O=this.getPixelColor(A,T),P=this.constructor.intToRGBA(O);if(this.constructor.colorDiff(h,P)>i)break t}m++}if(m-=n,d-=n,c-=n,p-=n,a){var M=Math.min(d,m),L=Math.min(c,p);d=m=M,p=c=L}var C=e-(m+d),R=r-(p+c);return m=0<=m?m:0,d=0<=d?d:0,c=0<=c?c:0,p=0<=p?p:0,(o?0!==d&&0!==c&&0!==m&&0!==p:0!==d||0!==c||0!==m||0!==p)&&this.crop(d,c,C,R),(0,B.isNodePattern)(t)&&t.call(this,null,this),this}}}},t("core-js/modules/es7.symbol.async-iterator"),t("core-js/modules/es6.symbol");var B=t("@jimp/utils");function N(t){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.exports=r.default}).call(this,t("buffer").Buffer)},{"@jimp/utils":303,buffer:16,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.symbol":141,"core-js/modules/es7.symbol.async-iterator":148}],280:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es7.symbol.async-iterator"),t("core-js/modules/es6.symbol");var n=t("@jimp/utils");function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.default=function(){return{displace:function(o,a,t){if("object"!==i(o)||o.constructor!==this.constructor)return n.throwError.call(this,"The source must be a Jimp image",t);if("number"!=typeof a)return n.throwError.call(this,"factor must be a number",t);var s=this.cloneQuiet();return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=o.bitmap.data[r]/256*a;n=Math.round(n);var i=this.getPixelIndex(t+n,e);this.bitmap.data[i]=s.bitmap.data[r],this.bitmap.data[i+1]=s.bitmap.data[r+1],this.bitmap.data[i+2]=s.bitmap.data[r+2]}),(0,n.isNodePattern)(t)&&t.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.symbol":141,"core-js/modules/es7.symbol.async-iterator":148}],281:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=t("@jimp/utils");function i(t){var i=[1,9,3,11,13,5,15,7,4,12,2,10,16,8,14,6];return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=i[((3&e)<<2)+t%4];this.bitmap.data[r]=Math.min(this.bitmap.data[r]+n,255),this.bitmap.data[r+1]=Math.min(this.bitmap.data[r+1]+n,255),this.bitmap.data[r+2]=Math.min(this.bitmap.data[r+2]+n,255)}),(0,n.isNodePattern)(t)&&t.call(this,null,this),this}r.default=function(){return{dither565:i,dither16:i}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],282:[function(n,i,o){(function(e){"use strict";n("core-js/modules/es6.object.define-property"),Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var r=n("@jimp/utils");function t(s,u,t){if("boolean"!=typeof s||"boolean"!=typeof u)return r.throwError.call(this,"horizontal and vertical must be Booleans",t);if(s&&u)return this.rotate(180,!0,t);var f=e.alloc(this.bitmap.data.length);return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=s?this.bitmap.width-1-t:t,i=u?this.bitmap.height-1-e:e,o=this.bitmap.width*i+n<<2,a=this.bitmap.data.readUInt32BE(r);f.writeUInt32BE(a,o)}),this.bitmap.data=e.from(f),(0,r.isNodePattern)(t)&&t.call(this,null,this),this}o.default=function(){return{flip:t,mirror:t}},i.exports=o.default}).call(this,n("buffer").Buffer)},{"@jimp/utils":303,buffer:16,"core-js/modules/es6.object.define-property":129}],283:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var j=t("@jimp/utils");r.default=function(){return{gaussian:function(t,e){if("number"!=typeof t)return j.throwError.call(this,"r must be a number",e);if(t<1)return j.throwError.call(this,"r must be greater than 0",e);for(var r=Math.ceil(2.57*t),n=2*r+1,i=t*t*2,o=i*Math.PI,a=[],s=0;s<n;s++){a[s]=[];for(var u=0;u<n;u++){var f=Math.pow(u-r,2)+Math.pow(s-r,2);a[s][u]=Math.exp(-f/i)/o}}for(var l=0;l<this.bitmap.height;l++)for(var h=0;h<this.bitmap.width;h++)for(var c=0,d=0,p=0,m=0,b=0,g=0;g<n;g++){for(var _=0;_<n;_++){var y=Math.min(this.bitmap.width-1,Math.max(0,_+h-r)),v=Math.min(this.bitmap.height-1,Math.max(0,g+l-r)),w=a[g][_],E=v*this.bitmap.width+y<<2;c+=this.bitmap.data[E]*w,d+=this.bitmap.data[E+1]*w,p+=this.bitmap.data[E+2]*w,m+=this.bitmap.data[E+3]*w,b+=w}var x=l*this.bitmap.width+h<<2;this.bitmap.data[x]=Math.round(c/b),this.bitmap.data[x+1]=Math.round(d/b),this.bitmap.data[x+2]=Math.round(p/b),this.bitmap.data[x+3]=Math.round(m/b)}return(0,j.isNodePattern)(e)&&e.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],284:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=t("@jimp/utils");r.default=function(){return{invert:function(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data[r]=255-this.bitmap.data[r],this.bitmap.data[r+1]=255-this.bitmap.data[r+1],this.bitmap.data[r+2]=255-this.bitmap.data[r+2]}),(0,n.isNodePattern)(t)&&t.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],285:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=t("@jimp/utils");r.default=function(){return{mask:function(t){var u=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,f=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0,e=3<arguments.length?arguments[3]:void 0;if(!(t instanceof this.constructor))return n.throwError.call(this,"The source must be a Jimp image",e);if("number"!=typeof u||"number"!=typeof f)return n.throwError.call(this,"x and y must be numbers",e);u=Math.round(u),f=Math.round(f);var l=this.bitmap.width,h=this.bitmap.height,c=this;return t.scanQuiet(0,0,t.bitmap.width,t.bitmap.height,function(t,e,r){var n=u+t,i=f+e;if(0<=n&&0<=i&&n<l&&i<h){var o=c.getPixelIndex(n,i),a=this.bitmap.data,s=(a[r+0]+a[r+1]+a[r+2])/3;c.bitmap.data[o+3]*=s/255}}),(0,n.isNodePattern)(e)&&e.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],286:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es6.array.find-index"),t("core-js/modules/es6.array.fill");var n=t("@jimp/utils");var s=function(t,e,r){return 255*(t-e)/(r-e)},i=function(t){return[t.findIndex(function(t){return 0<t}),255-t.slice().reverse().findIndex(function(t){return 0<t})]};r.default=function(){return{normalize:function(t){var e=function(){var n={r:new Array(256).fill(0),g:new Array(256).fill(0),b:new Array(256).fill(0)};return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){n.r[this.bitmap.data[r+0]]++,n.g[this.bitmap.data[r+1]]++,n.b[this.bitmap.data[r+2]]++}),n}.call(this),a={r:i(e.r),g:i(e.g),b:i(e.b)};return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){var n=this.bitmap.data[r+0],i=this.bitmap.data[r+1],o=this.bitmap.data[r+2];this.bitmap.data[r+0]=s(n,a.r[0],a.r[1]),this.bitmap.data[r+1]=s(i,a.g[0],a.g[1]),this.bitmap.data[r+2]=s(o,a.b[0],a.b[1])}),(0,n.isNodePattern)(t)&&t.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.array.fill":114,"core-js/modules/es6.array.find-index":116,"core-js/modules/es6.object.define-property":129}],287:[function(i,o,a){(function(t){"use strict";i("core-js/modules/es6.object.define-property"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,i("core-js/modules/es6.array.from"),i("core-js/modules/es7.symbol.async-iterator"),i("core-js/modules/es6.symbol"),i("core-js/modules/es6.array.is-array"),i("core-js/modules/es7.object.entries"),i("core-js/modules/es6.regexp.to-string"),i("core-js/modules/es6.date.to-string"),i("core-js/modules/es6.promise"),i("core-js/modules/es6.array.iterator"),i("core-js/modules/es6.string.iterator"),i("core-js/modules/es6.array.map"),i("core-js/modules/web.dom.iterable"),i("core-js/modules/es6.array.for-each"),i("core-js/modules/es6.regexp.split");var p=r(i("path")),e=r(i("load-bmfont")),y=i("@jimp/utils"),v=i("./measure-text");function r(t){return t&&t.__esModule?t:{default:t}}function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function E(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function x(t,e,r,n,i){if(0<i.width&&0<i.height){var o=e.pages[i.page];t.blit(o,r+i.xoffset,n+i.yoffset,i.x,i.y,i.width,i.height)}return t}var n="browser/lib/";a.default=function(){return{constants:{measureText:v.measureText,measureTextHeight:v.measureTextHeight,FONT_SANS_8_BLACK:p.default.join(n,"fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt"),FONT_SANS_10_BLACK:p.default.join(n,"fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt"),FONT_SANS_12_BLACK:p.default.join(n,"fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt"),FONT_SANS_14_BLACK:p.default.join(n,"fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt"),FONT_SANS_16_BLACK:p.default.join(n,"fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt"),FONT_SANS_32_BLACK:p.default.join(n,"fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt"),FONT_SANS_64_BLACK:p.default.join(n,"fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt"),FONT_SANS_128_BLACK:p.default.join(n,"fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt"),FONT_SANS_8_WHITE:p.default.join(n,"fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt"),FONT_SANS_16_WHITE:p.default.join(n,"fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt"),FONT_SANS_32_WHITE:p.default.join(n,"fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt"),FONT_SANS_64_WHITE:p.default.join(n,"fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt"),FONT_SANS_128_WHITE:p.default.join(n,"fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt"),loadFont:function(h,c){var d=this;return"string"!=typeof h?y.throwError.call(this,"file must be a string",c):new Promise(function(r,n){c=c||function(t,e){t?n(t):r(e)},(0,e.default)(h,function(t,e){var r,n,i,o,a={},s={};if(t)return y.throwError.call(d,t,c);for(var u=0;u<e.chars.length;u++)a[String.fromCharCode(e.chars[u].id)]=e.chars[u];for(var f=0;f<e.kernings.length;f++){var l=String.fromCharCode(e.kernings[f].first);s[l]=s[l]||{},s[l][String.fromCharCode(e.kernings[f].second)]=e.kernings[f].amount}(r=d,n=p.default.dirname(h),i=e.pages,o=i.map(function(t){return r.read(n+"/"+t)}),Promise.all(o)).then(function(t){c(null,{chars:a,kernings:s,pages:t,common:e.common,info:e.info})})})})}},class:{print:function(u,f,l,t,h,e,r){var c,n,d=this;if("function"==typeof h&&void 0===r&&(r=h,h=1/0),void 0===h&&(h=1/0),"function"==typeof e&&void 0===r&&(r=e,e=1/0),void 0===e&&(e=1/0),"object"!==w(u))return y.throwError.call(this,"font must be a Jimp loadFont",r);if("number"!=typeof f||"number"!=typeof l||"number"!=typeof h)return y.throwError.call(this,"x, y and maxWidth must be numbers",r);if("number"!=typeof h)return y.throwError.call(this,"maxWidth must be a number",r);if("number"!=typeof e)return y.throwError.call(this,"maxHeight must be a number",r);t="object"===w(t)&&null!==t.text&&void 0!==t.text?(c=t.alignmentX||this.constructor.HORIZONTAL_ALIGN_LEFT,n=t.alignmentY||this.constructor.VERTICAL_ALIGN_TOP,t.text):(c=this.constructor.HORIZONTAL_ALIGN_LEFT,n=this.constructor.VERTICAL_ALIGN_TOP,t.toString()),e!==1/0&&n===this.constructor.VERTICAL_ALIGN_BOTTOM?l+=e-(0,v.measureTextHeight)(u,t,h):e!==1/0&&n===this.constructor.VERTICAL_ALIGN_MIDDLE&&(l+=e/2-(0,v.measureTextHeight)(u,t,h)/2);var i,o,a,s,p,m,b=Object.entries(u.chars)[0][1].xadvance,g=(i=u,o=h,a=t.split(" "),s=[],p=[],m=0,a.forEach(function(t){var e=E(p).concat([t]).join(" "),r=(0,v.measureText)(i,e);r<=o?(m<r&&(m=r),p.push(t)):(s.push(p),p=[t])}),s.push(p),{lines:s,longestLine:m}),_=g.longestLine;return g.lines.forEach(function(t){var e,r,n,i,o,a=t.join(" "),s=(e=d.constructor,r=u,n=a,i=h,(o=c)===e.HORIZONTAL_ALIGN_LEFT?0:o===e.HORIZONTAL_ALIGN_CENTER?(i-(0,v.measureText)(r,n))/2:i-(0,v.measureText)(r,n));(function(t,e,r,n,i){for(var o=0;o<n.length;o++){var a=void 0;a=t.chars[n[o]]?n[o]:/\s/.test(n[o])?"":"?";var s=t.chars[a]||{},u=t.kernings[a];x(this,t,e,r,s||{}),e+=(u&&u[n[o+1]]?u[n[o+1]]:0)+(s.xadvance||i)}}).call(d,u,f+s,l,a,b),l+=u.common.lineHeight}),(0,y.isNodePattern)(r)&&r.call(this,null,this,{x:f+_,y:l}),this}}}},o.exports=a.default}).call(this,"/../../node_modules/@jimp/plugin-print/dist")},{"./measure-text":288,"@jimp/utils":303,"core-js/modules/es6.array.for-each":118,"core-js/modules/es6.array.from":119,"core-js/modules/es6.array.is-array":121,"core-js/modules/es6.array.iterator":122,"core-js/modules/es6.array.map":123,"core-js/modules/es6.date.to-string":124,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.promise":132,"core-js/modules/es6.regexp.split":137,"core-js/modules/es6.regexp.to-string":138,"core-js/modules/es6.string.iterator":140,"core-js/modules/es6.symbol":141,"core-js/modules/es7.object.entries":147,"core-js/modules/es7.symbol.async-iterator":148,"core-js/modules/web.dom.iterable":149,"load-bmfont":289,path:197}],288:[function(t,e,r){"use strict";function f(t,e){for(var r=0,n=0;n<e.length;n++)if(t.chars[e[n]]){var i=t.kernings[e[n]]&&t.kernings[e[n]][e[n+1]]?t.kernings[e[n]][e[n+1]]:0;r+=(t.chars[e[n]].xadvance||0)+i}return r}t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.measureText=f,r.measureTextHeight=function(t,e,r){for(var n=e.split(" "),i="",o=t.common.lineHeight,a=0;a<n.length;a++){var s=i+n[a]+" ",u=f(t,s);i=r<u&&0<a?(o+=t.common.lineHeight,n[a]+" "):s}return o},t("core-js/modules/es6.regexp.split")},{"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.regexp.split":137}],289:[function(e,i,t){(function(l){var t=e("xhr"),h=function(){},c=e("parse-bmfont-ascii"),d=e("parse-bmfont-xml"),p=e("parse-bmfont-binary"),m=e("./lib/is-binary"),r=e("xtend"),n=self.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest;i.exports=function(u,f){f="function"==typeof f?f:h,"string"==typeof u?u={uri:u}:u||(u={}),u.binary&&(u=function(t){if(n)return r(t,{responseType:"arraybuffer"});if(void 0===self.XMLHttpRequest)throw new Error("your browser does not support XHR loading");var e=new self.XMLHttpRequest;return e.overrideMimeType("text/plain; charset=x-user-defined"),r({xhr:e},t)}(u)),t(u,function(t,e,r){if(t)return f(t);if(!/^2/.test(e.statusCode))return f(new Error("http status code: "+e.statusCode));if(!r)return f(new Error("no body result"));var n,i,o=!1;if(n=r,"[object ArrayBuffer]"===Object.prototype.toString.call(n)){var a=new Uint8Array(r);r=new l(a,"binary")}m(r)&&(o=!0,"string"==typeof r&&(r=new l(r,"binary"))),o||(l.isBuffer(r)&&(r=r.toString(u.encoding)),r=r.trim());try{var s=e.headers["content-type"];i=o?p(r):/json/.test(s)||"{"===r.charAt(0)?JSON.parse(r):/xml/.test(s)||"<"===r.charAt(0)?d(r):c(r)}catch(t){f(new Error("error parsing font "+t.message)),f=h}f(null,i)})}}).call(this,e("buffer").Buffer)},{"./lib/is-binary":290,buffer:16,"parse-bmfont-ascii":192,"parse-bmfont-binary":193,"parse-bmfont-xml":194,xhr:259,xtend:261}],290:[function(n,i,t){(function(t){var e=n("buffer-equal"),r=new t([66,77,70,3]);i.exports=function(t){return"string"==typeof t?"BMF"===t.substring(0,3):4<t.length&&e(t.slice(0,4),r)}}).call(this,n("buffer").Buffer)},{buffer:16,"buffer-equal":15}],291:[function(e,r,n){(function(s){"use strict";e("core-js/modules/es6.object.define-property"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=e("@jimp/utils"),f=t(e("./modules/resize")),l=t(e("./modules/resize2"));function t(t){return t&&t.__esModule?t:{default:t}}n.default=function(){return{constants:{RESIZE_NEAREST_NEIGHBOR:"nearestNeighbor",RESIZE_BILINEAR:"bilinearInterpolation",RESIZE_BICUBIC:"bicubicInterpolation",RESIZE_HERMITE:"hermiteInterpolation",RESIZE_BEZIER:"bezierInterpolation"},class:{resize:function(e,r,t,n){if("number"!=typeof e||"number"!=typeof r)return u.throwError.call(this,"w and h must be numbers",n);if("function"==typeof t&&void 0===n&&(n=t,t=null),e===this.constructor.AUTO&&r===this.constructor.AUTO)return u.throwError.call(this,"w and h cannot both be set to auto",n);if(e===this.constructor.AUTO&&(e=this.bitmap.width*(r/this.bitmap.height)),r===this.constructor.AUTO&&(r=this.bitmap.height*(e/this.bitmap.width)),e<0||r<0)return u.throwError.call(this,"w and h must be positive numbers",n);if(e=Math.round(e),r=Math.round(r),"function"==typeof l.default[t]){var i={data:s.alloc(e*r*4),width:e,height:r};l.default[t](this.bitmap,i),this.bitmap=i}else{var o=this,a=new f.default(this.bitmap.width,this.bitmap.height,e,r,!0,!0,function(t){o.bitmap.data=s.from(t),o.bitmap.width=e,o.bitmap.height=r});a.resize(this.bitmap.data)}return(0,u.isNodePattern)(n)&&n.call(this,null,this),this}}}},r.exports=n.default}).call(this,e("buffer").Buffer)},{"./modules/resize":292,"./modules/resize2":293,"@jimp/utils":303,buffer:16,"core-js/modules/es6.object.define-property":129}],292:[function(t,e,r){"use strict";function n(t,e,r,n,i,o,a){this.widthOriginal=Math.abs(Math.floor(t)||0),this.heightOriginal=Math.abs(Math.floor(e)||0),this.targetWidth=Math.abs(Math.floor(r)||0),this.targetHeight=Math.abs(Math.floor(n)||0),this.colorChannels=i?4:3,this.interpolationPass=Boolean(o),this.resizeCallback="function"==typeof a?a:function(){},this.targetWidthMultipliedByChannels=this.targetWidth*this.colorChannels,this.originalWidthMultipliedByChannels=this.widthOriginal*this.colorChannels,this.originalHeightMultipliedByChannels=this.heightOriginal*this.colorChannels,this.widthPassResultSize=this.targetWidthMultipliedByChannels*this.heightOriginal,this.finalResultSize=this.targetWidthMultipliedByChannels*this.targetHeight,this.initialize()}t("core-js/modules/es6.typed.uint8-array"),t("core-js/modules/es6.typed.float64-array"),t("core-js/modules/es6.typed.float32-array"),n.prototype.initialize=function(){if(!(0<this.widthOriginal&&0<this.heightOriginal&&0<this.targetWidth&&0<this.targetHeight))throw new Error("Invalid settings specified for the resizer.");this.configurePasses()},n.prototype.configurePasses=function(){this.widthOriginal===this.targetWidth?this.resizeWidth=this.bypassResizer:(this.ratioWeightWidthPass=this.widthOriginal/this.targetWidth,this.ratioWeightWidthPass<1&&this.interpolationPass?(this.initializeFirstPassBuffers(!0),this.resizeWidth=4===this.colorChannels?this.resizeWidthInterpolatedRGBA:this.resizeWidthInterpolatedRGB):(this.initializeFirstPassBuffers(!1),this.resizeWidth=4===this.colorChannels?this.resizeWidthRGBA:this.resizeWidthRGB)),this.heightOriginal===this.targetHeight?this.resizeHeight=this.bypassResizer:(this.ratioWeightHeightPass=this.heightOriginal/this.targetHeight,this.ratioWeightHeightPass<1&&this.interpolationPass?(this.initializeSecondPassBuffers(!0),this.resizeHeight=this.resizeHeightInterpolated):(this.initializeSecondPassBuffers(!1),this.resizeHeight=4===this.colorChannels?this.resizeHeightRGBA:this.resizeHeightRGB))},n.prototype._resizeWidthInterpolatedRGBChannels=function(t,e){var r,n,i=e?4:3,o=this.ratioWeightWidthPass,a=this.widthBuffer,s=0,u=0,f=0,l=0,h=0;for(r=0;s<1/3;r+=i,s+=o)for(u=r,f=0;u<this.widthPassResultSize;f+=this.originalWidthMultipliedByChannels,u+=this.targetWidthMultipliedByChannels)a[u]=t[f],a[u+1]=t[f+1],a[u+2]=t[f+2],e&&(a[u+3]=t[f+3]);for(s-=1/3,n=this.widthOriginal-1;s<n;r+=i,s+=o)for(l=1-(h=s%1),u=r,f=Math.floor(s)*i;u<this.widthPassResultSize;f+=this.originalWidthMultipliedByChannels,u+=this.targetWidthMultipliedByChannels)a[u+0]=t[f+0]*l+t[f+i+0]*h,a[u+1]=t[f+1]*l+t[f+i+1]*h,a[u+2]=t[f+2]*l+t[f+i+2]*h,e&&(a[u+3]=t[f+3]*l+t[f+i+3]*h);for(n=this.originalWidthMultipliedByChannels-i;r<this.targetWidthMultipliedByChannels;r+=i)for(u=r,f=n;u<this.widthPassResultSize;f+=this.originalWidthMultipliedByChannels,u+=this.targetWidthMultipliedByChannels)a[u]=t[f],a[u+1]=t[f+1],a[u+2]=t[f+2],e&&(a[u+3]=t[f+3]);return a},n.prototype._resizeWidthRGBChannels=function(t,e){var r=e?4:3,n=this.ratioWeightWidthPass,i=1/n,o=this.originalWidthMultipliedByChannels-r+1,a=this.targetWidthMultipliedByChannels-r+1,s=this.outputWidthWorkBench,u=this.widthBuffer,f=this.outputWidthWorkBenchOpaquePixelsCount,l=0,h=0,c=0,d=0,p=0,m=0,b=0,g=1,_=0,y=0,v=0,w=0;do{for(p=0;p<this.originalHeightMultipliedByChannels;)s[p++]=0,s[p++]=0,s[p++]=0,e&&(s[p++]=0,f[p/r-1]=0);l=n;do{for(h=1+c-d,g=Math.min(l,h),p=0,m=c;p<this.originalHeightMultipliedByChannels;m+=o)_=t[m],y=t[++m],v=t[++m],w=e?t[++m]:255,s[p++]+=(w?_:0)*g,s[p++]+=(w?y:0)*g,s[p++]+=(w?v:0)*g,e&&(s[p++]+=w*g,f[p/r-1]+=w?g:0);if(!(h<=l)){d+=l;break}d=c+=r,l-=h}while(0<l&&c<this.originalWidthMultipliedByChannels);for(p=0,m=b;p<this.originalHeightMultipliedByChannels;m+=a)l=e?f[p/r]:1,g=e?l?1/l:0:i,u[m]=s[p++]*g,u[++m]=s[p++]*g,u[++m]=s[p++]*g,e&&(u[++m]=s[p++]*i);b+=r}while(b<this.targetWidthMultipliedByChannels);return u},n.prototype._resizeHeightRGBChannels=function(t,e){var r=this.ratioWeightHeightPass,n=1/r,i=this.outputHeightWorkBench,o=this.heightBuffer,a=this.outputHeightWorkBenchOpaquePixelsCount,s=0,u=0,f=0,l=0,h=0,c=0,d=0,p=1,m=0,b=0,g=0,_=0;do{for(h=0;h<this.targetWidthMultipliedByChannels;)i[h++]=0,i[h++]=0,i[h++]=0,e&&(i[h++]=0,a[h/4-1]=0);s=r;do{for(u=1+f-l,p=Math.min(s,u),d=f,h=0;h<this.targetWidthMultipliedByChannels;)m=t[d++],b=t[d++],g=t[d++],_=e?t[d++]:255,i[h++]+=(_?m:0)*p,i[h++]+=(_?b:0)*p,i[h++]+=(_?g:0)*p,e&&(i[h++]+=_*p,a[h/4-1]+=_?p:0);if(!(u<=s)){l+=s;break}l=f=d,s-=u}while(0<s&&f<this.widthPassResultSize);for(h=0;h<this.targetWidthMultipliedByChannels;)s=e?a[h/4]:1,p=e?s?1/s:0:n,o[c++]=Math.round(i[h++]*p),o[c++]=Math.round(i[h++]*p),o[c++]=Math.round(i[h++]*p),e&&(o[c++]=Math.round(i[h++]*n))}while(c<this.finalResultSize);return o},n.prototype.resizeWidthInterpolatedRGB=function(t){return this._resizeWidthInterpolatedRGBChannels(t,!1)},n.prototype.resizeWidthInterpolatedRGBA=function(t){return this._resizeWidthInterpolatedRGBChannels(t,!0)},n.prototype.resizeWidthRGB=function(t){return this._resizeWidthRGBChannels(t,!1)},n.prototype.resizeWidthRGBA=function(t){return this._resizeWidthRGBChannels(t,!0)},n.prototype.resizeHeightInterpolated=function(t){for(var e,r=this.ratioWeightHeightPass,n=this.heightBuffer,i=0,o=0,a=0,s=0,u=0,f=0,l=0;i<1/3;i+=r)for(a=0;a<this.targetWidthMultipliedByChannels;)n[o++]=Math.round(t[a++]);for(i-=1/3,e=this.heightOriginal-1;i<e;i+=r)for(f=1-(l=i%1),u=(s=Math.floor(i)*this.targetWidthMultipliedByChannels)+this.targetWidthMultipliedByChannels,a=0;a<this.targetWidthMultipliedByChannels;++a)n[o++]=Math.round(t[s++]*f+t[u++]*l);for(;o<this.finalResultSize;)for(a=0,s=e*this.targetWidthMultipliedByChannels;a<this.targetWidthMultipliedByChannels;++a)n[o++]=Math.round(t[s++]);return n},n.prototype.resizeHeightRGB=function(t){return this._resizeHeightRGBChannels(t,!1)},n.prototype.resizeHeightRGBA=function(t){return this._resizeHeightRGBChannels(t,!0)},n.prototype.resize=function(t){this.resizeCallback(this.resizeHeight(this.resizeWidth(t)))},n.prototype.bypassResizer=function(t){return t},n.prototype.initializeFirstPassBuffers=function(t){this.widthBuffer=this.generateFloatBuffer(this.widthPassResultSize),t||(this.outputWidthWorkBench=this.generateFloatBuffer(this.originalHeightMultipliedByChannels),3<this.colorChannels&&(this.outputWidthWorkBenchOpaquePixelsCount=this.generateFloat64Buffer(this.heightOriginal)))},n.prototype.initializeSecondPassBuffers=function(t){this.heightBuffer=this.generateUint8Buffer(this.finalResultSize),t||(this.outputHeightWorkBench=this.generateFloatBuffer(this.targetWidthMultipliedByChannels),3<this.colorChannels&&(this.outputHeightWorkBenchOpaquePixelsCount=this.generateFloat64Buffer(this.targetWidth)))},n.prototype.generateFloatBuffer=function(t){try{return new Float32Array(t)}catch(t){return[]}},n.prototype.generateFloat64Buffer=function(t){try{return new Float64Array(t)}catch(t){return[]}},n.prototype.generateUint8Buffer=function(t){try{return new Uint8Array(t)}catch(t){return[]}},e.exports=n},{"core-js/modules/es6.typed.float32-array":142,"core-js/modules/es6.typed.float64-array":143,"core-js/modules/es6.typed.uint8-array":144}],293:[function(t,e,r){(function(rt){"use strict";e.exports={nearestNeighbor:function(t,e){for(var r=t.width,n=t.height,i=e.width,o=e.height,a=t.data,s=e.data,u=0;u<o;u++)for(var f=0;f<i;f++){var l=4*(u*i+f),h=4*(Math.floor(u*n/o)*r+Math.floor(f*r/i));s[l++]=a[h++],s[l++]=a[h++],s[l++]=a[h++],s[l++]=a[h++]}},bilinearInterpolation:function(t,e){for(var c=t.width,r=t.height,n=e.width,i=e.height,d=t.data,p=e.data,m=function(t,e,r,n,i){return e===n?r:Math.round((t-e)*i+(n-t)*r)},o=function(t,e,r,n,i,o,a,s){var u=4*(a*c+n)+e,f=4*(a*c+i)+e,l=m(r,n,d[u],i,d[f]);if(s===a)p[t+e]=l;else{f=4*(s*c+i)+e;var h=m(r,n,d[u=4*(s*c+n)+e],i,d[f]);p[t+e]=m(o,a,l,s,h)}},a=0;a<i;a++)for(var s=0;s<n;s++){var u=4*(a*n+s),f=s*c/n,l=Math.floor(f),h=Math.min(Math.ceil(f),c-1),b=a*r/i,g=Math.floor(b),_=Math.min(Math.ceil(b),r-1);o(u,0,f,l,h,b,g,_),o(u,1,f,l,h,b,g,_),o(u,2,f,l,h,b,g,_),o(u,3,f,l,h,b,g,_)}},_interpolate2D:function(t,e,r,n){for(var i=t.data,o=e.data,a=t.width,s=t.height,u=e.width,f=e.height,l=Math.max(1,Math.floor(a/u)),h=u*l,c=Math.max(1,Math.floor(s/f)),d=f*c,p=rt.alloc(h*s*4),m=0;m<s;m++)for(var b=0;b<h;b++)for(var g=b*(a-1)/h,_=Math.floor(g),y=g-_,v=4*(m*a+_),w=4*(m*h+b),E=0;E<4;E++){var x=v+E,j=0<_?i[x-4]:2*i[x]-i[x+4],k=i[x],S=i[x+4],I=_<a-2?i[x+8]:2*i[x+4]-i[x];p[w+E]=n(j,k,S,I,y)}for(var A=rt.alloc(h*d*4),T=0;T<d;T++)for(var O=0;O<h;O++)for(var P=T*(s-1)/d,M=Math.floor(P),L=P-M,C=4*(M*h+O),R=4*(T*h+O),B=0;B<4;B++){var N=C+B,D=0<M?p[N-4*h]:2*p[N]-p[N+4*h],U=p[N],F=p[N+4*h],z=M<s-2?p[N+8*h]:2*p[N+4*h]-p[N];A[R+B]=n(D,U,F,z,L)}var H=l*c;if(1<H)for(var G=0;G<f;G++)for(var W=0;W<u;W++){for(var Z=0,q=0,Y=0,V=0,X=0,J=0;J<c;J++)for(var K=G*c+J,Q=0;Q<l;Q++){var $=4*(K*h+(W*l+Q)),tt=A[$+3];tt&&(Z+=A[$],q+=A[$+1],Y+=A[$+2],X++),V+=tt}var et=4*(G*u+W);o[et]=X?Math.round(Z/X):0,o[et+1]=X?Math.round(q/X):0,o[et+2]=X?Math.round(Y/X):0,o[et+3]=Math.round(V/H)}else e.data=A},bicubicInterpolation:function(t,e,r){return this._interpolate2D(t,e,r,function(t,e,r,n,i){var o=n-r-t+e,a=t-e-o,s=r-t,u=e;return Math.max(0,Math.min(255,o*(i*i*i)+a*(i*i)+s*i+u))})},hermiteInterpolation:function(t,e,r){return this._interpolate2D(t,e,r,function(t,e,r,n,i){var o=e,a=.5*(r-t),s=t-2.5*e+2*r-.5*n,u=.5*(n-t)+1.5*(e-r);return Math.max(0,Math.min(255,Math.round(((u*i+s)*i+a)*i+o)))})},bezierInterpolation:function(t,e,r){return this._interpolate2D(t,e,r,function(t,e,r,n,i){var o=1-i,a=e*o*o*o,s=3*(e+(r-t)/4)*o*o*i,u=3*(r-(n-e)/4)*o*i*i,f=r*i*i*i;return Math.max(0,Math.min(255,Math.round(a+s+u+f)))})}}}).call(this,t("buffer").Buffer)},{buffer:16}],294:[function(t,e,r){(function(j){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=t("@jimp/utils");r.default=function(){return{rotate:function(t,e,r){return null==e&&(e=!0),"function"==typeof e&&void 0===r&&(r=e,e=!0),"number"!=typeof t?n.throwError.call(this,"deg must be a number",r):"boolean"!=typeof e&&"string"!=typeof e?n.throwError.call(this,"mode must be a boolean or a string",r):(function(t,e){var r=(t%=360)*Math.PI/180,n=Math.cos(r),i=Math.sin(r),o=this.bitmap.width,a=this.bitmap.height;if(!0===e||"string"==typeof e){(o=Math.ceil(Math.abs(this.bitmap.width*n)+Math.abs(this.bitmap.height*i))+1)%2!=0&&o++,(a=Math.ceil(Math.abs(this.bitmap.width*i)+Math.abs(this.bitmap.height*n))+1)%2!=0&&a++;var s=this.cloneQuiet();this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,function(t,e,r){this.bitmap.data.writeUInt32BE(this._background,r)});var u=Math.max(o,a,this.bitmap.width,this.bitmap.height);this.resize(u,u,e),this.blit(s,this.bitmap.width/2-s.bitmap.width/2,this.bitmap.height/2-s.bitmap.height/2)}var f=this.bitmap.width,l=this.bitmap.height,h=j.alloc(this.bitmap.data.length);function c(r,n){return function(t,e){return{x:t+r,y:e+n}}}for(var d=c(-f/2,-l/2),p=c(f/2+.5,l/2+.5),m=1;m<=l;m++)for(var b=1;b<=f;b++){var g=d(b,m),_=p(n*g.x-i*g.y,n*g.y+i*g.x),y=f*(m-1)+b-1<<2;if(0<=_.x&&_.x<f&&0<=_.y&&_.y<l){var v=(f*(0|_.y)+_.x|0)<<2,w=this.bitmap.data.readUInt32BE(v);h.writeUInt32BE(w,y)}else h.writeUInt32BE(this._background,y)}if(this.bitmap.data=h,!0===e||"string"==typeof e){var E=f/2-o/2,x=l/2-a/2;this.crop(E,x,o,a)}}.call(this,t,e,r),(0,n.isNodePattern)(r)&&r.call(this,null,this),this)}}},e.exports=r.default}).call(this,t("buffer").Buffer)},{"@jimp/utils":303,buffer:16,"core-js/modules/es6.object.define-property":129}],295:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var o=t("@jimp/utils");r.default=function(){return{scale:function(t,e,r){if("number"!=typeof t)return o.throwError.call(this,"f must be a number",r);if(t<0)return o.throwError.call(this,"f must be a positive number",r);"function"==typeof e&&void 0===r&&(r=e,e=null);var n=this.bitmap.width*t,i=this.bitmap.height*t;return this.resize(n,i,e),(0,o.isNodePattern)(r)&&r.call(this,null,this),this},scaleToFit:function(t,e,r,n){if("number"!=typeof t||"number"!=typeof e)return o.throwError.call(this,"w and h must be numbers",n);"function"==typeof r&&void 0===n&&(n=r,r=null);var i=t/e>this.bitmap.width/this.bitmap.height?e/this.bitmap.height:t/this.bitmap.width;return this.scale(i,r),(0,o.isNodePattern)(n)&&n.call(this,null,this),this}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129}],296:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es6.string.iterator"),t("core-js/modules/es6.array.from"),t("core-js/modules/es6.regexp.to-string"),t("core-js/modules/es6.date.to-string"),t("core-js/modules/es7.symbol.async-iterator"),t("core-js/modules/es6.symbol"),t("core-js/modules/web.dom.iterable"),t("core-js/modules/es6.array.is-array"),t("core-js/modules/es6.array.map");var n=t("timm"),i=w(t("@jimp/plugin-dither")),o=w(t("@jimp/plugin-resize")),a=w(t("@jimp/plugin-blit")),s=w(t("@jimp/plugin-rotate")),u=w(t("@jimp/plugin-color")),f=w(t("@jimp/plugin-print")),l=w(t("@jimp/plugin-blur")),h=w(t("@jimp/plugin-crop")),c=w(t("@jimp/plugin-normalize")),d=w(t("@jimp/plugin-invert")),p=w(t("@jimp/plugin-gaussian")),m=w(t("@jimp/plugin-flip")),b=w(t("@jimp/plugin-mask")),g=w(t("@jimp/plugin-scale")),_=w(t("@jimp/plugin-displace")),y=w(t("@jimp/plugin-contain")),v=w(t("@jimp/plugin-cover"));function w(t){return t&&t.__esModule?t:{default:t}}function E(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var x=[i.default,o.default,a.default,s.default,u.default,f.default,l.default,h.default,c.default,d.default,p.default,m.default,b.default,g.default,_.default,y.default,v.default];r.default=function(r){var t=x.map(function(t){var e=t(r)||{};return e.class||e.constants||(e={class:e}),e});return n.mergeDeep.apply(void 0,E(t))},e.exports=r.default},{"@jimp/plugin-blit":273,"@jimp/plugin-blur":275,"@jimp/plugin-color":276,"@jimp/plugin-contain":277,"@jimp/plugin-cover":278,"@jimp/plugin-crop":279,"@jimp/plugin-displace":280,"@jimp/plugin-dither":281,"@jimp/plugin-flip":282,"@jimp/plugin-gaussian":283,"@jimp/plugin-invert":284,"@jimp/plugin-mask":285,"@jimp/plugin-normalize":286,"@jimp/plugin-print":287,"@jimp/plugin-resize":291,"@jimp/plugin-rotate":294,"@jimp/plugin-scale":295,"core-js/modules/es6.array.from":119,"core-js/modules/es6.array.is-array":121,"core-js/modules/es6.array.map":123,"core-js/modules/es6.date.to-string":124,"core-js/modules/es6.object.define-property":129,"core-js/modules/es6.regexp.to-string":138,"core-js/modules/es6.string.iterator":140,"core-js/modules/es6.symbol":141,"core-js/modules/es7.symbol.async-iterator":148,"core-js/modules/web.dom.iterable":149,timm:249}],297:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es6.object.define-property");var n,i=(n=t("bmp-js"))&&n.__esModule?n:{default:n},o=t("@jimp/utils");function a(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s="image/bmp",u="image/x-ms-bmp";var f=function(t){return s=i.default.decode(t),(0,o.scan)({bitmap:s},0,0,s.width,s.height,function(t,e,r){var n=this.bitmap.data[r+0],i=this.bitmap.data[r+1],o=this.bitmap.data[r+2],a=this.bitmap.data[r+3];this.bitmap.data[r+0]=a,this.bitmap.data[r+1]=o,this.bitmap.data[r+2]=i,this.bitmap.data[r+3]=s.is_with_alpha?n:255}).bitmap;var s},l=function(t){return i.default.encode((e=t,(0,o.scan)(e,0,0,e.bitmap.width,e.bitmap.height,function(t,e,r){var n=this.bitmap.data[r+0],i=this.bitmap.data[r+1],o=this.bitmap.data[r+2],a=this.bitmap.data[r+3];this.bitmap.data[r+0]=a,this.bitmap.data[r+1]=o,this.bitmap.data[r+2]=i,this.bitmap.data[r+3]=n}).bitmap)).data;var e};r.default=function(){var t,e;return{mime:a({},s,["bmp"]),constants:{MIME_BMP:s,MIME_X_MS_BMP:u},decoders:(t={},a(t,s,f),a(t,u,f),t),encoders:(e={},a(e,s,l),a(e,u,l),e)}},e.exports=r.default},{"@jimp/utils":303,"bmp-js":8,"core-js/modules/es6.object.define-property":129}],298:[function(o,a,s){(function(n){"use strict";Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0,o("core-js/modules/es6.object.define-property");var t,i=(t=o("omggif"))&&t.__esModule?t:{default:t};function e(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var r="image/gif";s.default=function(){return{mime:e({},r,["gif"]),constants:{MIME_GIF:r},decoders:e({},r,function(t){var e=new i.default.GifReader(t),r=n.alloc(e.width*e.height*4);return e.decodeAndBlitFrameRGBA(0,r),{data:r,width:e.width,height:e.height}})}},a.exports=s.default}).call(this,o("buffer").Buffer)},{buffer:16,"core-js/modules/es6.object.define-property":129,omggif:175}],299:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es6.object.define-property");var n,i=(n=t("jpeg-js"))&&n.__esModule?n:{default:n},o=t("@jimp/utils");function a(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s="image/jpeg";r.default=function(){return{mime:a({},s,["jpeg","jpg","jpe"]),constants:{MIME_JPEG:s},decoders:a({},s,i.default.decode),encoders:a({},s,function(t){return i.default.encode(t.bitmap,t._quality).data}),class:{_quality:100,quality:function(t,e){return"number"!=typeof t?o.throwError.call(this,"n must be a number",e):t<0||100<t?o.throwError.call(this,"n must be a number 0 - 100",e):(this._quality=Math.round(t),(0,o.isNodePattern)(e)&&e.call(this,null,this),this)}}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129,"jpeg-js":171}],300:[function(t,e,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,t("core-js/modules/es6.object.define-property");var n=t("pngjs"),i=t("@jimp/utils");function o(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var a="image/png";r.default=function(){return{mime:o({},a,["png"]),constants:{MIME_PNG:a,PNG_FILTER_AUTO:-1,PNG_FILTER_NONE:0,PNG_FILTER_SUB:1,PNG_FILTER_UP:2,PNG_FILTER_AVERAGE:3,PNG_FILTER_PATH:4},hasAlpha:o({},a,!0),decoders:o({},a,n.PNG.sync.read),encoders:o({},a,function(t){var e=new n.PNG({width:t.bitmap.width,height:t.bitmap.height});return e.data=t.bitmap.data,n.PNG.sync.write(e,{width:t.bitmap.width,height:t.bitmap.height,deflateLevel:t._deflateLevel,deflateStrategy:t._deflateStrategy,filterType:t._filterType,colorType:"number"==typeof t._colorType?t._colorType:t._rgba?6:2,inputHasAlpha:t._rgba})}),class:{_deflateLevel:9,_deflateStrategy:3,_filterType:-1,_colorType:null,deflateLevel:function(t,e){return"number"!=typeof t?i.throwError.call(this,"l must be a number",e):t<0||9<t?i.throwError.call(this,"l must be a number 0 - 9",e):(this._deflateLevel=Math.round(t),(0,i.isNodePattern)(e)&&e.call(this,null,this),this)},deflateStrategy:function(t,e){return"number"!=typeof t?i.throwError.call(this,"s must be a number",e):t<0||3<t?i.throwError.call(this,"s must be a number 0 - 3",e):(this._deflateStrategy=Math.round(t),(0,i.isNodePattern)(e)&&e.call(this,null,this),this)},filterType:function(t,e){return"number"!=typeof t?i.throwError.call(this,"n must be a number",e):t<-1||4<t?i.throwError.call(this,"n must be -1 (auto) or a number 0 - 4",e):(this._filterType=Math.round(t),(0,i.isNodePattern)(e)&&e.call(this,null,this),this)},colorType:function(t,e){return"number"!=typeof t?i.throwError.call(this,"s must be a number",e):0!==t&&2!==t&&4!==t&&6!==t?i.throwError.call(this,"s must be a number 0, 2, 4, 6.",e):(this._colorType=Math.round(t),(0,i.isNodePattern)(e)&&e.call(this,null,this),this)}}}},e.exports=r.default},{"@jimp/utils":303,"core-js/modules/es6.object.define-property":129,pngjs:219}],301:[function(n,a,s){(function(i){"use strict";Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0,n("core-js/modules/es6.object.define-property");var t,o=(t=n("utif"))&&t.__esModule?t:{default:t};function e(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var r="image/tiff";s.default=function(){return{mime:e({},r,["tiff","tif"]),constants:{MIME_TIFF:r},decoders:e({},r,function(t){var e=o.default.decode(t),r=e[0];o.default.decodeImages(t,e);var n=o.default.toRGBA8(r);return{data:i.from(n),width:r.t256[0],height:r.t257[0]}}),encoders:e({},r,function(t){var e=o.default.encodeImage(t.bitmap.data,t.bitmap.width,t.bitmap.height);return i.from(e)})}},a.exports=s.default}).call(this,n("buffer").Buffer)},{buffer:16,"core-js/modules/es6.object.define-property":129,utif:255}],302:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=t("timm"),i=f(t("@jimp/jpeg")),o=f(t("@jimp/png")),a=f(t("@jimp/bmp")),s=f(t("@jimp/tiff")),u=f(t("@jimp/gif"));function f(t){return t&&t.__esModule?t:{default:t}}r.default=function(){return(0,n.mergeDeep)((0,i.default)(),(0,o.default)(),(0,a.default)(),(0,s.default)(),(0,u.default)())},e.exports=r.default},{"@jimp/bmp":297,"@jimp/gif":298,"@jimp/jpeg":299,"@jimp/png":300,"@jimp/tiff":301,"core-js/modules/es6.object.define-property":129,timm:249}],303:[function(t,e,r){"use strict";t("core-js/modules/es6.object.define-property"),Object.defineProperty(r,"__esModule",{value:!0}),r.isNodePattern=function(t){if(void 0===t)return!1;if("function"==typeof t)return!0;throw new TypeError("Callback must be a function")},r.throwError=function(t,e){"string"==typeof t&&(t=new Error(t));if("function"!=typeof e)throw t;return e.call(this,t)},r.scan=function(t,e,r,n,i,o){e=Math.round(e),r=Math.round(r),n=Math.round(n),i=Math.round(i);for(var a=r;a<r+i;a++)for(var s=e;s<e+n;s++){var u=t.bitmap.width*a+s<<2;o.call(t,s,a,u)}return t}},{"core-js/modules/es6.object.define-property":129}]},{},[272])(272)});