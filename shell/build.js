'use strict'

const fs = require('fs')
const path = require('path')
const jsdom = require("jsdom")
const argv = require("argv")
const i18n = require('../src/i18n/node')

const args = argv.option([
  {
    name: 'publicPath',
    type: 'string'
  }
]).run()

const publicPath = args.options.publicPath || ''

const { JSDOM } = jsdom

console.log(`[log] 执行 build.js 包含：生成多语言模板，替换相对路径，复制 favicon`)

// dist 目录路径
const dist = path.resolve('dist')

// 检查目录
if (!fs.existsSync(dist)) {
  console.error(`[error] dist 目录不存在`)
  console.log(`[log] 多语言任务完成`)
  process.exit(0)
}

// en 文件夹名
const foldername = 'en'
// 创建 en 文件夹
if (!fs.existsSync(path.join(dist, foldername))) {
  fs.mkdirSync(path.join(dist, foldername))
}

// en 目录路径
const en = path.join(dist, foldername)
// zh 目录路径
const zh = dist

// 替换模板
function replaceTemplate (template, name, lang) {
  const sandbox = new JSDOM(template)
  // 匹配 data-i18n 替换 对应语言文案
  {
    const list = sandbox.window.document.querySelectorAll('[data-i18n]')
    list.forEach((element) => {
      const key = element.dataset.i18n
      if (!key) {
        return
      }
      const value = i18n[key]
      if (!value) {
        return
      }
      const text = value[lang]
      if (typeof text !== 'string') {
        return
      }
      element.textContent = text
    })
  }
  // 匹配 data-show 删除 不对应的标签
  {
    const list = sandbox.window.document.querySelectorAll('[data-show]')
    list.forEach((element) => {
      if (element.dataset.show !== lang) {
        element.remove()
      }
    })
  }
  // 匹配 scrip + img 的 src 替换 publicPath
  {
    const list = sandbox.window.document.querySelectorAll('script[src^="/"]:not([src^="//"]),img[src^="/"]:not([src^="//"])')
    list.forEach((element) => {
      element.setAttribute('src', `${publicPath}${element.getAttribute('src')}`)
    })
  }
  // 匹配 link + a 的 href 替换 publicPath
  {
    const list = sandbox.window.document.querySelectorAll('link[href^="/"]:not([href^="//"]),a[href^="/"]:not([href^="//"])')
    list.forEach((element) => {
      element.setAttribute('href', `${publicPath}${element.getAttribute('href')}`)
    })
  }
  // 匹配 style 替换 assets image url
  {
    const list = sandbox.window.document.querySelectorAll('style')
    list.forEach((element) => {
      element.textContent = element.textContent.replace(/\/assets\//g, `${publicPath}/assets/`)
    })
  }
  // 匹配 content 替换 publicPath js 用
  {
    const meta = sandbox.window.document.querySelector('meta[name="publicPath"]')
    meta.setAttribute('content', `${publicPath}${meta.getAttribute('content')}`)
  }
  // 添加 root 的 class
  {
    sandbox.window.document.documentElement.classList.add(lang)
  }
  return '<!DOCTYPE html>'+'\n'+sandbox.window.document.documentElement.outerHTML
}

// 遍历 dist
fs.readdirSync(dist).forEach((filename) => {
  if (path.extname(filename) === '.html') {
    // 模板路径
    const filepath = path.join(dist, filename)
    // 模板名
    const name = path.basename(filename, '.html')
    // 模板
    const template = fs.readFileSync(filepath, 'utf8')
    // 保存 en
    fs.writeFileSync(path.join(en, filename), replaceTemplate(template, name, 'en'))
    // 保存 zh
    fs.writeFileSync(path.join(zh, filename), replaceTemplate(template, name, 'zh'))
  }
})

const favname = `favicon.ico`
const favfile = path.join(path.resolve('src'), favname)

// 检查 fav icon 是否存在
if (fs.existsSync(favfile)) {
  fs.copyFileSync(favfile, path.join(dist, favname))
} else {
  console.error(`[error] fav icon 不存在`)
}

// 复制 sitemap.xml
const sitemapname = `sitemap.xml`
const sitemapfile = path.join(path.resolve('src'), sitemapname)

// 检查 sitemap.xml 是否存在
if (fs.existsSync(sitemapfile)) {
  fs.copyFileSync(sitemapfile, path.join(dist, sitemapname))
  console.log(`[log] sitemap.xml 复制完成`)
} else {
  console.error(`[error] sitemap.xml 不存在`)
}

// 复制 robots.txt
const robotsname = `robots.txt`
const robotsfile = path.join(path.resolve('src'), robotsname)

// 检查 robots.txt 是否存在
if (fs.existsSync(robotsfile)) {
  fs.copyFileSync(robotsfile, path.join(dist, robotsname))
  console.log(`[log] robots.txt 复制完成`)
} else {
  console.error(`[error] robots.txt 不存在`)
}

console.log(`[log] 执行 build.js 完成`)
