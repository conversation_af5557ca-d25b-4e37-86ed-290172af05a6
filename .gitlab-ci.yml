image: harbor.yy.com/front_end/legoflow:2.1.0

stages:
  - build

build_test:
  only:
    - /^test$/
  stage: build
  tags:
    - webfe
  artifacts:
    expire_in: 1 week
    paths:
    - dist/
    - gitlog
    - package.json
  script:
    - npm i
    - lf:ci preview
    - npm run i18n:uxtest
  after_script:
    - git log -1 --pretty=medium >> gitlog

build_production:
  only:
    - /^master$/
  stage: build
  tags:
    - webfe
  artifacts:
    expire_in: 1 week
    paths:
    - dist/
    - gitlog
    - package.json
  script:
    - npm i
    - npm run i18n:preview
    - npm run i18n:ux
  after_script:
    - git log -1 --pretty=medium >> gitlog
