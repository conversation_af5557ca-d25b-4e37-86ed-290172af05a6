// -----------------------------------------------
%index-about0 {
	width: 77px;
	height: 63px;
	background-image: url('../img/index-about0.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-about0.png {
	width: 77px;
	height: 63px;
	background-image: url('../img/index-about0.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-about3 {
	width: 67px;
	height: 67px;
	background-image: url('../img/index-about3.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-about3.png {
	width: 67px;
	height: 67px;
	background-image: url('../img/index-about3.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-about2 {
	width: 77px;
	height: 67px;
	background-image: url('../img/index-about2.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-about2.png {
	width: 77px;
	height: 67px;
	background-image: url('../img/index-about2.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-about1 {
	width: 70px;
	height: 84px;
	background-image: url('../img/index-about1.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-about1.png {
	width: 70px;
	height: 84px;
	background-image: url('../img/index-about1.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-example {
	width: 367px;
	height: 652px;
	background-image: url('../img/index-example.jpg');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-example.jpg {
	width: 367px;
	height: 652px;
	background-image: url('../img/index-example.jpg');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-pad-normal {
	width: 317px;
	height: 204px;
	background-image: url('../img/index-pad-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-pad-normal.png {
	width: 317px;
	height: 204px;
	background-image: url('../img/index-pad-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-phone-hover {
	width: 135px;
	height: 261px;
	background-image: url('../img/index-phone-hover.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-phone-hover.png {
	width: 135px;
	height: 261px;
	background-image: url('../img/index-phone-hover.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-phone-normal {
	width: 135px;
	height: 261px;
	background-image: url('../img/index-phone-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-phone-normal.png {
	width: 135px;
	height: 261px;
	background-image: url('../img/index-phone-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-web-normal {
	width: 404px;
	height: 308px;
	background-image: url('../img/index-web-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-web-normal.png {
	width: 404px;
	height: 308px;
	background-image: url('../img/index-web-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-pad-hover {
	width: 317px;
	height: 204px;
	background-image: url('../img/index-pad-hover.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-pad-hover.png {
	width: 317px;
	height: 204px;
	background-image: url('../img/index-pad-hover.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%index-web-hover {
	width: 404px;
	height: 308px;
	background-image: url('../img/index-web-hover.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%index-web-hover.png {
	width: 404px;
	height: 308px;
	background-image: url('../img/index-web-hover.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%preview-input-active {
	width: 640px;
	height: 360px;
	background-image: url('../img/preview-input-active.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%preview-input-active.png {
	width: 640px;
	height: 360px;
	background-image: url('../img/preview-input-active.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%preview-input-normal {
	width: 640px;
	height: 360px;
	background-image: url('../img/preview-input-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%preview-input-normal.png {
	width: 640px;
	height: 360px;
	background-image: url('../img/preview-input-normal.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%intro-flow {
	width: 1406px;
	height: 574px;
	background-image: url('../img/intro-flow.jpg');
	background-size: contain;
	background-repeat: no-repeat;
}
%intro-flow.jpg {
	width: 1406px;
	height: 574px;
	background-image: url('../img/intro-flow.jpg');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
// -----------------------------------------------
%intro-flow {
	width: 2008px;
	height: 820px;
	background-image: url('../img/intro-flow.png');
	background-size: contain;
	background-repeat: no-repeat;
}
%intro-flow.png {
	width: 2008px;
	height: 820px;
	background-image: url('../img/intro-flow.png');
	background-size: contain;
	background-repeat: no-repeat;
}
// -----------------------------------------------
