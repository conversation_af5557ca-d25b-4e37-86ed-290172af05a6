<!DOCTYPE html>
<html lang="zh-cmn-Hans" prefix="og: http://ogp.me/ns#">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  <!-- SEO 基础标签 -->
  <title>SVGA - 跨平台高性能动画解决方案 | iOS/Android/Flutter/Web/HarmonyOS动画格式</title>
  <meta name="description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。支持After Effects和Animate导出，动画文件体积小，播放资源占用优，设计师和开发者友好的跨平台动画解决方案。">
  <meta name="keywords" content="SVGA, 直播礼物动画, 高性能动画, 跨平台动画方案, 矢量动画, 透明MP4, After Effects, Animate, iOS动画, Android动画, Flutter动画, Web动画, 动画格式, 开源动画库">
  <meta name="author" content="SVGA Team">
  <meta name="robots" content="index, follow, max-snippet:160, max-image-preview:large, max-video-preview:-1">
  <meta name="publicPath" content="">
  
  <!-- 规范化URL -->
  <link rel="canonical" href="https://svga.dev/">
  
  <!-- 网站图标 -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/favicon.ico">
  
  <!-- Open Graph 标签 (Facebook) -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta property="og:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta property="og:url" content="https://svga.dev/">
  <meta property="og:site_name" content="SVGA">
  <meta property="og:image" content="https://svga.dev/img/index-example.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="SVGA动画效果展示">
  <meta property="og:locale" content="zh_CN">
  
  <!-- Twitter Card 标签 -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta name="twitter:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta name="twitter:image" content="https://svga.dev/img/index-example.jpg">
  <meta name="twitter:image:alt" content="SVGA动画效果展示">
  
  <!-- 语言和地区 -->
  <meta name="language" content="zh-CN">
  <meta name="geo.region" content="CN">
  
  <!-- 主题颜色 -->
  <meta name="theme-color" content="#4A90E2">
  <meta name="msapplication-TileColor" content="#4A90E2">
  
  <!-- 结构化数据 (JSON-LD) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "SVGA",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。",
    "url": "https://svga.dev/",
    "author": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://github.com/svga"
    },
    "operatingSystem": ["iOS", "Android", "Web", "Flutter"],
    "applicationCategory": "DeveloperApplication",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "downloadUrl": "https://github.com/svga",
    "screenshot": "https://svga.dev/img/index-example.jpg",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "500"
    },
    "programmingLanguage": ["JavaScript", "Swift", "Kotlin", "Dart"],
    "codeRepository": "https://github.com/svga"
  }
  </script>
  
  <!-- 面包屑导航结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "首页",
        "item": "https://svga.dev/"
      }
    ]
  }
  </script>
  
  <!-- DNS预取 -->
  <link rel="dns-prefetch" href="//github.com">
  <link rel="dns-prefetch" href="//pagead2.googlesyndication.com">
  
  <!-- 样式表 -->
  <!-- <style>canvas, header input { display: none; }</style> -->
  <link rel="stylesheet" href="/assets/font/amplesoft-bold.css">
  <link rel="stylesheet" href="/css/main.css">
  
  <!-- Google AdSense -->
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1514042314776212"
     crossorigin="anonymous"></script>
</head>
<body ontouchstart="">
<header>
  <div class="center">
    <h2><a href="./index.html">SVGA</a></h2>
    <input type="checkbox" name="checkbox" id="checkbox">
    <div class="icon">
      <i></i>
      <i></i>
      <i></i>
      <i></i>
      <i></i>
    </div>
    <nav>
      <div class="tab">
        <div class="item">
          <div class="label">
            <a href="./intro.html"><em data-i18n="header-nav-0">介绍</em></a>
          </div>
        </div>
        <div class="item">
          <div class="label">
            <a href="./integrated.html"><em data-i18n="header-nav-1">集成指南</em></a>
          </div>
        </div>
        <div class="item">
          <div class="list">
            <div class="item">
              <div class="label">
                <a target="_blank" href="https://github.com/svga"><em data-i18n="header-nav-3">开源代码</em></a>
              </div>
            </div>
            <div class="item">
              <div class="label">
                <a href="./designer.html"><em data-i18n="header-nav-5">设计师工具</em></a>
              </div>
            </div>
            <div class="item pc-show mb-hide">
              <div class="label">
                <a href="./svga-preview.html"><em data-i18n="header-nav-6">在线播放器</em></a>
              </div>
            </div>
            <!-- <div class="item svgatools">
              <div class="label">
                <a href="https://www.nangonghan.com/svga/"><em data-i18n="header-nav-8">SVGA Tools</em></a>
              </div>
            </div> -->
            <div class="item">
              <div class="label">
                <a href="./article.html"><em data-i18n="header-nav-7">资源文章</em></a>
              </div>
            </div>
          </div>
          <div class="label"><em data-i18n="header-nav-2">社区生态</em></div>
        </div>
        <div class="item">
          <div class="list">
            <div class="item">
              <div class="label">
                <a href="/en/index.html" data-lang="en"><em>English</em></a>
              </div>
            </div>
            <div class="item">
              <div class="label">
                <a href="/index.html" data-lang="zh"><em>中文</em></a>
              </div>
            </div>
          </div>
          <div class="label"><em data-i18n="header-language">多语言</em></div>
        </div>
      </div>
    </nav>
  </div>
</header>

  <main role="main">
    <article id="index" itemscope itemtype="https://schema.org/SoftwareApplication">
      <section aria-labelledby="hero-title">
        <div class="center">
          <div class="container">
            <div class="col left">
              <h1 id="hero-title" data-i18n="index-title-0" itemprop="name">SVGA动画格式</h1>
              <h2 data-i18n="index-title-1" itemprop="headline">高性能动画播放体验</h2>
              <p data-i18n="index-title-2" itemprop="description">SVGA 是一种同时兼容 iOS / Android / Flutter / Web / HarmonyOS 的跨平台动画解决方案。</p>
            <p>
              <a href="./svga-preview.html">
                <button data-i18n="index-title-button" class="demo">立即体验</button>
              </a>
              <a target="_blank" href="https://github.com/svga">
                <button class="github"><svg aria-labelledby="simpleicons-github-dark-icon" lang="" role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title id="simpleicons-github-icon" lang="en">GitHub Dark icon</title><path fill="#7F8C8D" d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"></path></svg>GitHub</button>
              </a>
            </p>
            <p>
              <br>
            </p>
          </div>
          <div class="col right">
            <figure>
              <div id="index-response" class="response">
                <div class="web">
                  <div class="active"></div>
                  <canvas id="index-web" width="400" height="400"></canvas>
                </div>
                <div class="pad">
                  <div class="active"></div>
                  <canvas id="index-pad" width="250" height="250"></canvas>
                </div>
                <div class="phone">
                  <div class="active"></div>
                  <canvas id="index-phone" width="130" height="130"></canvas>
                </div>
              </div>
            </figure>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col left"><br></div>
          <div class="col right">
              <h2 data-i18n="index-reason-0">为什么选 SVGA？</h2>
          </div>
          <div class="clear"></div>
          <div class="col left">
            <figure itemscope itemtype="https://schema.org/ImageObject">
              <img src="/img/index-example.jpg" 
                   alt="SVGA动画效果展示 - 跨平台动画解决方案实际运行效果" 
                   itemprop="contentUrl"
                   loading="lazy"
                   width="360" 
                   height="650">
              <div class="canvas">
                <canvas id="index-example" width="360" height="360" 
                        aria-label="SVGA动画播放演示区域"></canvas>
              </div>
            </figure>
          </div>
          <div class="col right">
            <h3 data-i18n="index-reason-1">对开发者友好</h3>
            <p data-i18n="index-reason-2">便捷的 SDK 使得 SVGA 可运行在不同平台上，集成步骤轻松简单。</p>
            <h3 data-i18n="index-reason-3">对设计师友好</h3>
            <p data-i18n="index-reason-4">可以使用 After Effects 或是 Animate(Flash) 进行动画设计，SVGA 可以支持其中的大部分效果，设计师使用导出工具即可生成动画文件。</p>
            <h3 data-i18n="index-reason-5">性价比更高</h3>
            <p data-i18n="index-reason-6">动画文件体积更小，播放资源占用更优，动画还原效果更好。</p>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container align">
          <h2 data-i18n="index-about-0">关于 SVGA</h2>
          <div class="col">
            <div class="icon icon0"><i></i></div>
            <h3 data-i18n="index-about-1">集成指南</h3>
            <p data-i18n="index-about-2">通过集成指南，可以轻松的在 iOS / Android / Flutter / Web / HarmonyOS 中引入 SVGA 播放器并播放资源文件。</p>
          </div>
          <div class="col">
            <div class="icon icon1"><i></i></div>
            <h3 data-i18n="index-about-3">设计师工具</h3>
            <p data-i18n="index-about-4">使用设计师工具，可以轻松的将 Aep 或 Fla 格式的动画文件导出成 SVGA 格式资源文件。</p>
          </div>
          <div class="col">
            <div class="icon icon2"><i></i></div>
            <h3 data-i18n="index-about-5">开源代码</h3>
            <p data-i18n="index-about-6">各平台的播放器都是开源的，欢迎提交 PR 贡献代码。</p>
          </div>
          <div class="col">
            <div class="icon icon3"><i></i></div>
            <h3 data-i18n="index-about-7">技术支持</h3>
            <p><span data-i18n="index-about-8">在使用过程中，如有任何问题欢迎提交</span><a target="_blank" href="https://github.com/svga/docs/issues" data-i18n="index-about-9">issue</a><!-- <span data-i18n="index-about-10">或者加入我们的 QQ 群参与讨论：</span><a href="//shang.qq.com/wpa/qunwpa?idkey=f795fb4b48f0797dbd6baca5a1597da1bb2caa2a21ee0273ea7bc1173c489cc0">576461005</a><span data-i18n="index-about-11">。</span>--></p>
          </div>
        </div>
      </div>
    </section>
  </article>
</main>
<footer>
  <div class="center">
    
    <!-- 友情链接区域 -->
    <section aria-label="友情链接" class="partner-links" style="text-align: center; margin:5px 0 0;">
      <nav aria-label="合作伙伴链接">
        <ul style="display: inline-block; margin: 0; padding: 0; list-style: none;">
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://codefont.dev" title="CodeFont - Font Switching Made Simple for VS Code" target="_blank" rel="noopener" 
               aria-label="Code Font">CodeFont</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://sline.dev" title="Sline - SHOPLINE Custom-Built E-commerce Template Engine" target="_blank" rel="noopener" 
               aria-label="Sline">Sline</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://colorcode.cc" title="ColorCode - 专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。支持 OKLCH、LAB、CMYK 等专业格式，ΔE≤0.5 精度保障" target="_blank" rel="noopener" 
               aria-label="ColorCode">ColorCode</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://zartify.com" title="Zartify - Premium HD Classic Paintings for Prints, Displays &amp; Inspiration" target="_blank" rel="noopener"
               aria-label="Zartify">Zartify</a>
          </li>
        </ul>
      </nav>
    </section>
    
    <p>Copyright &copy; 2016-2025 <a href="https://svga.dev" rel="noopener">SVGA Team</a></p>
  </div>
  
  <!-- 组织机构结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "SVGA Team",
    "url": "https://svga.dev",
    "logo": "https://svga.dev/favicon.ico",
    "description": "SVGA是一个开源的跨平台动画解决方案团队",
    "foundingDate": "2016",
    "sameAs": [
      "https://github.com/svga"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "technical support",
      "url": "https://github.com/svga/docs/issues"
    }
  }
  </script>
  
  <!-- 网站结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "SVGA",
    "url": "https://svga.dev",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式",
    "inLanguage": "zh-CN",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://github.com/svga/docs/issues?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://svga.dev"
    }
  }
  </script>
</footer>
<script src="/assets/jszip.min.js"></script>
<script src="/assets/svga.min.js"></script>
<script src="/js/main.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-8307426-22"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-8307426-22');
</script>
<script src="https://analytics.ahrefs.com/analytics.js" data-key="LPY8uaT1uYB8yUgv+xE/Uw" async></script>

</body>
</html>
