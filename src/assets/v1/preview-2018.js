/*!
 * @project : svga官网
 * @version : 0.0.1
 * <AUTHOR> 
 * @update  : 2018-04-23 9:26:30 am
 */
!function(e){function t(o){if(r[o])return r[o].exports;var n=r[o]={exports:{},id:o,loaded:!1};return e[o].call(n.exports,n,n.exports,t),n.loaded=!0,n.exports}var r={};return t.m=e,t.c=r,t.p="./js/",t(0)}([function(e,t,r){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}function i(e,t){var r=window.URL||window.webkitURL||window,o=new Blob([t]),i=document.createElementNS("http://www.w3.org/1999/xhtml","a");i.href=r.createObjectURL(o),i.download=e,n(i)}function s(e,t){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onloadend=function(){"object"===("undefined"==typeof window?"undefined":(0,p["default"])(window))&&(window.SVGAPerformance.networkEnd=performance.now(),window.SVGAPerformance.unzipStart=performance.now()),t(r.response)},r.send()}function a(e){for(var t=window.atob(e),r=t.length,o=new Uint8Array(r),n=0;n<r;n++)o[n]=t.charCodeAt(n);return o.buffer}var u=r(1),p=o(u),g=r(56),c=o(g),l=r(100),h=o(l),f=$(".preview-main"),d=$("#previewDemo"),y=new SVGA.Player("#previewDemo"),_=new SVGA.Parser;$(function(){function e(e){e.stopPropagation(),e.preventDefault();var t=e.dataTransfer.files;_.load(t[0],function(e){console.log(e),window.currentVideoItem=e,window.currentFileName=t[0].name.split(".")[0],f.removeClass("is-active"),f.removeClass("ext-bg").find("p").hide(),f.css("width",e.videoSize.width+"px"),f.css("height",e.videoSize.height+"px"),d.css("width",e.videoSize.width+"px"),d.css("height",e.videoSize.height+"px"),y.setVideoItem(e),y.startAnimation(),document.getElementById("previewResource").parentElement.style.display="inline-block",document.getElementById("colorPicker").style.display="block",document.getElementById("downloadResource").parentElement.style.display="none",document.getElementById("changeVersion").parentElement.style.display="inline-block",document.getElementById("qrcode").style.display="inline-block"})}function t(e){e.stopPropagation(),e.preventDefault(),e.dataTransfer.dragEffect="copy",f.hasClass("is-active")||f.addClass("is-active")}function r(e){e.stopPropagation(),e.preventDefault(),f.removeClass("is-active")}function o(e){var t=e.target.files;_.load(t[0],function(e){window.currentVideoItem=e,window.currentFileName=t[0].name.split(".")[0],f.removeClass("is-active"),f.removeClass("ext-bg").find("p").hide(),f.css("width",e.videoSize.width+"px"),f.css("height",e.videoSize.height+"px"),d.css("width",e.videoSize.width+"px"),d.css("height",e.videoSize.height+"px"),y.setVideoItem(e),y.startAnimation(),document.getElementById("previewResource").parentElement.style.display="inline-block",document.getElementById("colorPicker").style.display="block",document.getElementById("downloadResource").parentElement.style.display="none",document.getElementById("changeVersion").parentElement.style.display="inline-block",document.getElementById("qrcode").style.display="inline-block"})}var n=f.get(0);n.addEventListener("drop",e,!1),n.addEventListener("dragover",t,!1),n.addEventListener("dragleave",r,!1),document.getElementById("fs").addEventListener("change",o,!1),$('[data-role="upload-file"]').on("click",function(){document.getElementById("fs").click()})}),$('[data-role="download-file"]').on("click",function(){if(void 0!=window.currentVideoItem){var e=window.currentVideoItem.version;if($(".alertViewTitle").remove(),$('[data-role="download-file1_0"]').css("display","block"),$('[data-role="download-file1_5"]').css("display","block"),$('[data-role="download-file2_0"]').css("display","block"),"2.0"==e){$('[data-role="download-file1_0"]').css("display","none"),$('[data-role="download-file1_5"]').css("display","none"),$('[data-role="download-file2_0"]').css("display","none");var t='<blockquote class="alertViewTitle" style="position: static; margin-top: 44px"><p style="color:#123456;font-size:18px;" >您当前播放的 SVGA 文件版本为：'+e+"</p></blockquote>";$(".alertViewTips").before(t),$(".alertViewTips").css("margin-top",30),$(".alertViewTipsP").text("暂不支持高版本往低版本转换，");var r='<blockquote class="alertViewTitle" style="position: static; margin-top: 0px"><p style="color:#123456;font-size:18px;" >请使用 SVGA Converter 插件导出。</p></blockquote>';$(".alertViewTips").after(r)}else{var t='<blockquote class="alertViewTitle" style="position: static; margin-top: 44px"><p style="color:#123456;font-size:18px;" >您当前播放的 SVGA 文件版本为：'+e+"</p></blockquote>";$(".alertViewTips").before(t),$(".alertViewTips").css("margin-top",0),$('[data-role="download-file'+e.split(".").join("_")+'"]').css("display","none"),$(".alertViewTipsP").text("请选择您要转换的版本：")}}$(".popbox").fadeIn("fast")}),$('[data-role="popbox-close"]').on("click",function(){$(".popbox").fadeOut("fast")}),$('[data-role="storage-videoItem"]').on("click",function(){h["default"].initUI(),$("#swiperButtonPrev").css("display","block"),$("#previewDemo").css("display","none"),mySwiper.slideNext()}),$('[data-role="download-file1_0"]').on("click",function(){return $(".popbox").fadeOut("fast"),void 0==window.currentVideoItem?void _.load("https://raw.githubusercontent.com/yyued/svga.dev/master/assets/svga/sample-2_0.svga",function(e){f.removeClass("is-active"),f.removeClass("ext-bg").find("p").hide(),f.css("width",e.videoSize.width+"px"),f.css("height",e.videoSize.height+"px"),d.css("width",e.videoSize.width+"px"),d.css("height",e.videoSize.height+"px"),y.setVideoItem(e),y.startAnimation(),c["default"].packagingVideoItemToSVGA(e,"1.0",function(e){i("sample-1_0.svga",e)})}):void c["default"].packagingVideoItemToSVGA(window.currentVideoItem,"1.0",function(e){i(window.currentFileName+"-1_0.svga",e)})}),$('[data-role="download-file1_5"]').on("click",function(){return $(".popbox").fadeOut("fast"),void 0==window.currentVideoItem?void _.load("https://raw.githubusercontent.com/yyued/svga.dev/master/assets/svga/sample-2_0.svga",function(e){f.removeClass("is-active"),f.removeClass("ext-bg").find("p").hide(),f.css("width",e.videoSize.width+"px"),f.css("height",e.videoSize.height+"px"),d.css("width",e.videoSize.width+"px"),d.css("height",e.videoSize.height+"px"),y.setVideoItem(e),y.startAnimation(),c["default"].packagingVideoItemToSVGA(e,"1.5",function(e){i("sample-1_5.svga",e)})}):void c["default"].packagingVideoItemToSVGA(window.currentVideoItem,"1.5",function(e){i(window.currentFileName+"-1_5.svga",e)})}),$('[data-role="download-file2_0"]').on("click",function(){return $(".popbox").fadeOut("fast"),void 0==window.currentVideoItem?void _.load("https://raw.githubusercontent.com/yyued/svga.dev/master/assets/svga/sample-2_0.svga",function(e){f.removeClass("is-active"),f.removeClass("ext-bg").find("p").hide(),f.css("width",e.videoSize.width+"px"),f.css("height",e.videoSize.height+"px"),d.css("width",e.videoSize.width+"px"),d.css("height",e.videoSize.height+"px"),y.setVideoItem(e),y.startAnimation(),c["default"].packagingVideoItemToSVGA(e,"2.0",function(e){i("sample-2_0.svga",e)})}):void c["default"].packagingVideoItemToSVGA(window.currentVideoItem,"2.0",function(e){i(window.currentFileName+"-2_0.svga",e)})}),$('[data-role="change-color"]').on("click",function(){$("#previewDemo").css("background-color",$(this).css("background-color"))})},function(e,t,r){"use strict";var o=r(2)["default"];t["default"]=function(e){return e&&e.constructor===o?"symbol":typeof e},t.__esModule=!0},function(e,t,r){e.exports={"default":r(3),__esModule:!0}},function(e,t,r){r(4),r(53),r(54),r(55),e.exports=r(10).Symbol},function(e,t,r){"use strict";var o=r(5),n=r(6),i=r(7),s=r(9),a=r(21),u=r(22).KEY,p=r(8),g=r(24),c=r(25),l=r(23),h=r(26),f=r(27),d=r(28),y=r(30),_=r(43),m=r(46),b=r(15),v=r(33),S=r(19),E=r(20),w=r(47),A=r(50),j=r(52),T=r(14),I=r(31),B=j.f,M=T.f,O=A.f,x=o.Symbol,C=o.JSON,F=C&&C.stringify,k="prototype",R=h("_hidden"),D=h("toPrimitive"),N={}.propertyIsEnumerable,U=g("symbol-registry"),L=g("symbols"),P=g("op-symbols"),W=Object[k],z="function"==typeof x,V=o.QObject,H=!V||!V[k]||!V[k].findChild,G=i&&p(function(){return 7!=w(M({},"a",{get:function(){return M(this,"a",{value:7}).a}})).a})?function(e,t,r){var o=B(W,t);o&&delete W[t],M(e,t,r),o&&e!==W&&M(W,t,o)}:M,Y=function(e){var t=L[e]=w(x[k]);return t._k=e,t},Z=z&&"symbol"==typeof x.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof x},X=function ne(e,t,r){return e===W&&X(P,t,r),b(e),t=S(t,!0),b(r),n(L,t)?(r.enumerable?(n(e,R)&&e[R][t]&&(e[R][t]=!1),r=w(r,{enumerable:E(0,!1)})):(n(e,R)||M(e,R,E(1,{})),e[R][t]=!0),G(e,t,r)):M(e,t,r)},K=function ie(e,t){b(e);for(var r=_(t=v(t)),o=0,n=r.length,i;n>o;)X(e,i=r[o++],t[i]);return e},q=function se(e,t){return void 0===t?w(e):K(w(e),t)},J=function ae(e){var t=N.call(this,e=S(e,!0));return!(this===W&&n(L,e)&&!n(P,e))&&(!(t||!n(this,e)||!n(L,e)||n(this,R)&&this[R][e])||t)},Q=function ue(e,t){if(e=v(e),t=S(t,!0),e!==W||!n(L,t)||n(P,t)){var r=B(e,t);return!r||!n(L,t)||n(e,R)&&e[R][t]||(r.enumerable=!0),r}},ee=function pe(e){for(var t=O(v(e)),r=[],o=0,i;t.length>o;)n(L,i=t[o++])||i==R||i==u||r.push(i);return r},te=function ge(e){for(var t=e===W,r=O(t?P:v(e)),o=[],i=0,s;r.length>i;)!n(L,s=r[i++])||t&&!n(W,s)||o.push(L[s]);return o};z||(x=function ce(){if(this instanceof x)throw TypeError("Symbol is not a constructor!");var e=l(arguments.length>0?arguments[0]:void 0),t=function(r){this===W&&t.call(P,r),n(this,R)&&n(this[R],e)&&(this[R][e]=!1),G(this,e,E(1,r))};return i&&H&&G(W,e,{configurable:!0,set:t}),Y(e)},a(x[k],"toString",function le(){return this._k}),j.f=Q,T.f=X,r(51).f=A.f=ee,r(45).f=J,r(44).f=te,i&&!r(29)&&a(W,"propertyIsEnumerable",J,!0),f.f=function(e){return Y(h(e))}),s(s.G+s.W+s.F*!z,{Symbol:x});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),oe=0;re.length>oe;)h(re[oe++]);for(var re=I(h.store),oe=0;re.length>oe;)d(re[oe++]);s(s.S+s.F*!z,"Symbol",{"for":function(e){return n(U,e+="")?U[e]:U[e]=x(e)},keyFor:function he(e){if(Z(e))return y(U,e);throw TypeError(e+" is not a symbol!")},useSetter:function(){H=!0},useSimple:function(){H=!1}}),s(s.S+s.F*!z,"Object",{create:q,defineProperty:X,defineProperties:K,getOwnPropertyDescriptor:Q,getOwnPropertyNames:ee,getOwnPropertySymbols:te}),C&&s(s.S+s.F*(!z||p(function(){var e=x();return"[null]"!=F([e])||"{}"!=F({a:e})||"{}"!=F(Object(e))})),"JSON",{stringify:function fe(e){if(void 0!==e&&!Z(e)){for(var t=[e],r=1,o,n;arguments.length>r;)t.push(arguments[r++]);return o=t[1],"function"==typeof o&&(n=o),!n&&m(o)||(o=function(e,t){if(n&&(t=n.call(this,e,t)),!Z(t))return t}),t[1]=o,F.apply(C,t)}}}),x[k][D]||r(13)(x[k],D,x[k].valueOf),c(x,"Symbol"),c(Math,"Math",!0),c(o.JSON,"JSON",!0)},function(e,t){var r=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},function(e,t){var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},function(e,t,r){e.exports=!r(8)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,r){var o=r(5),n=r(10),i=r(11),s=r(13),a="prototype",u=function(e,t,r){var p=e&u.F,g=e&u.G,c=e&u.S,l=e&u.P,h=e&u.B,f=e&u.W,d=g?n:n[t]||(n[t]={}),y=d[a],_=g?o:c?o[t]:(o[t]||{})[a],m,b,v;g&&(r=t);for(m in r)b=!p&&_&&void 0!==_[m],b&&m in d||(v=b?_[m]:r[m],d[m]=g&&"function"!=typeof _[m]?r[m]:h&&b?i(v,o):f&&_[m]==v?function(e){var t=function(t,r,o){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,r)}return new e(t,r,o)}return e.apply(this,arguments)};return t[a]=e[a],t}(v):l&&"function"==typeof v?i(Function.call,v):v,l&&((d.virtual||(d.virtual={}))[m]=v,e&u.R&&y&&!y[m]&&s(y,m,v)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t){var r=e.exports={version:"2.4.0"};"number"==typeof __e&&(__e=r)},function(e,t,r){var o=r(12);e.exports=function(e,t,r){if(o(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,o){return e.call(t,r,o)};case 3:return function(r,o,n){return e.call(t,r,o,n)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,r){var o=r(14),n=r(20);e.exports=r(7)?function(e,t,r){return o.f(e,t,n(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t,r){var o=r(15),n=r(17),i=r(19),s=Object.defineProperty;t.f=r(7)?Object.defineProperty:function a(e,t,r){if(o(e),t=i(t,!0),o(r),n)try{return s(e,t,r)}catch(a){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){var o=r(16);e.exports=function(e){if(!o(e))throw TypeError(e+" is not an object!");return e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,r){e.exports=!r(7)&&!r(8)(function(){return 7!=Object.defineProperty(r(18)("div"),"a",{get:function(){return 7}}).a})},function(e,t,r){var o=r(16),n=r(5).document,i=o(n)&&o(n.createElement);e.exports=function(e){return i?n.createElement(e):{}}},function(e,t,r){var o=r(16);e.exports=function(e,t){if(!o(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!o(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!o(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!o(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,r){e.exports=r(13)},function(e,t,r){var o=r(23)("meta"),n=r(16),i=r(6),s=r(14).f,a=0,u=Object.isExtensible||function(){return!0},p=!r(8)(function(){return u(Object.preventExtensions({}))}),g=function(e){s(e,o,{value:{i:"O"+ ++a,w:{}}})},c=function(e,t){if(!n(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!i(e,o)){if(!u(e))return"F";if(!t)return"E";g(e)}return e[o].i},l=function(e,t){if(!i(e,o)){if(!u(e))return!0;if(!t)return!1;g(e)}return e[o].w},h=function(e){return p&&f.NEED&&u(e)&&!i(e,o)&&g(e),e},f=e.exports={KEY:o,NEED:!1,fastKey:c,getWeak:l,onFreeze:h}},function(e,t){var r=0,o=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++r+o).toString(36))}},function(e,t,r){var o=r(5),n="__core-js_shared__",i=o[n]||(o[n]={});e.exports=function(e){return i[e]||(i[e]={})}},function(e,t,r){var o=r(14).f,n=r(6),i=r(26)("toStringTag");e.exports=function(e,t,r){e&&!n(e=r?e:e.prototype,i)&&o(e,i,{configurable:!0,value:t})}},function(e,t,r){var o=r(24)("wks"),n=r(23),i=r(5).Symbol,s="function"==typeof i,a=e.exports=function(e){return o[e]||(o[e]=s&&i[e]||(s?i:n)("Symbol."+e))};a.store=o},function(e,t,r){t.f=r(26)},function(e,t,r){var o=r(5),n=r(10),i=r(29),s=r(27),a=r(14).f;e.exports=function(e){var t=n.Symbol||(n.Symbol=i?{}:o.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t){e.exports=!0},function(e,t,r){var o=r(31),n=r(33);e.exports=function(e,t){for(var r=n(e),i=o(r),s=i.length,a=0,u;s>a;)if(r[u=i[a++]]===t)return u}},function(e,t,r){var o=r(32),n=r(42);e.exports=Object.keys||function i(e){return o(e,n)}},function(e,t,r){var o=r(6),n=r(33),i=r(37)(!1),s=r(41)("IE_PROTO");e.exports=function(e,t){var r=n(e),a=0,u=[],p;for(p in r)p!=s&&o(r,p)&&u.push(p);for(;t.length>a;)o(r,p=t[a++])&&(~i(u,p)||u.push(p));return u}},function(e,t,r){var o=r(34),n=r(36);e.exports=function(e){return o(n(e))}},function(e,t,r){var o=r(35);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==o(e)?e.split(""):Object(e)}},function(e,t){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){var o=r(33),n=r(38),i=r(40);e.exports=function(e){return function(t,r,s){var a=o(t),u=n(a.length),p=i(s,u),g;if(e&&r!=r){for(;u>p;)if(g=a[p++],g!=g)return!0}else for(;u>p;p++)if((e||p in a)&&a[p]===r)return e||p||0;return!e&&-1}}},function(e,t,r){var o=r(39),n=Math.min;e.exports=function(e){return e>0?n(o(e),9007199254740991):0}},function(e,t){var r=Math.ceil,o=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?o:r)(e)}},function(e,t,r){var o=r(39),n=Math.max,i=Math.min;e.exports=function(e,t){return e=o(e),e<0?n(e+t,0):i(e,t)}},function(e,t,r){var o=r(24)("keys"),n=r(23);e.exports=function(e){return o[e]||(o[e]=n(e))}},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){var o=r(31),n=r(44),i=r(45);e.exports=function(e){var t=o(e),r=n.f;if(r)for(var s=r(e),a=i.f,u=0,p;s.length>u;)a.call(e,p=s[u++])&&t.push(p);return t}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,r){var o=r(35);e.exports=Array.isArray||function n(e){return"Array"==o(e)}},function(e,t,r){var o=r(15),n=r(48),i=r(42),s=r(41)("IE_PROTO"),a=function(){},u="prototype",p=function(){var e=r(18)("iframe"),t=i.length,o="<",n=">",s;for(e.style.display="none",r(49).appendChild(e),e.src="javascript:",s=e.contentWindow.document,s.open(),s.write(o+"script"+n+"document.F=Object"+o+"/script"+n),s.close(),p=s.F;t--;)delete p[u][i[t]];return p()};e.exports=Object.create||function g(e,t){var r;return null!==e?(a[u]=o(e),r=new a,a[u]=null,r[s]=e):r=p(),void 0===t?r:n(r,t)}},function(e,t,r){var o=r(14),n=r(15),i=r(31);e.exports=r(7)?Object.defineProperties:function s(e,t){n(e);for(var r=i(t),s=r.length,a=0,u;s>a;)o.f(e,u=r[a++],t[u]);return e}},function(e,t,r){e.exports=r(5).document&&document.documentElement},function(e,t,r){var o=r(33),n=r(51).f,i={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(e){try{return n(e)}catch(t){return s.slice()}};e.exports.f=function u(e){return s&&"[object Window]"==i.call(e)?a(e):n(o(e))}},function(e,t,r){var o=r(32),n=r(42).concat("length","prototype");t.f=Object.getOwnPropertyNames||function i(e){return o(e,n)}},function(e,t,r){var o=r(45),n=r(20),i=r(33),s=r(19),a=r(6),u=r(17),p=Object.getOwnPropertyDescriptor;t.f=r(7)?p:function g(e,t){if(e=i(e),t=s(t,!0),u)try{return p(e,t)}catch(r){}if(a(e,t))return n(!o.f.call(e,t),e[t])}},function(e,t){},function(e,t,r){r(28)("asyncIterator")},function(e,t,r){r(28)("observable")},function(module,exports,__webpack_require__){function _interopRequireDefault(e){return e&&e.__esModule?e:{"default":e}}function _base64ToArrayBuffer(e){for(var t=window.atob(e),r=t.length,o=new Uint8Array(r),n=0;n<r;n++)o[n]=t.charCodeAt(n);return o.buffer}var _freeze=__webpack_require__(57),_freeze2=_interopRequireDefault(_freeze),_create=__webpack_require__(61),_create2=_interopRequireDefault(_create),_isFrozen=__webpack_require__(64),_isFrozen2=_interopRequireDefault(_isFrozen),_seal=__webpack_require__(67),_seal2=_interopRequireDefault(_seal),_getOwnPropertyDescriptor=__webpack_require__(70),_getOwnPropertyDescriptor2=_interopRequireDefault(_getOwnPropertyDescriptor),_defineProperties=__webpack_require__(73),_defineProperties2=_interopRequireDefault(_defineProperties),_iterator=__webpack_require__(76),_iterator2=_interopRequireDefault(_iterator),_typeof2=__webpack_require__(1),_typeof3=_interopRequireDefault(_typeof2),_symbol=__webpack_require__(2),_symbol2=_interopRequireDefault(_symbol),_defineProperty=__webpack_require__(89),_defineProperty2=_interopRequireDefault(_defineProperty),_stringify=__webpack_require__(92),_stringify2=_interopRequireDefault(_stringify),_jszip=__webpack_require__(94),_jszip2=_interopRequireDefault(_jszip),_pako_deflate=__webpack_require__(99),_pako_deflate2=_interopRequireDefault(_pako_deflate),SVGAPackaging={packagingVideoItemToSVGA:function e(t,r,o){var n={},i={},s={};for(var a in t.images)i[a]=a;s.fps=t.FPS,s.frames=t.frames,s.viewBox=t.videoSize,n.ver=r,n.images=i,n.movie=s;var u=(0,_stringify2["default"])(t.sprites);if(n.sprites=JSON.parse(u).map(function(e){return e.frames=e.frames.map(function(e){if(0==e.alpha)return{};var t={};return t.alpha=e.alpha,t.layout=e.layout,t.transform=e.transform,null!=e.maskPath&&(t.clipPath=e.maskPath._d),null!=e.shapes&&(t.shapes=e.shapes),t}),e}),"2.0"==r){var p={};for(var g in t.images)p[g]=_base64ToArrayBuffer(t.images[g]);var c=_pako_deflate2["default"].deflate(SVGAProtoHelper_2_0_0.convertToProto(n,p)),l=new Blob([c],{type:"application/octet-binary"});o(l)}else{var h=new _jszip2["default"];for(var g in t.images)h.file(g+".png",t.images[g],{base64:!0});if(h.file("movie.spec",(0,_stringify2["default"])(n)),"1.5"==r){var f=SVGAProtoHelper_1_5_0.convertToProto(n);h.file("movie.binary",f)}h.generateAsync({type:"blob",compression:"DEFLATE"}).then(function(e){o(e)})}}};!function(e){function t(o){if(r[o])return r[o].exports;var n=r[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,t),n.l=!0,n.exports}var r={};t.m=e,t.c=r,t.d=function(e,r,o){t.o(e,r)||(0,_defineProperty2["default"])(e,r,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var r=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=0)}([function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),o=0,n=e.length;o<n;o++)r[o]=e.charCodeAt(o);return t}var i="function"==typeof _symbol2["default"]&&"symbol"==(0,_typeof3["default"])(_iterator2["default"])?function(e){return"undefined"==typeof e?"undefined":(0,_typeof3["default"])(e)}:function(e){return e&&"function"==typeof _symbol2["default"]&&e.constructor===_symbol2["default"]&&e!==_symbol2["default"].prototype?"symbol":"undefined"==typeof e?"undefined":(0,_typeof3["default"])(e)},s=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),(0,_defineProperty2["default"])(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),a=r(1),u=function(){function e(){o(this,e)}return s(e,null,[{key:"convertToProto",value:function t(e){var t=new a.MovieEntity;t.setVersion("1.5.0");var r=new a.MovieParams;r.setViewboxwidth(e.movie.viewBox.width),r.setViewboxheight(e.movie.viewBox.height),r.setFps(e.movie.fps),r.setFrames(e.movie.frames),t.setParams(r);var o=t.getImagesMap();for(var s in e.images){var u=e.images[s];o.set(s,n(u))}var p=t.getSpritesList();return e.sprites.forEach(function(e){var t=new a.SpriteEntity;t.setImagekey(e.imageKey);var r=t.getFramesList();e.frames.forEach(function(e){var t=new a.FrameEntity;if("number"==typeof e.alpha&&t.setAlpha(e.alpha||1),"object"===i(e.layout)&&t.setLayout(function(e){var t=new a.Layout;return t.setX(e.x),t.setY(e.y),t.setWidth(e.width),t.setHeight(e.height),t}(e.layout)),"object"===i(e.transform)&&t.setTransform(function(e){var t=new a.Transform;return t.setA(e.a),t.setB(e.b),t.setC(e.c),t.setD(e.d),t.setTx(e.tx),t.setTy(e.ty),t}(e.transform)),"string"==typeof e.clipPath&&t.setClippath(e.clipPath),"object"===i(e.shapes)){var o=t.getShapesList();e.shapes.forEach(function(e){var t=new a.ShapeEntity;if(t.setType(function(){return"shape"===e.type?a.ShapeEntity.ShapeType.SHAPE:"rect"===e.type?a.ShapeEntity.ShapeType.RECT:"ellipse"===e.type?a.ShapeEntity.ShapeType.ELLIPSE:"keep"===e.type?a.ShapeEntity.ShapeType.KEEP:void 0}(e.type)),"object"===i(e.args))if("shape"===e.type){var r=new a.ShapeEntity.ShapeArgs;"string"==typeof e.args.d&&r.setD(e.args.d),t.setShape(r)}else if("rect"===e.type){var n=new a.ShapeEntity.RectArgs;"number"==typeof e.args.x&&n.setX(e.args.x),"number"==typeof e.args.y&&n.setY(e.args.y),"number"==typeof e.args.width&&n.setWidth(e.args.width),"number"==typeof e.args.height&&n.setHeight(e.args.height),"number"==typeof e.args.cornerRadius&&n.setCornerradius(e.args.cornerRadius),t.setRect(n)}else if("ellipse"===e.type){var s=new a.ShapeEntity.EllipseArgs;"number"==typeof e.args.x&&s.setX(e.args.x),"number"==typeof e.args.y&&s.setY(e.args.y),"number"==typeof e.args.radiusX&&s.setRadiusx(e.args.radiusX),"number"==typeof e.args.radiusY&&s.setRadiusy(e.args.radiusY),t.setEllipse(s)}if("object"===i(e.styles)){var u=new a.ShapeEntity.ShapeStyle;if("object"===i(e.styles.fill)){var p=new a.ShapeEntity.ShapeStyle.RGBAColor;p.setR(e.styles.fill[0]),p.setG(e.styles.fill[1]),p.setB(e.styles.fill[2]),p.setA(e.styles.fill[3]),u.setFill(p)}if("object"===i(e.styles.stroke)){var g=new a.ShapeEntity.ShapeStyle.RGBAColor;g.setR(e.styles.stroke[0]),g.setG(e.styles.stroke[1]),g.setB(e.styles.stroke[2]),g.setA(e.styles.stroke[3]),u.setStroke(g)}"number"==typeof e.styles.strokeWidth&&u.setStrokewidth(e.styles.strokeWidth),"string"==typeof e.styles.lineCap&&("butt"===e.styles.lineCap?u.setLinecap(a.ShapeEntity.ShapeStyle.LineCap.LINECAP_BUTT):"round"===e.styles.lineCap?u.setLinecap(a.ShapeEntity.ShapeStyle.LineCap.LINECAP_ROUND):"square"===e.styles.lineCap&&u.setLinecap(a.ShapeEntity.ShapeStyle.LineCap.LINECAP_SQUARE)),"string"==typeof e.styles.lineJoin&&("miter"===e.styles.lineJoin?u.setLinejoin(a.ShapeEntity.ShapeStyle.LineJoin.LINEJOIN_MITER):"round"===e.styles.lineJoin?u.setLinejoin(a.ShapeEntity.ShapeStyle.LineJoin.LINEJOIN_ROUND):"bevel"===e.styles.lineJoin&&u.setLinejoin(a.ShapeEntity.ShapeStyle.LineJoin.LINEJOIN_BEVEL)),"number"==typeof e.styles.miterLimit&&u.setMiterlimit(e.styles.miterLimit),"object"===i(e.styles.lineDash)&&("number"==typeof e.styles.lineDash[0]&&u.setLinedashi(e.styles.lineDash[0]),"number"==typeof e.styles.lineDash[1]&&u.setLinedashii(e.styles.lineDash[1]),"number"==typeof e.styles.lineDash[2]&&u.setLinedashiii(e.styles.lineDash[2])),t.setStyles(u)}"object"===i(e.transform)&&t.setTransform(function(e){var t=new a.Transform;return t.setA(e.a),t.setB(e.b),t.setC(e.c),t.setD(e.d),t.setTx(e.tx),t.setTy(e.ty),t}(e.transform)),o.push(t)})}r.push(t)}),p.push(t)}),t.serializeBinary()}}]),e}(),p=function(){function e(){o(this,e)}return s(e,null,[{key:"convertToProto",value:function t(e,r){var o=new a.MovieEntity;o.setVersion("2.0.0");var n=new a.MovieParams;n.setViewboxwidth(e.movie.viewBox.width),n.setViewboxheight(e.movie.viewBox.height),n.setFps(e.movie.fps),n.setFrames(e.movie.frames),o.setParams(n);var s=o.getImagesMap();for(var u in e.images)"object"===i(r[u])&&s.set(u,r[u]);var p=o.getSpritesList();return e.sprites.forEach(function(e){var t=new a.SpriteEntity;t.setImagekey(e.imageKey);var r=t.getFramesList();e.frames.forEach(function(e){var t=new a.FrameEntity;if("number"==typeof e.alpha&&t.setAlpha(e.alpha||1),"object"===i(e.layout)&&t.setLayout(function(e){var t=new a.Layout;return t.setX(e.x),t.setY(e.y),t.setWidth(e.width),t.setHeight(e.height),t}(e.layout)),"object"===i(e.transform)&&t.setTransform(function(e){var t=new a.Transform;return t.setA(e.a),t.setB(e.b),t.setC(e.c),t.setD(e.d),t.setTx(e.tx),t.setTy(e.ty),t}(e.transform)),"string"==typeof e.clipPath&&t.setClippath(e.clipPath),"object"===i(e.shapes)){var o=t.getShapesList();e.shapes.forEach(function(e){var t=new a.ShapeEntity;if(t.setType(function(){return"shape"===e.type?a.ShapeEntity.ShapeType.SHAPE:"rect"===e.type?a.ShapeEntity.ShapeType.RECT:"ellipse"===e.type?a.ShapeEntity.ShapeType.ELLIPSE:"keep"===e.type?a.ShapeEntity.ShapeType.KEEP:void 0}(e.type)),"object"===i(e.args))if("shape"===e.type){var r=new a.ShapeEntity.ShapeArgs;"string"==typeof e.args.d&&r.setD(e.args.d),t.setShape(r)}else if("rect"===e.type){var n=new a.ShapeEntity.RectArgs;"number"==typeof e.args.x&&n.setX(e.args.x),"number"==typeof e.args.y&&n.setY(e.args.y),"number"==typeof e.args.width&&n.setWidth(e.args.width),"number"==typeof e.args.height&&n.setHeight(e.args.height),"number"==typeof e.args.cornerRadius&&n.setCornerradius(e.args.cornerRadius),t.setRect(n)}else if("ellipse"===e.type){var s=new a.ShapeEntity.EllipseArgs;"number"==typeof e.args.x&&s.setX(e.args.x),"number"==typeof e.args.y&&s.setY(e.args.y),"number"==typeof e.args.radiusX&&s.setRadiusx(e.args.radiusX),"number"==typeof e.args.radiusY&&s.setRadiusy(e.args.radiusY),t.setEllipse(s)}if("object"===i(e.styles)){var u=new a.ShapeEntity.ShapeStyle;if("object"===i(e.styles.fill)){var p=new a.ShapeEntity.ShapeStyle.RGBAColor;p.setR(e.styles.fill[0]),p.setG(e.styles.fill[1]),p.setB(e.styles.fill[2]),p.setA(e.styles.fill[3]),u.setFill(p)}if("object"===i(e.styles.stroke)){var g=new a.ShapeEntity.ShapeStyle.RGBAColor;g.setR(e.styles.stroke[0]),g.setG(e.styles.stroke[1]),g.setB(e.styles.stroke[2]),g.setA(e.styles.stroke[3]),u.setStroke(g)}"number"==typeof e.styles.strokeWidth&&u.setStrokewidth(e.styles.strokeWidth),"string"==typeof e.styles.lineCap&&("butt"===e.styles.lineCap?u.setLinecap(a.ShapeEntity.ShapeStyle.LineCap.LINECAP_BUTT):"round"===e.styles.lineCap?u.setLinecap(a.ShapeEntity.ShapeStyle.LineCap.LINECAP_ROUND):"square"===e.styles.lineCap&&u.setLinecap(a.ShapeEntity.ShapeStyle.LineCap.LINECAP_SQUARE)),"string"==typeof e.styles.lineJoin&&("miter"===e.styles.lineJoin?u.setLinejoin(a.ShapeEntity.ShapeStyle.LineJoin.LineJoin_MITER):"round"===e.styles.lineJoin?u.setLinejoin(a.ShapeEntity.ShapeStyle.LineJoin.LineJoin_ROUND):"bevel"===e.styles.lineJoin&&u.setLinejoin(a.ShapeEntity.ShapeStyle.LineJoin.LineJoin_BEVEL)),"number"==typeof e.styles.miterLimit&&u.setMiterlimit(e.styles.miterLimit),"object"===i(e.styles.lineDash)&&("number"==typeof e.styles.lineDash[0]&&u.setLinedashi(e.styles.lineDash[0]),"number"==typeof e.styles.lineDash[1]&&u.setLinedashii(e.styles.lineDash[1]),"number"==typeof e.styles.lineDash[2]&&u.setLinedashiii(e.styles.lineDash[2])),t.setStyles(u)}"object"===i(e.transform)&&t.setTransform(function(e){var t=new a.Transform;return t.setA(e.a),t.setB(e.b),t.setC(e.c),t.setD(e.d),t.setTx(e.tx),t.setTy(e.ty),t}(e.transform)),o.push(t)})}r.push(t)}),p.push(t)}),o.serializeBinary()}}]),e}();"object"===("undefined"==typeof window?"undefined":i(window))&&(window.SVGAProtoHelper_1_5_0=u,window.SVGAProtoHelper_2_0_0=p),t.SVGAProtoHelper_1_5_0=u,t.SVGAProtoHelper_2_0_0=p},function(e,t,r){var o=r(2),n=o,i=Function("return this")();n.exportSymbol("proto.com.opensource.svga.FrameEntity",null,i),n.exportSymbol("proto.com.opensource.svga.Layout",null,i),n.exportSymbol("proto.com.opensource.svga.MovieEntity",null,i),n.exportSymbol("proto.com.opensource.svga.MovieParams",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.EllipseArgs",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.RectArgs",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.ShapeArgs",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.ShapeStyle",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.ShapeStyle.LineCap",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.ShapeStyle.LineJoin",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor",null,i),n.exportSymbol("proto.com.opensource.svga.ShapeEntity.ShapeType",null,i),n.exportSymbol("proto.com.opensource.svga.SpriteEntity",null,i),n.exportSymbol("proto.com.opensource.svga.Transform",null,i),proto.com.opensource.svga.MovieParams=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.MovieParams,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.MovieParams.displayName="proto.com.opensource.svga.MovieParams"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.MovieParams.prototype.toObject=function(e){return proto.com.opensource.svga.MovieParams.toObject(e,this)},proto.com.opensource.svga.MovieParams.toObject=function(e,t){var r={viewboxwidth:+o.Message.getFieldWithDefault(t,1,0),viewboxheight:+o.Message.getFieldWithDefault(t,2,0),fps:o.Message.getFieldWithDefault(t,3,0),frames:o.Message.getFieldWithDefault(t,4,0)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.MovieParams.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.MovieParams;return proto.com.opensource.svga.MovieParams.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.MovieParams.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setViewboxwidth(r);
break;case 2:var r=t.readFloat();e.setViewboxheight(r);break;case 3:var r=t.readInt32();e.setFps(r);break;case 4:var r=t.readInt32();e.setFrames(r);break;default:t.skipField()}return e},proto.com.opensource.svga.MovieParams.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.MovieParams.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.MovieParams.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getViewboxwidth(),0!==r&&t.writeFloat(1,r),r=e.getViewboxheight(),0!==r&&t.writeFloat(2,r),r=e.getFps(),0!==r&&t.writeInt32(3,r),0!==(r=e.getFrames())&&t.writeInt32(4,r)},proto.com.opensource.svga.MovieParams.prototype.getViewboxwidth=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.MovieParams.prototype.setViewboxwidth=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.MovieParams.prototype.getViewboxheight=function(){return+o.Message.getFieldWithDefault(this,2,0)},proto.com.opensource.svga.MovieParams.prototype.setViewboxheight=function(e){o.Message.setField(this,2,e)},proto.com.opensource.svga.MovieParams.prototype.getFps=function(){return o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.MovieParams.prototype.setFps=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.MovieParams.prototype.getFrames=function(){return o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.MovieParams.prototype.setFrames=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.SpriteEntity=function(e){o.Message.initialize(this,e,0,-1,proto.com.opensource.svga.SpriteEntity.repeatedFields_,null)},n.inherits(proto.com.opensource.svga.SpriteEntity,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.SpriteEntity.displayName="proto.com.opensource.svga.SpriteEntity"),proto.com.opensource.svga.SpriteEntity.repeatedFields_=[2],o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.SpriteEntity.prototype.toObject=function(e){return proto.com.opensource.svga.SpriteEntity.toObject(e,this)},proto.com.opensource.svga.SpriteEntity.toObject=function(e,t){var r={imagekey:o.Message.getFieldWithDefault(t,1,""),framesList:o.Message.toObjectList(t.getFramesList(),proto.com.opensource.svga.FrameEntity.toObject,e)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.SpriteEntity.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.SpriteEntity;return proto.com.opensource.svga.SpriteEntity.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.SpriteEntity.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readString();e.setImagekey(r);break;case 2:var r=new proto.com.opensource.svga.FrameEntity;t.readMessage(r,proto.com.opensource.svga.FrameEntity.deserializeBinaryFromReader),e.addFrames(r);break;default:t.skipField()}return e},proto.com.opensource.svga.SpriteEntity.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.SpriteEntity.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.SpriteEntity.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getImagekey(),r.length>0&&t.writeString(1,r),r=e.getFramesList(),r.length>0&&t.writeRepeatedMessage(2,r,proto.com.opensource.svga.FrameEntity.serializeBinaryToWriter)},proto.com.opensource.svga.SpriteEntity.prototype.getImagekey=function(){return o.Message.getFieldWithDefault(this,1,"")},proto.com.opensource.svga.SpriteEntity.prototype.setImagekey=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.SpriteEntity.prototype.getFramesList=function(){return o.Message.getRepeatedWrapperField(this,proto.com.opensource.svga.FrameEntity,2)},proto.com.opensource.svga.SpriteEntity.prototype.setFramesList=function(e){o.Message.setRepeatedWrapperField(this,2,e)},proto.com.opensource.svga.SpriteEntity.prototype.addFrames=function(e,t){return o.Message.addToRepeatedWrapperField(this,2,e,proto.com.opensource.svga.FrameEntity,t)},proto.com.opensource.svga.SpriteEntity.prototype.clearFramesList=function(){this.setFramesList([])},proto.com.opensource.svga.Layout=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.Layout,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.Layout.displayName="proto.com.opensource.svga.Layout"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.Layout.prototype.toObject=function(e){return proto.com.opensource.svga.Layout.toObject(e,this)},proto.com.opensource.svga.Layout.toObject=function(e,t){var r={x:+o.Message.getFieldWithDefault(t,1,0),y:+o.Message.getFieldWithDefault(t,2,0),width:+o.Message.getFieldWithDefault(t,3,0),height:+o.Message.getFieldWithDefault(t,4,0)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.Layout.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.Layout;return proto.com.opensource.svga.Layout.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.Layout.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setX(r);break;case 2:var r=t.readFloat();e.setY(r);break;case 3:var r=t.readFloat();e.setWidth(r);break;case 4:var r=t.readFloat();e.setHeight(r);break;default:t.skipField()}return e},proto.com.opensource.svga.Layout.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.Layout.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.Layout.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getX(),0!==r&&t.writeFloat(1,r),r=e.getY(),0!==r&&t.writeFloat(2,r),r=e.getWidth(),0!==r&&t.writeFloat(3,r),0!==(r=e.getHeight())&&t.writeFloat(4,r)},proto.com.opensource.svga.Layout.prototype.getX=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.Layout.prototype.setX=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.Layout.prototype.getY=function(){return+o.Message.getFieldWithDefault(this,2,0)},proto.com.opensource.svga.Layout.prototype.setY=function(e){o.Message.setField(this,2,e)},proto.com.opensource.svga.Layout.prototype.getWidth=function(){return+o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.Layout.prototype.setWidth=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.Layout.prototype.getHeight=function(){return+o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.Layout.prototype.setHeight=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.Transform=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.Transform,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.Transform.displayName="proto.com.opensource.svga.Transform"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.Transform.prototype.toObject=function(e){return proto.com.opensource.svga.Transform.toObject(e,this)},proto.com.opensource.svga.Transform.toObject=function(e,t){var r={a:+o.Message.getFieldWithDefault(t,1,0),b:+o.Message.getFieldWithDefault(t,2,0),c:+o.Message.getFieldWithDefault(t,3,0),d:+o.Message.getFieldWithDefault(t,4,0),tx:+o.Message.getFieldWithDefault(t,5,0),ty:+o.Message.getFieldWithDefault(t,6,0)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.Transform.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.Transform;return proto.com.opensource.svga.Transform.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.Transform.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setA(r);break;case 2:var r=t.readFloat();e.setB(r);break;case 3:var r=t.readFloat();e.setC(r);break;case 4:var r=t.readFloat();e.setD(r);break;case 5:var r=t.readFloat();e.setTx(r);break;case 6:var r=t.readFloat();e.setTy(r);break;default:t.skipField()}return e},proto.com.opensource.svga.Transform.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.Transform.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.Transform.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getA(),0!==r&&t.writeFloat(1,r),r=e.getB(),0!==r&&t.writeFloat(2,r),r=e.getC(),0!==r&&t.writeFloat(3,r),r=e.getD(),0!==r&&t.writeFloat(4,r),r=e.getTx(),0!==r&&t.writeFloat(5,r),0!==(r=e.getTy())&&t.writeFloat(6,r)},proto.com.opensource.svga.Transform.prototype.getA=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.Transform.prototype.setA=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.Transform.prototype.getB=function(){return+o.Message.getFieldWithDefault(this,2,0)},proto.com.opensource.svga.Transform.prototype.setB=function(e){o.Message.setField(this,2,e)},proto.com.opensource.svga.Transform.prototype.getC=function(){return+o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.Transform.prototype.setC=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.Transform.prototype.getD=function(){return+o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.Transform.prototype.setD=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.Transform.prototype.getTx=function(){return+o.Message.getFieldWithDefault(this,5,0)},proto.com.opensource.svga.Transform.prototype.setTx=function(e){o.Message.setField(this,5,e)},proto.com.opensource.svga.Transform.prototype.getTy=function(){return+o.Message.getFieldWithDefault(this,6,0)},proto.com.opensource.svga.Transform.prototype.setTy=function(e){o.Message.setField(this,6,e)},proto.com.opensource.svga.ShapeEntity=function(e){o.Message.initialize(this,e,0,-1,null,proto.com.opensource.svga.ShapeEntity.oneofGroups_)},n.inherits(proto.com.opensource.svga.ShapeEntity,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.ShapeEntity.displayName="proto.com.opensource.svga.ShapeEntity"),proto.com.opensource.svga.ShapeEntity.oneofGroups_=[[2,3,4]],proto.com.opensource.svga.ShapeEntity.ArgsCase={ARGS_NOT_SET:0,SHAPE:2,RECT:3,ELLIPSE:4},proto.com.opensource.svga.ShapeEntity.prototype.getArgsCase=function(){return o.Message.computeOneofCase(this,proto.com.opensource.svga.ShapeEntity.oneofGroups_[0])},o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.ShapeEntity.prototype.toObject=function(e){return proto.com.opensource.svga.ShapeEntity.toObject(e,this)},proto.com.opensource.svga.ShapeEntity.toObject=function(e,t){var r,n={type:o.Message.getFieldWithDefault(t,1,0),shape:(r=t.getShape())&&proto.com.opensource.svga.ShapeEntity.ShapeArgs.toObject(e,r),rect:(r=t.getRect())&&proto.com.opensource.svga.ShapeEntity.RectArgs.toObject(e,r),ellipse:(r=t.getEllipse())&&proto.com.opensource.svga.ShapeEntity.EllipseArgs.toObject(e,r),styles:(r=t.getStyles())&&proto.com.opensource.svga.ShapeEntity.ShapeStyle.toObject(e,r),transform:(r=t.getTransform())&&proto.com.opensource.svga.Transform.toObject(e,r)};return e&&(n.$jspbMessageInstance=t),n}),proto.com.opensource.svga.ShapeEntity.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.ShapeEntity;return proto.com.opensource.svga.ShapeEntity.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.ShapeEntity.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readEnum();e.setType(r);break;case 2:var r=new proto.com.opensource.svga.ShapeEntity.ShapeArgs;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.ShapeArgs.deserializeBinaryFromReader),e.setShape(r);break;case 3:var r=new proto.com.opensource.svga.ShapeEntity.RectArgs;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.RectArgs.deserializeBinaryFromReader),e.setRect(r);break;case 4:var r=new proto.com.opensource.svga.ShapeEntity.EllipseArgs;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.EllipseArgs.deserializeBinaryFromReader),e.setEllipse(r);break;case 10:var r=new proto.com.opensource.svga.ShapeEntity.ShapeStyle;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.ShapeStyle.deserializeBinaryFromReader),e.setStyles(r);break;case 11:var r=new proto.com.opensource.svga.Transform;t.readMessage(r,proto.com.opensource.svga.Transform.deserializeBinaryFromReader),e.setTransform(r);break;default:t.skipField()}return e},proto.com.opensource.svga.ShapeEntity.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.ShapeEntity.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.ShapeEntity.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getType(),0!==r&&t.writeEnum(1,r),r=e.getShape(),null!=r&&t.writeMessage(2,r,proto.com.opensource.svga.ShapeEntity.ShapeArgs.serializeBinaryToWriter),r=e.getRect(),null!=r&&t.writeMessage(3,r,proto.com.opensource.svga.ShapeEntity.RectArgs.serializeBinaryToWriter),r=e.getEllipse(),null!=r&&t.writeMessage(4,r,proto.com.opensource.svga.ShapeEntity.EllipseArgs.serializeBinaryToWriter),r=e.getStyles(),null!=r&&t.writeMessage(10,r,proto.com.opensource.svga.ShapeEntity.ShapeStyle.serializeBinaryToWriter),null!=(r=e.getTransform())&&t.writeMessage(11,r,proto.com.opensource.svga.Transform.serializeBinaryToWriter)},proto.com.opensource.svga.ShapeEntity.ShapeType={SHAPE:0,RECT:1,ELLIPSE:2,KEEP:3},proto.com.opensource.svga.ShapeEntity.ShapeArgs=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.ShapeEntity.ShapeArgs,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.ShapeEntity.ShapeArgs.displayName="proto.com.opensource.svga.ShapeEntity.ShapeArgs"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.ShapeEntity.ShapeArgs.prototype.toObject=function(e){return proto.com.opensource.svga.ShapeEntity.ShapeArgs.toObject(e,this)},proto.com.opensource.svga.ShapeEntity.ShapeArgs.toObject=function(e,t){var r={d:o.Message.getFieldWithDefault(t,1,"")};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.ShapeEntity.ShapeArgs.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.ShapeEntity.ShapeArgs;return proto.com.opensource.svga.ShapeEntity.ShapeArgs.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.ShapeEntity.ShapeArgs.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readString();e.setD(r);break;default:t.skipField()}return e},proto.com.opensource.svga.ShapeEntity.ShapeArgs.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.ShapeEntity.ShapeArgs.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.ShapeEntity.ShapeArgs.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getD(),r.length>0&&t.writeString(1,r)},proto.com.opensource.svga.ShapeEntity.ShapeArgs.prototype.getD=function(){return o.Message.getFieldWithDefault(this,1,"")},proto.com.opensource.svga.ShapeEntity.ShapeArgs.prototype.setD=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.ShapeEntity.RectArgs=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.ShapeEntity.RectArgs,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.ShapeEntity.RectArgs.displayName="proto.com.opensource.svga.ShapeEntity.RectArgs"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.toObject=function(e){return proto.com.opensource.svga.ShapeEntity.RectArgs.toObject(e,this)},proto.com.opensource.svga.ShapeEntity.RectArgs.toObject=function(e,t){var r={x:+o.Message.getFieldWithDefault(t,1,0),y:+o.Message.getFieldWithDefault(t,2,0),width:+o.Message.getFieldWithDefault(t,3,0),height:+o.Message.getFieldWithDefault(t,4,0),cornerradius:+o.Message.getFieldWithDefault(t,5,0)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.ShapeEntity.RectArgs.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.ShapeEntity.RectArgs;return proto.com.opensource.svga.ShapeEntity.RectArgs.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.ShapeEntity.RectArgs.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setX(r);break;case 2:var r=t.readFloat();e.setY(r);break;case 3:var r=t.readFloat();e.setWidth(r);break;case 4:var r=t.readFloat();e.setHeight(r);break;case 5:var r=t.readFloat();e.setCornerradius(r);break;default:t.skipField()}return e},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.ShapeEntity.RectArgs.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.ShapeEntity.RectArgs.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getX(),0!==r&&t.writeFloat(1,r),r=e.getY(),0!==r&&t.writeFloat(2,r),r=e.getWidth(),0!==r&&t.writeFloat(3,r),r=e.getHeight(),0!==r&&t.writeFloat(4,r),0!==(r=e.getCornerradius())&&t.writeFloat(5,r)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.getX=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.setX=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.getY=function(){return+o.Message.getFieldWithDefault(this,2,0)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.setY=function(e){o.Message.setField(this,2,e)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.getWidth=function(){return+o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.setWidth=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.getHeight=function(){return+o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.setHeight=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.getCornerradius=function(){return+o.Message.getFieldWithDefault(this,5,0)},proto.com.opensource.svga.ShapeEntity.RectArgs.prototype.setCornerradius=function(e){o.Message.setField(this,5,e)},proto.com.opensource.svga.ShapeEntity.EllipseArgs=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.ShapeEntity.EllipseArgs,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.ShapeEntity.EllipseArgs.displayName="proto.com.opensource.svga.ShapeEntity.EllipseArgs"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.toObject=function(e){return proto.com.opensource.svga.ShapeEntity.EllipseArgs.toObject(e,this)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.toObject=function(e,t){var r={x:+o.Message.getFieldWithDefault(t,1,0),y:+o.Message.getFieldWithDefault(t,2,0),radiusx:+o.Message.getFieldWithDefault(t,3,0),radiusy:+o.Message.getFieldWithDefault(t,4,0)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.ShapeEntity.EllipseArgs.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.ShapeEntity.EllipseArgs;return proto.com.opensource.svga.ShapeEntity.EllipseArgs.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setX(r);break;case 2:var r=t.readFloat();e.setY(r);break;case 3:var r=t.readFloat();e.setRadiusx(r);break;case 4:var r=t.readFloat();e.setRadiusy(r);break;default:t.skipField()}return e},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.ShapeEntity.EllipseArgs.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.ShapeEntity.EllipseArgs.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getX(),0!==r&&t.writeFloat(1,r),r=e.getY(),0!==r&&t.writeFloat(2,r),r=e.getRadiusx(),0!==r&&t.writeFloat(3,r),0!==(r=e.getRadiusy())&&t.writeFloat(4,r)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.getX=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.setX=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.getY=function(){return+o.Message.getFieldWithDefault(this,2,0)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.setY=function(e){o.Message.setField(this,2,e)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.getRadiusx=function(){return+o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.setRadiusx=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.getRadiusy=function(){return+o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.ShapeEntity.EllipseArgs.prototype.setRadiusy=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.ShapeEntity.ShapeStyle,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.ShapeEntity.ShapeStyle.displayName="proto.com.opensource.svga.ShapeEntity.ShapeStyle"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.toObject=function(e){return proto.com.opensource.svga.ShapeEntity.ShapeStyle.toObject(e,this)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.toObject=function(e,t){var r,n={fill:(r=t.getFill())&&proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.toObject(e,r),stroke:(r=t.getStroke())&&proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.toObject(e,r),strokewidth:+o.Message.getFieldWithDefault(t,3,0),linecap:o.Message.getFieldWithDefault(t,4,0),linejoin:o.Message.getFieldWithDefault(t,5,0),miterlimit:+o.Message.getFieldWithDefault(t,6,0),linedashi:+o.Message.getFieldWithDefault(t,7,0),linedashii:+o.Message.getFieldWithDefault(t,8,0),linedashiii:+o.Message.getFieldWithDefault(t,9,0)};return e&&(n.$jspbMessageInstance=t),n}),proto.com.opensource.svga.ShapeEntity.ShapeStyle.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.ShapeEntity.ShapeStyle;return proto.com.opensource.svga.ShapeEntity.ShapeStyle.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=new proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.deserializeBinaryFromReader),e.setFill(r);break;case 2:var r=new proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.deserializeBinaryFromReader),e.setStroke(r);break;case 3:var r=t.readFloat();e.setStrokewidth(r);break;case 4:var r=t.readEnum();e.setLinecap(r);break;case 5:var r=t.readEnum();e.setLinejoin(r);break;case 6:var r=t.readFloat();e.setMiterlimit(r);break;case 7:var r=t.readFloat();e.setLinedashi(r);break;case 8:var r=t.readFloat();e.setLinedashii(r);break;case 9:var r=t.readFloat();e.setLinedashiii(r);break;default:t.skipField()}return e},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.ShapeEntity.ShapeStyle.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.ShapeEntity.ShapeStyle.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getFill(),null!=r&&t.writeMessage(1,r,proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.serializeBinaryToWriter),r=e.getStroke(),null!=r&&t.writeMessage(2,r,proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.serializeBinaryToWriter),r=e.getStrokewidth(),0!==r&&t.writeFloat(3,r),r=e.getLinecap(),0!==r&&t.writeEnum(4,r),r=e.getLinejoin(),0!==r&&t.writeEnum(5,r),r=e.getMiterlimit(),0!==r&&t.writeFloat(6,r),r=e.getLinedashi(),0!==r&&t.writeFloat(7,r),r=e.getLinedashii(),0!==r&&t.writeFloat(8,r),0!==(r=e.getLinedashiii())&&t.writeFloat(9,r)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.LineCap={LINECAP_BUTT:0,LINECAP_ROUND:1,LINECAP_SQUARE:2},proto.com.opensource.svga.ShapeEntity.ShapeStyle.LineJoin={LINEJOIN_MITER:0,LINEJOIN_ROUND:1,LINEJOIN_BEVEL:2},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor=function(e){o.Message.initialize(this,e,0,-1,null,null)},n.inherits(proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.displayName="proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor"),o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.toObject=function(e){return proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.toObject(e,this)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.toObject=function(e,t){var r={r:+o.Message.getFieldWithDefault(t,1,0),g:+o.Message.getFieldWithDefault(t,2,0),b:+o.Message.getFieldWithDefault(t,3,0),a:+o.Message.getFieldWithDefault(t,4,0)};return e&&(r.$jspbMessageInstance=t),r}),proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor;return proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setR(r);break;case 2:var r=t.readFloat();e.setG(r);break;case 3:var r=t.readFloat();e.setB(r);break;case 4:var r=t.readFloat();e.setA(r);break;default:t.skipField()}return e},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getR(),0!==r&&t.writeFloat(1,r),r=e.getG(),0!==r&&t.writeFloat(2,r),r=e.getB(),0!==r&&t.writeFloat(3,r),0!==(r=e.getA())&&t.writeFloat(4,r)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.getR=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.setR=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.getG=function(){return+o.Message.getFieldWithDefault(this,2,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.setG=function(e){o.Message.setField(this,2,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.getB=function(){return+o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.setB=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.getA=function(){return+o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor.prototype.setA=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getFill=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor,1)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setFill=function(e){o.Message.setWrapperField(this,1,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.clearFill=function(){this.setFill(void 0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.hasFill=function(){return null!=o.Message.getField(this,1)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getStroke=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.ShapeEntity.ShapeStyle.RGBAColor,2)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setStroke=function(e){o.Message.setWrapperField(this,2,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.clearStroke=function(){this.setStroke(void 0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.hasStroke=function(){return null!=o.Message.getField(this,2)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getStrokewidth=function(){return+o.Message.getFieldWithDefault(this,3,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setStrokewidth=function(e){o.Message.setField(this,3,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getLinecap=function(){return o.Message.getFieldWithDefault(this,4,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setLinecap=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getLinejoin=function(){return o.Message.getFieldWithDefault(this,5,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setLinejoin=function(e){o.Message.setField(this,5,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getMiterlimit=function(){return+o.Message.getFieldWithDefault(this,6,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setMiterlimit=function(e){o.Message.setField(this,6,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getLinedashi=function(){return+o.Message.getFieldWithDefault(this,7,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setLinedashi=function(e){o.Message.setField(this,7,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getLinedashii=function(){return+o.Message.getFieldWithDefault(this,8,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setLinedashii=function(e){o.Message.setField(this,8,e)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.getLinedashiii=function(){return+o.Message.getFieldWithDefault(this,9,0)},proto.com.opensource.svga.ShapeEntity.ShapeStyle.prototype.setLinedashiii=function(e){o.Message.setField(this,9,e)},proto.com.opensource.svga.ShapeEntity.prototype.getType=function(){return o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.ShapeEntity.prototype.setType=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.ShapeEntity.prototype.getShape=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.ShapeEntity.ShapeArgs,2)},proto.com.opensource.svga.ShapeEntity.prototype.setShape=function(e){o.Message.setOneofWrapperField(this,2,proto.com.opensource.svga.ShapeEntity.oneofGroups_[0],e)},proto.com.opensource.svga.ShapeEntity.prototype.clearShape=function(){this.setShape(void 0)},proto.com.opensource.svga.ShapeEntity.prototype.hasShape=function(){return null!=o.Message.getField(this,2)},proto.com.opensource.svga.ShapeEntity.prototype.getRect=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.ShapeEntity.RectArgs,3)},proto.com.opensource.svga.ShapeEntity.prototype.setRect=function(e){o.Message.setOneofWrapperField(this,3,proto.com.opensource.svga.ShapeEntity.oneofGroups_[0],e)},proto.com.opensource.svga.ShapeEntity.prototype.clearRect=function(){this.setRect(void 0)},proto.com.opensource.svga.ShapeEntity.prototype.hasRect=function(){return null!=o.Message.getField(this,3)},proto.com.opensource.svga.ShapeEntity.prototype.getEllipse=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.ShapeEntity.EllipseArgs,4)},proto.com.opensource.svga.ShapeEntity.prototype.setEllipse=function(e){o.Message.setOneofWrapperField(this,4,proto.com.opensource.svga.ShapeEntity.oneofGroups_[0],e)},proto.com.opensource.svga.ShapeEntity.prototype.clearEllipse=function(){
this.setEllipse(void 0)},proto.com.opensource.svga.ShapeEntity.prototype.hasEllipse=function(){return null!=o.Message.getField(this,4)},proto.com.opensource.svga.ShapeEntity.prototype.getStyles=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.ShapeEntity.ShapeStyle,10)},proto.com.opensource.svga.ShapeEntity.prototype.setStyles=function(e){o.Message.setWrapperField(this,10,e)},proto.com.opensource.svga.ShapeEntity.prototype.clearStyles=function(){this.setStyles(void 0)},proto.com.opensource.svga.ShapeEntity.prototype.hasStyles=function(){return null!=o.Message.getField(this,10)},proto.com.opensource.svga.ShapeEntity.prototype.getTransform=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.Transform,11)},proto.com.opensource.svga.ShapeEntity.prototype.setTransform=function(e){o.Message.setWrapperField(this,11,e)},proto.com.opensource.svga.ShapeEntity.prototype.clearTransform=function(){this.setTransform(void 0)},proto.com.opensource.svga.ShapeEntity.prototype.hasTransform=function(){return null!=o.Message.getField(this,11)},proto.com.opensource.svga.FrameEntity=function(e){o.Message.initialize(this,e,0,-1,proto.com.opensource.svga.FrameEntity.repeatedFields_,null)},n.inherits(proto.com.opensource.svga.FrameEntity,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.FrameEntity.displayName="proto.com.opensource.svga.FrameEntity"),proto.com.opensource.svga.FrameEntity.repeatedFields_=[5],o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.FrameEntity.prototype.toObject=function(e){return proto.com.opensource.svga.FrameEntity.toObject(e,this)},proto.com.opensource.svga.FrameEntity.toObject=function(e,t){var r,n={alpha:+o.Message.getFieldWithDefault(t,1,0),layout:(r=t.getLayout())&&proto.com.opensource.svga.Layout.toObject(e,r),transform:(r=t.getTransform())&&proto.com.opensource.svga.Transform.toObject(e,r),clippath:o.Message.getFieldWithDefault(t,4,""),shapesList:o.Message.toObjectList(t.getShapesList(),proto.com.opensource.svga.ShapeEntity.toObject,e)};return e&&(n.$jspbMessageInstance=t),n}),proto.com.opensource.svga.FrameEntity.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.FrameEntity;return proto.com.opensource.svga.FrameEntity.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.FrameEntity.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readFloat();e.setAlpha(r);break;case 2:var r=new proto.com.opensource.svga.Layout;t.readMessage(r,proto.com.opensource.svga.Layout.deserializeBinaryFromReader),e.setLayout(r);break;case 3:var r=new proto.com.opensource.svga.Transform;t.readMessage(r,proto.com.opensource.svga.Transform.deserializeBinaryFromReader),e.setTransform(r);break;case 4:var r=t.readString();e.setClippath(r);break;case 5:var r=new proto.com.opensource.svga.ShapeEntity;t.readMessage(r,proto.com.opensource.svga.ShapeEntity.deserializeBinaryFromReader),e.addShapes(r);break;default:t.skipField()}return e},proto.com.opensource.svga.FrameEntity.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.FrameEntity.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.FrameEntity.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getAlpha(),0!==r&&t.writeFloat(1,r),r=e.getLayout(),null!=r&&t.writeMessage(2,r,proto.com.opensource.svga.Layout.serializeBinaryToWriter),r=e.getTransform(),null!=r&&t.writeMessage(3,r,proto.com.opensource.svga.Transform.serializeBinaryToWriter),r=e.getClippath(),r.length>0&&t.writeString(4,r),r=e.getShapesList(),r.length>0&&t.writeRepeatedMessage(5,r,proto.com.opensource.svga.ShapeEntity.serializeBinaryToWriter)},proto.com.opensource.svga.FrameEntity.prototype.getAlpha=function(){return+o.Message.getFieldWithDefault(this,1,0)},proto.com.opensource.svga.FrameEntity.prototype.setAlpha=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.FrameEntity.prototype.getLayout=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.Layout,2)},proto.com.opensource.svga.FrameEntity.prototype.setLayout=function(e){o.Message.setWrapperField(this,2,e)},proto.com.opensource.svga.FrameEntity.prototype.clearLayout=function(){this.setLayout(void 0)},proto.com.opensource.svga.FrameEntity.prototype.hasLayout=function(){return null!=o.Message.getField(this,2)},proto.com.opensource.svga.FrameEntity.prototype.getTransform=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.Transform,3)},proto.com.opensource.svga.FrameEntity.prototype.setTransform=function(e){o.Message.setWrapperField(this,3,e)},proto.com.opensource.svga.FrameEntity.prototype.clearTransform=function(){this.setTransform(void 0)},proto.com.opensource.svga.FrameEntity.prototype.hasTransform=function(){return null!=o.Message.getField(this,3)},proto.com.opensource.svga.FrameEntity.prototype.getClippath=function(){return o.Message.getFieldWithDefault(this,4,"")},proto.com.opensource.svga.FrameEntity.prototype.setClippath=function(e){o.Message.setField(this,4,e)},proto.com.opensource.svga.FrameEntity.prototype.getShapesList=function(){return o.Message.getRepeatedWrapperField(this,proto.com.opensource.svga.ShapeEntity,5)},proto.com.opensource.svga.FrameEntity.prototype.setShapesList=function(e){o.Message.setRepeatedWrapperField(this,5,e)},proto.com.opensource.svga.FrameEntity.prototype.addShapes=function(e,t){return o.Message.addToRepeatedWrapperField(this,5,e,proto.com.opensource.svga.ShapeEntity,t)},proto.com.opensource.svga.FrameEntity.prototype.clearShapesList=function(){this.setShapesList([])},proto.com.opensource.svga.MovieEntity=function(e){o.Message.initialize(this,e,0,-1,proto.com.opensource.svga.MovieEntity.repeatedFields_,null)},n.inherits(proto.com.opensource.svga.MovieEntity,o.Message),n.DEBUG&&!COMPILED&&(proto.com.opensource.svga.MovieEntity.displayName="proto.com.opensource.svga.MovieEntity"),proto.com.opensource.svga.MovieEntity.repeatedFields_=[4],o.Message.GENERATE_TO_OBJECT&&(proto.com.opensource.svga.MovieEntity.prototype.toObject=function(e){return proto.com.opensource.svga.MovieEntity.toObject(e,this)},proto.com.opensource.svga.MovieEntity.toObject=function(e,t){var r,n={version:o.Message.getFieldWithDefault(t,1,""),params:(r=t.getParams())&&proto.com.opensource.svga.MovieParams.toObject(e,r),imagesMap:(r=t.getImagesMap())?r.toObject(e,void 0):[],spritesList:o.Message.toObjectList(t.getSpritesList(),proto.com.opensource.svga.SpriteEntity.toObject,e)};return e&&(n.$jspbMessageInstance=t),n}),proto.com.opensource.svga.MovieEntity.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new proto.com.opensource.svga.MovieEntity;return proto.com.opensource.svga.MovieEntity.deserializeBinaryFromReader(r,t)},proto.com.opensource.svga.MovieEntity.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readString();e.setVersion(r);break;case 2:var r=new proto.com.opensource.svga.MovieParams;t.readMessage(r,proto.com.opensource.svga.MovieParams.deserializeBinaryFromReader),e.setParams(r);break;case 3:var r=e.getImagesMap();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readBytes)});break;case 4:var r=new proto.com.opensource.svga.SpriteEntity;t.readMessage(r,proto.com.opensource.svga.SpriteEntity.deserializeBinaryFromReader),e.addSprites(r);break;default:t.skipField()}return e},proto.com.opensource.svga.MovieEntity.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return proto.com.opensource.svga.MovieEntity.serializeBinaryToWriter(this,e),e.getResultBuffer()},proto.com.opensource.svga.MovieEntity.serializeBinaryToWriter=function(e,t){var r=void 0;r=e.getVersion(),r.length>0&&t.writeString(1,r),r=e.getParams(),null!=r&&t.writeMessage(2,r,proto.com.opensource.svga.MovieParams.serializeBinaryToWriter),r=e.getImagesMap(!0),r&&r.getLength()>0&&r.serializeBinary(3,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeBytes),r=e.getSpritesList(),r.length>0&&t.writeRepeatedMessage(4,r,proto.com.opensource.svga.SpriteEntity.serializeBinaryToWriter)},proto.com.opensource.svga.MovieEntity.prototype.getVersion=function(){return o.Message.getFieldWithDefault(this,1,"")},proto.com.opensource.svga.MovieEntity.prototype.setVersion=function(e){o.Message.setField(this,1,e)},proto.com.opensource.svga.MovieEntity.prototype.getParams=function(){return o.Message.getWrapperField(this,proto.com.opensource.svga.MovieParams,2)},proto.com.opensource.svga.MovieEntity.prototype.setParams=function(e){o.Message.setWrapperField(this,2,e)},proto.com.opensource.svga.MovieEntity.prototype.clearParams=function(){this.setParams(void 0)},proto.com.opensource.svga.MovieEntity.prototype.hasParams=function(){return null!=o.Message.getField(this,2)},proto.com.opensource.svga.MovieEntity.prototype.getImagesMap=function(e){return o.Message.getMapField(this,3,e,null)},proto.com.opensource.svga.MovieEntity.prototype.clearImagesMap=function(){this.getImagesMap().clear()},proto.com.opensource.svga.MovieEntity.prototype.getSpritesList=function(){return o.Message.getRepeatedWrapperField(this,proto.com.opensource.svga.SpriteEntity,4)},proto.com.opensource.svga.MovieEntity.prototype.setSpritesList=function(e){o.Message.setRepeatedWrapperField(this,4,e)},proto.com.opensource.svga.MovieEntity.prototype.addSprites=function(e,t){return o.Message.addToRepeatedWrapperField(this,4,e,proto.com.opensource.svga.SpriteEntity,t)},proto.com.opensource.svga.MovieEntity.prototype.clearSpritesList=function(){this.setSpritesList([])},n.object.extend(t,proto.com.opensource.svga)},function(module,exports,__webpack_require__){(function(global){var $jscomp={scope:{},getGlobal:function e(t){return"undefined"!=typeof window&&window===t?t:void 0!==global?global:t}};$jscomp.global=$jscomp.getGlobal(this),$jscomp.initSymbol=function(){$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol),$jscomp.initSymbol=function(){}},$jscomp.symbolCounter_=0,$jscomp.Symbol=function(e){return"jscomp_symbol_"+e+$jscomp.symbolCounter_++},$jscomp.initSymbolIterator=function(){$jscomp.initSymbol(),$jscomp.global.Symbol.iterator||($jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator")),$jscomp.initSymbolIterator=function(){}},$jscomp.makeIterator=function(e){$jscomp.initSymbolIterator(),$jscomp.initSymbol(),$jscomp.initSymbolIterator();var t=e[_iterator2["default"]];if(t)return t.call(e);var r=0;return{next:function o(){return r<e.length?{done:!1,value:e[r++]}:{done:!0}}}},$jscomp.arrayFromIterator=function(e){for(var t,r=[];!(t=e.next()).done;)r.push(t.value);return r},$jscomp.arrayFromIterable=function(e){return e instanceof Array?e:$jscomp.arrayFromIterator($jscomp.makeIterator(e))},$jscomp.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e;for(var o in t)if(_defineProperties2["default"]){var n=(0,_getOwnPropertyDescriptor2["default"])(t,o);n&&(0,_defineProperty2["default"])(e,o,n)}else e[o]=t[o]},$jscomp.array=$jscomp.array||{},$jscomp.iteratorFromArray=function(e,t){$jscomp.initSymbolIterator(),e instanceof String&&(e+="");var r=0,o={next:function n(){if(r<e.length){var n=r++;return{value:t(n,e[n]),done:!1}}return o.next=function(){return{done:!0,value:void 0}},o.next()}};return $jscomp.initSymbol(),$jscomp.initSymbolIterator(),o[_iterator2["default"]]=function(){return o},o},$jscomp.findInternal=function(e,t,r){e instanceof String&&(e=String(e));for(var o=e.length,n=0;n<o;n++){var i=e[n];if(t.call(r,i,n,e))return{i:n,v:i}}return{i:-1,v:void 0}},$jscomp.array.from=function(e,t,r){$jscomp.initSymbolIterator(),t=null!=t?t:function(e){return e};var o=[];$jscomp.initSymbol(),$jscomp.initSymbolIterator();var n=e[_iterator2["default"]];if("function"==typeof n&&(e=n.call(e)),"function"==typeof e.next)for(;!(n=e.next()).done;)o.push(t.call(r,n.value));else for(var n=e.length,i=0;i<n;i++)o.push(t.call(r,e[i]));return o},$jscomp.array.of=function(e){return $jscomp.array.from(arguments)},$jscomp.array.entries=function(){return $jscomp.iteratorFromArray(this,function(e,t){return[e,t]})},$jscomp.array.installHelper_=function(e,t){!Array.prototype[e]&&_defineProperties2["default"]&&_defineProperty2["default"]&&(0,_defineProperty2["default"])(Array.prototype,e,{configurable:!0,enumerable:!1,writable:!0,value:t})},$jscomp.array.entries$install=function(){$jscomp.array.installHelper_("entries",$jscomp.array.entries)},$jscomp.array.keys=function(){return $jscomp.iteratorFromArray(this,function(e){return e})},$jscomp.array.keys$install=function(){$jscomp.array.installHelper_("keys",$jscomp.array.keys)},$jscomp.array.values=function(){return $jscomp.iteratorFromArray(this,function(e,t){return t})},$jscomp.array.values$install=function(){$jscomp.array.installHelper_("values",$jscomp.array.values)},$jscomp.array.copyWithin=function(e,t,r){var o=this.length;if(e=Number(e),t=Number(t),r=Number(null!=r?r:o),e<t)for(r=Math.min(r,o);t<r;)t in this?this[e++]=this[t++]:(delete this[e++],t++);else for(r=Math.min(r,o+t-e),e+=r-t;r>t;)--r in this?this[--e]=this[r]:delete this[e];return this},$jscomp.array.copyWithin$install=function(){$jscomp.array.installHelper_("copyWithin",$jscomp.array.copyWithin)},$jscomp.array.fill=function(e,t,r){var o=this.length||0;for(0>t&&(t=Math.max(0,o+t)),(null==r||r>o)&&(r=o),r=Number(r),0>r&&(r=Math.max(0,o+r)),t=Number(t||0);t<r;t++)this[t]=e;return this},$jscomp.array.fill$install=function(){$jscomp.array.installHelper_("fill",$jscomp.array.fill)},$jscomp.array.find=function(e,t){return $jscomp.findInternal(this,e,t).v},$jscomp.array.find$install=function(){$jscomp.array.installHelper_("find",$jscomp.array.find)},$jscomp.array.findIndex=function(e,t){return $jscomp.findInternal(this,e,t).i},$jscomp.array.findIndex$install=function(){$jscomp.array.installHelper_("findIndex",$jscomp.array.findIndex)},$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.Map$isConformant=function(){if($jscomp.ASSUME_NO_NATIVE_MAP)return!1;var e=$jscomp.global.Map;if(!e||!e.prototype.entries||"function"!=typeof _seal2["default"])return!1;try{var t=(0,_seal2["default"])({x:4}),r=new e($jscomp.makeIterator([[t,"s"]]));if("s"!=r.get(t)||1!=r.size||r.get({x:4})||r.set({x:4},"t")!=r||2!=r.size)return!1;var o=r.entries(),n=o.next();return!(n.done||n.value[0]!=t||"s"!=n.value[1]||(n=o.next(),n.done||4!=n.value[0].x||"t"!=n.value[1]||!o.next().done))}catch(e){return!1}},$jscomp.Map=function(e){if(this.data_={},this.head_=$jscomp.Map.createHead(),this.size=0,e){e=$jscomp.makeIterator(e);for(var t;!(t=e.next()).done;)t=t.value,this.set(t[0],t[1])}},$jscomp.Map.prototype.set=function(e,t){var r=$jscomp.Map.maybeGetEntry(this,e);return r.list||(r.list=this.data_[r.id]=[]),r.entry?r.entry.value=t:(r.entry={next:this.head_,previous:this.head_.previous,head:this.head_,key:e,value:t},r.list.push(r.entry),this.head_.previous.next=r.entry,this.head_.previous=r.entry,this.size++),this},$jscomp.Map.prototype["delete"]=function(e){return e=$jscomp.Map.maybeGetEntry(this,e),!(!e.entry||!e.list||(e.list.splice(e.index,1),e.list.length||delete this.data_[e.id],e.entry.previous.next=e.entry.next,e.entry.next.previous=e.entry.previous,e.entry.head=null,this.size--,0))},$jscomp.Map.prototype.clear=function(){this.data_={},this.head_=this.head_.previous=$jscomp.Map.createHead(),this.size=0},$jscomp.Map.prototype.has=function(e){return!!$jscomp.Map.maybeGetEntry(this,e).entry},$jscomp.Map.prototype.get=function(e){return(e=$jscomp.Map.maybeGetEntry(this,e).entry)&&e.value},$jscomp.Map.prototype.entries=function(){return $jscomp.Map.makeIterator_(this,function(e){return[e.key,e.value]})},$jscomp.Map.prototype.keys=function(){return $jscomp.Map.makeIterator_(this,function(e){return e.key})},$jscomp.Map.prototype.values=function(){return $jscomp.Map.makeIterator_(this,function(e){return e.value})},$jscomp.Map.prototype.forEach=function(e,t){for(var r,o=this.entries();!(r=o.next()).done;)r=r.value,e.call(t,r[1],r[0],this)},$jscomp.Map.maybeGetEntry=function(e,t){var r=$jscomp.Map.getId(t),o=e.data_[r];if(o&&Object.prototype.hasOwnProperty.call(e.data_,r))for(var n=0;n<o.length;n++){var i=o[n];if(t!==t&&i.key!==i.key||t===i.key)return{id:r,list:o,index:n,entry:i}}return{id:r,list:o,index:-1,entry:void 0}},$jscomp.Map.makeIterator_=function(e,t){var r=e.head_,o={next:function n(){if(r){for(;r.head!=e.head_;)r=r.previous;for(;r.next!=r.head;)return r=r.next,{done:!1,value:t(r)};r=null}return{done:!0,value:void 0}}};return $jscomp.initSymbol(),$jscomp.initSymbolIterator(),o[_iterator2["default"]]=function(){return o},o},$jscomp.Map.mapIndex_=0,$jscomp.Map.createHead=function(){var e={};return e.previous=e.next=e.head=e},$jscomp.Map.getId=function(e){if(!(e instanceof Object))return"p_"+e;if(!($jscomp.Map.idKey in e))try{$jscomp.Map.defineProperty(e,$jscomp.Map.idKey,{value:++$jscomp.Map.mapIndex_})}catch(e){}return $jscomp.Map.idKey in e?e[$jscomp.Map.idKey]:"o_ "+e},$jscomp.Map.defineProperty=_defineProperty2["default"]?function(e,t,r){(0,_defineProperty2["default"])(e,t,{value:String(r)})}:function(e,t,r){e[t]=String(r)},$jscomp.Map.Entry=function(){},$jscomp.Map$install=function(){$jscomp.initSymbol(),$jscomp.initSymbolIterator(),$jscomp.Map$isConformant()?$jscomp.Map=$jscomp.global.Map:($jscomp.initSymbol(),$jscomp.initSymbolIterator(),$jscomp.Map.prototype[_iterator2["default"]]=$jscomp.Map.prototype.entries,$jscomp.initSymbol(),$jscomp.Map.idKey=(0,_symbol2["default"])("map-id-key"),$jscomp.Map$install=function(){})},$jscomp.math=$jscomp.math||{},$jscomp.math.clz32=function(e){if(0===(e=Number(e)>>>0))return 32;var t=0;return 0==(4294901760&e)&&(e<<=16,t+=16),0==(4278190080&e)&&(e<<=8,t+=8),0==(4026531840&e)&&(e<<=4,t+=4),0==(3221225472&e)&&(e<<=2,t+=2),0==(2147483648&e)&&t++,t},$jscomp.math.imul=function(e,t){e=Number(e),t=Number(t);var r=65535&e,o=65535&t;return r*o+((e>>>16&65535)*o+r*(t>>>16&65535)<<16>>>0)|0},$jscomp.math.sign=function(e){return e=Number(e),0===e||isNaN(e)?e:0<e?1:-1},$jscomp.math.log10=function(e){return Math.log(e)/Math.LN10},$jscomp.math.log2=function(e){return Math.log(e)/Math.LN2},$jscomp.math.log1p=function(e){if(.25>(e=Number(e))&&-.25<e){for(var t=e,r=1,o=e,n=0,i=1;n!=o;)t*=e,i*=-1,o=(n=o)+i*t/++r;return o}return Math.log(1+e)},$jscomp.math.expm1=function(e){if(.25>(e=Number(e))&&-.25<e){for(var t=e,r=1,o=e,n=0;n!=o;)t*=e/++r,o=(n=o)+t;return o}return Math.exp(e)-1},$jscomp.math.cosh=function(e){return e=Number(e),(Math.exp(e)+Math.exp(-e))/2},$jscomp.math.sinh=function(e){return e=Number(e),0===e?e:(Math.exp(e)-Math.exp(-e))/2},$jscomp.math.tanh=function(e){if(0===(e=Number(e)))return e;var t=Math.exp(-2*Math.abs(e)),t=(1-t)/(1+t);return 0>e?-t:t},$jscomp.math.acosh=function(e){return e=Number(e),Math.log(e+Math.sqrt(e*e-1))},$jscomp.math.asinh=function(e){if(0===(e=Number(e)))return e;var t=Math.log(Math.abs(e)+Math.sqrt(e*e+1));return 0>e?-t:t},$jscomp.math.atanh=function(e){return e=Number(e),($jscomp.math.log1p(e)-$jscomp.math.log1p(-e))/2},$jscomp.math.hypot=function(e,t,r){e=Number(e),t=Number(t);var o,n,i,s=Math.max(Math.abs(e),Math.abs(t));for(o=2;o<arguments.length;o++)s=Math.max(s,Math.abs(arguments[o]));if(1e100<s||1e-100>s){for(e/=s,t/=s,i=e*e+t*t,o=2;o<arguments.length;o++)n=Number(arguments[o])/s,i+=n*n;return Math.sqrt(i)*s}for(i=e*e+t*t,o=2;o<arguments.length;o++)n=Number(arguments[o]),i+=n*n;return Math.sqrt(i)},$jscomp.math.trunc=function(e){if(e=Number(e),isNaN(e)||1/0===e||-1/0===e||0===e)return e;var t=Math.floor(Math.abs(e));return 0>e?-t:t},$jscomp.math.cbrt=function(e){if(0===e)return e;e=Number(e);var t=Math.pow(Math.abs(e),1/3);return 0>e?-t:t},$jscomp.number=$jscomp.number||{},$jscomp.number.isFinite=function(e){return"number"==typeof e&&!isNaN(e)&&1/0!==e&&-1/0!==e},$jscomp.number.isInteger=function(e){return!!$jscomp.number.isFinite(e)&&e===Math.floor(e)},$jscomp.number.isNaN=function(e){return"number"==typeof e&&isNaN(e)},$jscomp.number.isSafeInteger=function(e){return $jscomp.number.isInteger(e)&&Math.abs(e)<=$jscomp.number.MAX_SAFE_INTEGER},$jscomp.number.EPSILON=function(){return Math.pow(2,-52)}(),$jscomp.number.MAX_SAFE_INTEGER=9007199254740991,$jscomp.number.MIN_SAFE_INTEGER=-9007199254740991,$jscomp.object=$jscomp.object||{},$jscomp.object.assign=function(e,t){for(var r=1;r<arguments.length;r++){var o=arguments[r];if(o)for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},$jscomp.object.is=function(e,t){return e===t?0!==e||1/e==1/t:e!==e&&t!==t},$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.Set$isConformant=function(){if($jscomp.ASSUME_NO_NATIVE_SET)return!1;var e=$jscomp.global.Set;if(!e||!e.prototype.entries||"function"!=typeof _seal2["default"])return!1;try{var t=(0,_seal2["default"])({x:4}),r=new e($jscomp.makeIterator([t]));if(!r.has(t)||1!=r.size||r.add(t)!=r||1!=r.size||r.add({x:4})!=r||2!=r.size)return!1;var o=r.entries(),n=o.next();return!n.done&&n.value[0]==t&&n.value[1]==t&&(n=o.next(),!n.done&&n.value[0]!=t&&4==n.value[0].x&&n.value[1]==n.value[0]&&o.next().done)}catch(e){return!1}},$jscomp.Set=function(e){if(this.map_=new $jscomp.Map,e){e=$jscomp.makeIterator(e);for(var t;!(t=e.next()).done;)this.add(t.value)}this.size=this.map_.size},$jscomp.Set.prototype.add=function(e){return this.map_.set(e,e),this.size=this.map_.size,this},$jscomp.Set.prototype["delete"]=function(e){return e=this.map_["delete"](e),this.size=this.map_.size,e},$jscomp.Set.prototype.clear=function(){this.map_.clear(),this.size=0},$jscomp.Set.prototype.has=function(e){return this.map_.has(e)},$jscomp.Set.prototype.entries=function(){return this.map_.entries()},$jscomp.Set.prototype.values=function(){return this.map_.values()},$jscomp.Set.prototype.forEach=function(e,t){var r=this;this.map_.forEach(function(o){return e.call(t,o,o,r)})},$jscomp.Set$install=function(){$jscomp.Map$install(),$jscomp.Set$isConformant()?$jscomp.Set=$jscomp.global.Set:($jscomp.initSymbol(),$jscomp.initSymbolIterator(),$jscomp.Set.prototype[_iterator2["default"]]=$jscomp.Set.prototype.values,$jscomp.Set$install=function(){})},$jscomp.string=$jscomp.string||{},$jscomp.checkStringArgs=function(e,t,r){if(null==e)throw new TypeError("The 'this' value for String.prototype."+r+" must not be null or undefined");if(t instanceof RegExp)throw new TypeError("First argument to String.prototype."+r+" must not be a regular expression");return e+""},$jscomp.string.fromCodePoint=function(e){for(var t="",r=0;r<arguments.length;r++){var o=Number(arguments[r]);if(0>o||1114111<o||o!==Math.floor(o))throw new RangeError("invalid_code_point "+o);65535>=o?t+=String.fromCharCode(o):(o-=65536,t+=String.fromCharCode(o>>>10&1023|55296),t+=String.fromCharCode(1023&o|56320))}return t},$jscomp.string.repeat=function(e){var t=$jscomp.checkStringArgs(this,null,"repeat");if(0>e||1342177279<e)throw new RangeError("Invalid count value");e|=0;for(var r="";e;)1&e&&(r+=t),(e>>>=1)&&(t+=t);return r},$jscomp.string.repeat$install=function(){String.prototype.repeat||(String.prototype.repeat=$jscomp.string.repeat)},$jscomp.string.codePointAt=function(e){var t=$jscomp.checkStringArgs(this,null,"codePointAt"),r=t.length;if(0<=(e=Number(e)||0)&&e<r){e|=0;var o=t.charCodeAt(e);return 55296>o||56319<o||e+1===r?o:(e=t.charCodeAt(e+1),56320>e||57343<e?o:1024*(o-55296)+e+9216)}},$jscomp.string.codePointAt$install=function(){String.prototype.codePointAt||(String.prototype.codePointAt=$jscomp.string.codePointAt)},$jscomp.string.includes=function(e,t){return-1!==$jscomp.checkStringArgs(this,e,"includes").indexOf(e,t||0)},$jscomp.string.includes$install=function(){String.prototype.includes||(String.prototype.includes=$jscomp.string.includes)},$jscomp.string.startsWith=function(e,t){var r=$jscomp.checkStringArgs(this,e,"startsWith");e+="";for(var o=r.length,n=e.length,i=Math.max(0,Math.min(0|t,r.length)),s=0;s<n&&i<o;)if(r[i++]!=e[s++])return!1;return s>=n},$jscomp.string.startsWith$install=function(){String.prototype.startsWith||(String.prototype.startsWith=$jscomp.string.startsWith)},$jscomp.string.endsWith=function(e,t){var r=$jscomp.checkStringArgs(this,e,"endsWith");e+="",void 0===t&&(t=r.length);for(var o=Math.max(0,Math.min(0|t,r.length)),n=e.length;0<n&&0<o;)if(r[--o]!=e[--n])return!1;return 0>=n},$jscomp.string.endsWith$install=function(){String.prototype.endsWith||(String.prototype.endsWith=$jscomp.string.endsWith)};var COMPILED=!0,goog=goog||{};goog.global=this,goog.isDef=function(e){return void 0!==e},goog.exportPath_=function(e,t,r){e=e.split("."),r=r||goog.global,e[0]in r||!r.execScript||r.execScript("var "+e[0]);for(var o;e.length&&(o=e.shift());)!e.length&&goog.isDef(t)?r[o]=t:r=r[o]?r[o]:r[o]={}},goog.define=function(e,t){var r=t;COMPILED||(goog.global.CLOSURE_UNCOMPILED_DEFINES&&Object.prototype.hasOwnProperty.call(goog.global.CLOSURE_UNCOMPILED_DEFINES,e)?r=goog.global.CLOSURE_UNCOMPILED_DEFINES[e]:goog.global.CLOSURE_DEFINES&&Object.prototype.hasOwnProperty.call(goog.global.CLOSURE_DEFINES,e)&&(r=goog.global.CLOSURE_DEFINES[e])),goog.exportPath_(e,r)},goog.DEBUG=!0,goog.LOCALE="en",goog.TRUSTED_SITE=!0,goog.STRICT_MODE_COMPATIBLE=!1,goog.DISALLOW_TEST_ONLY_CODE=COMPILED&&!goog.DEBUG,goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING=!1,goog.provide=function(e){if(!COMPILED&&goog.isProvided_(e))throw Error('Namespace "'+e+'" already declared.');goog.constructNamespace_(e)},goog.constructNamespace_=function(e,t){if(!COMPILED){delete goog.implicitNamespaces_[e];for(var r=e;(r=r.substring(0,r.lastIndexOf(".")))&&!goog.getObjectByName(r);)goog.implicitNamespaces_[r]=!0}goog.exportPath_(e,t)},goog.VALID_MODULE_RE_=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/,goog.module=function(e){if(!goog.isString(e)||!e||-1==e.search(goog.VALID_MODULE_RE_))throw Error("Invalid module identifier");if(!goog.isInModuleLoader_())throw Error("Module "+e+" has been loaded incorrectly.");if(goog.moduleLoaderState_.moduleName)throw Error("goog.module may only be called once per module.");if(goog.moduleLoaderState_.moduleName=e,!COMPILED){if(goog.isProvided_(e))throw Error('Namespace "'+e+'" already declared.');delete goog.implicitNamespaces_[e]}},goog.module.get=function(e){return goog.module.getInternal_(e)},goog.module.getInternal_=function(e){if(!COMPILED)return goog.isProvided_(e)?e in goog.loadedModules_?goog.loadedModules_[e]:goog.getObjectByName(e):null},goog.moduleLoaderState_=null,goog.isInModuleLoader_=function(){return null!=goog.moduleLoaderState_},goog.module.declareLegacyNamespace=function(){if(!COMPILED&&!goog.isInModuleLoader_())throw Error("goog.module.declareLegacyNamespace must be called from within a goog.module");if(!COMPILED&&!goog.moduleLoaderState_.moduleName)throw Error("goog.module must be called prior to goog.module.declareLegacyNamespace.");goog.moduleLoaderState_.declareLegacyNamespace=!0},goog.setTestOnly=function(e){if(goog.DISALLOW_TEST_ONLY_CODE)throw e=e||"",Error("Importing test-only code into non-debug environment"+(e?": "+e:"."))},goog.forwardDeclare=function(e){},COMPILED||(goog.isProvided_=function(e){return e in goog.loadedModules_||!goog.implicitNamespaces_[e]&&goog.isDefAndNotNull(goog.getObjectByName(e))},goog.implicitNamespaces_={"goog.module":!0}),goog.getObjectByName=function(e,t){for(var r,o=e.split("."),n=t||goog.global;r=o.shift();){if(!goog.isDefAndNotNull(n[r]))return null;n=n[r]}return n},goog.globalize=function(e,t){var r,o=t||goog.global;for(r in e)o[r]=e[r]},goog.addDependency=function(e,t,r,o){if(goog.DEPENDENCIES_ENABLED){var n;e=e.replace(/\\/g,"/");for(var i=goog.dependencies_,s=0;n=t[s];s++)i.nameToPath[n]=e,i.pathIsModule[e]=!!o;for(o=0;t=r[o];o++)e in i.requires||(i.requires[e]={}),i.requires[e][t]=!0}},goog.ENABLE_DEBUG_LOADER=!0,goog.logToConsole_=function(e){goog.global.console&&goog.global.console.error(e)},goog.require=function(e){if(!COMPILED){if(goog.ENABLE_DEBUG_LOADER&&goog.IS_OLD_IE_&&goog.maybeProcessDeferredDep_(e),goog.isProvided_(e))return goog.isInModuleLoader_()?goog.module.getInternal_(e):null;if(goog.ENABLE_DEBUG_LOADER){var t=goog.getPathFromDeps_(e);if(t)return goog.writeScripts_(t),null}throw e="goog.require could not find: "+e,goog.logToConsole_(e),Error(e)}},goog.basePath="",goog.nullFunction=function(){},goog.abstractMethod=function(){throw Error("unimplemented abstract method")},goog.addSingletonGetter=function(e){e.getInstance=function(){return e.instance_?e.instance_:(goog.DEBUG&&(goog.instantiatedSingletons_[goog.instantiatedSingletons_.length]=e),e.instance_=new e)}},goog.instantiatedSingletons_=[],goog.LOAD_MODULE_USING_EVAL=!0,goog.SEAL_MODULE_EXPORTS=goog.DEBUG,goog.loadedModules_={},goog.DEPENDENCIES_ENABLED=!COMPILED&&goog.ENABLE_DEBUG_LOADER,goog.DEPENDENCIES_ENABLED&&(goog.dependencies_={pathIsModule:{},nameToPath:{},requires:{},visited:{},written:{},deferred:{}},goog.inHtmlDocument_=function(){var e=goog.global.document;return null!=e&&"write"in e},goog.findBasePath_=function(){if(goog.isDef(goog.global.CLOSURE_BASE_PATH))goog.basePath=goog.global.CLOSURE_BASE_PATH;else if(goog.inHtmlDocument_())for(var e=goog.global.document.getElementsByTagName("SCRIPT"),t=e.length-1;0<=t;--t){var r=e[t].src,o=r.lastIndexOf("?"),o=-1==o?r.length:o;if("base.js"==r.substr(o-7,7)){goog.basePath=r.substr(0,o-7);break}}},goog.importScript_=function(e,t){(goog.global.CLOSURE_IMPORT_SCRIPT||goog.writeScriptTag_)(e,t)&&(goog.dependencies_.written[e]=!0)},goog.IS_OLD_IE_=!(goog.global.atob||!goog.global.document||!goog.global.document.all),goog.importModule_=function(e){goog.importScript_("",'goog.retrieveAndExecModule_("'+e+'");')&&(goog.dependencies_.written[e]=!0)},goog.queuedModules_=[],goog.wrapModule_=function(e,t){return goog.LOAD_MODULE_USING_EVAL&&goog.isDef(goog.global.JSON)?"goog.loadModule("+goog.global.JSON.stringify(t+"\n//# sourceURL="+e+"\n")+");":'goog.loadModule(function(exports) {"use strict";'+t+"\n;return exports});\n//# sourceURL="+e+"\n"},goog.loadQueuedModules_=function(){var e=goog.queuedModules_.length;if(0<e){var t=goog.queuedModules_;goog.queuedModules_=[];for(var r=0;r<e;r++)goog.maybeProcessDeferredPath_(t[r])}},goog.maybeProcessDeferredDep_=function(e){goog.isDeferredModule_(e)&&goog.allDepsAreAvailable_(e)&&(e=goog.getPathFromDeps_(e),goog.maybeProcessDeferredPath_(goog.basePath+e))},goog.isDeferredModule_=function(e){return!(!(e=goog.getPathFromDeps_(e))||!goog.dependencies_.pathIsModule[e])&&goog.basePath+e in goog.dependencies_.deferred},goog.allDepsAreAvailable_=function(e){if((e=goog.getPathFromDeps_(e))&&e in goog.dependencies_.requires)for(var t in goog.dependencies_.requires[e])if(!goog.isProvided_(t)&&!goog.isDeferredModule_(t))return!1;return!0},goog.maybeProcessDeferredPath_=function(e){if(e in goog.dependencies_.deferred){var t=goog.dependencies_.deferred[e];delete goog.dependencies_.deferred[e],goog.globalEval(t)}},goog.loadModuleFromUrl=function(e){goog.retrieveAndExecModule_(e)},goog.loadModule=function(e){var t=goog.moduleLoaderState_;try{goog.moduleLoaderState_={moduleName:void 0,declareLegacyNamespace:!1};var r;if(goog.isFunction(e))r=e.call(goog.global,{});else{if(!goog.isString(e))throw Error("Invalid module definition");r=goog.loadModuleFromSource_.call(goog.global,e)}var o=goog.moduleLoaderState_.moduleName;if(!goog.isString(o)||!o)throw Error('Invalid module name "'+o+'"');goog.moduleLoaderState_.declareLegacyNamespace?goog.constructNamespace_(o,r):goog.SEAL_MODULE_EXPORTS&&_seal2["default"]&&(0,_seal2["default"])(r),goog.loadedModules_[o]=r}finally{goog.moduleLoaderState_=t}},goog.loadModuleFromSource_=function(a){return eval(a),{}},goog.writeScriptSrcNode_=function(e){goog.global.document.write('<script type="text/javascript" src="'+e+'"></script>')},goog.appendScriptSrcNode_=function(e){var t=goog.global.document,r=t.createElement("script");r.type="text/javascript",r.src=e,r.defer=!1,r.async=!1,t.head.appendChild(r)},goog.writeScriptTag_=function(e,t){if(goog.inHtmlDocument_()){var r=goog.global.document;if(!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING&&"complete"==r.readyState){
if(/\bdeps.js$/.test(e))return!1;throw Error('Cannot write "'+e+'" after document load')}var o=goog.IS_OLD_IE_;return void 0===t?o?(o=" onreadystatechange='goog.onScriptLoad_(this, "+ ++goog.lastNonModuleScriptIndex_+")' ",r.write('<script type="text/javascript" src="'+e+'"'+o+"></script>")):goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING?goog.appendScriptSrcNode_(e):goog.writeScriptSrcNode_(e):r.write('<script type="text/javascript">'+t+"</script>"),!0}return!1},goog.lastNonModuleScriptIndex_=0,goog.onScriptLoad_=function(e,t){return"complete"==e.readyState&&goog.lastNonModuleScriptIndex_==t&&goog.loadQueuedModules_(),!0},goog.writeScripts_=function(e){function t(e){if(!(e in n.written||e in n.visited)){if(n.visited[e]=!0,e in n.requires)for(var i in n.requires[e])if(!goog.isProvided_(i)){if(!(i in n.nameToPath))throw Error("Undefined nameToPath for "+i);t(n.nameToPath[i])}e in o||(o[e]=!0,r.push(e))}}var r=[],o={},n=goog.dependencies_;for(t(e),e=0;e<r.length;e++){var i=r[e];goog.dependencies_.written[i]=!0}var s=goog.moduleLoaderState_;for(goog.moduleLoaderState_=null,e=0;e<r.length;e++){if(!(i=r[e]))throw goog.moduleLoaderState_=s,Error("Undefined script input");n.pathIsModule[i]?goog.importModule_(goog.basePath+i):goog.importScript_(goog.basePath+i)}goog.moduleLoaderState_=s},goog.getPathFromDeps_=function(e){return e in goog.dependencies_.nameToPath?goog.dependencies_.nameToPath[e]:null},goog.findBasePath_(),goog.global.CLOSURE_NO_DEPS||goog.importScript_(goog.basePath+"deps.js")),goog.normalizePath_=function(e){e=e.split("/");for(var t=0;t<e.length;)"."==e[t]?e.splice(t,1):t&&".."==e[t]&&e[t-1]&&".."!=e[t-1]?e.splice(--t,2):t++;return e.join("/")},goog.loadFileSync_=function(e){if(goog.global.CLOSURE_LOAD_FILE_SYNC)return goog.global.CLOSURE_LOAD_FILE_SYNC(e);var t=new goog.global.XMLHttpRequest;return t.open("get",e,!1),t.send(),t.responseText},goog.retrieveAndExecModule_=function(e){if(!COMPILED){var t=e;e=goog.normalizePath_(e);var r=goog.global.CLOSURE_IMPORT_SCRIPT||goog.writeScriptTag_,o=goog.loadFileSync_(e);if(null==o)throw Error("load of "+e+"failed");o=goog.wrapModule_(e,o),goog.IS_OLD_IE_?(goog.dependencies_.deferred[t]=o,goog.queuedModules_.push(t)):r(e,o)}},goog.typeOf=function(e){var t="undefined"==typeof e?"undefined":(0,_typeof3["default"])(e);if("object"==t){if(!e)return"null";if(e instanceof Array)return"array";if(e instanceof Object)return t;var r=Object.prototype.toString.call(e);if("[object Window]"==r)return"object";if("[object Array]"==r||"number"==typeof e.length&&void 0!==e.splice&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("splice"))return"array";if("[object Function]"==r||void 0!==e.call&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("call"))return"function"}else if("function"==t&&void 0===e.call)return"object";return t},goog.isNull=function(e){return null===e},goog.isDefAndNotNull=function(e){return null!=e},goog.isArray=function(e){return"array"==goog.typeOf(e)},goog.isArrayLike=function(e){var t=goog.typeOf(e);return"array"==t||"object"==t&&"number"==typeof e.length},goog.isDateLike=function(e){return goog.isObject(e)&&"function"==typeof e.getFullYear},goog.isString=function(e){return"string"==typeof e},goog.isBoolean=function(e){return"boolean"==typeof e},goog.isNumber=function(e){return"number"==typeof e},goog.isFunction=function(e){return"function"==goog.typeOf(e)},goog.isObject=function(e){var t="undefined"==typeof e?"undefined":(0,_typeof3["default"])(e);return"object"==t&&null!=e||"function"==t},goog.getUid=function(e){return e[goog.UID_PROPERTY_]||(e[goog.UID_PROPERTY_]=++goog.uidCounter_)},goog.hasUid=function(e){return!!e[goog.UID_PROPERTY_]},goog.removeUid=function(e){null!==e&&"removeAttribute"in e&&e.removeAttribute(goog.UID_PROPERTY_);try{delete e[goog.UID_PROPERTY_]}catch(e){}},goog.UID_PROPERTY_="closure_uid_"+(1e9*Math.random()>>>0),goog.uidCounter_=0,goog.getHashCode=goog.getUid,goog.removeHashCode=goog.removeUid,goog.cloneObject=function(e){var t=goog.typeOf(e);if("object"==t||"array"==t){if(e.clone)return e.clone();var r,t="array"==t?[]:{};for(r in e)t[r]=goog.cloneObject(e[r]);return t}return e},goog.bindNative_=function(e,t,r){return e.call.apply(e.bind,arguments)},goog.bindJs_=function(e,t,r){if(!e)throw Error();if(2<arguments.length){var o=Array.prototype.slice.call(arguments,2);return function(){var r=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(r,o),e.apply(t,r)}}return function(){return e.apply(t,arguments)}},goog.bind=function(e,t,r){return Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?goog.bind=goog.bindNative_:goog.bind=goog.bindJs_,goog.bind.apply(null,arguments)},goog.partial=function(e,t){var r=Array.prototype.slice.call(arguments,1);return function(){var t=r.slice();return t.push.apply(t,arguments),e.apply(this,t)}},goog.mixin=function(e,t){for(var r in t)e[r]=t[r]},goog.now=goog.TRUSTED_SITE&&Date.now||function(){return+new Date},goog.globalEval=function(e){if(goog.global.execScript)goog.global.execScript(e,"JavaScript");else{if(!goog.global.eval)throw Error("goog.globalEval not available");if(null==goog.evalWorksForGlobals_)if(goog.global.eval("var _evalTest_ = 1;"),void 0!==goog.global._evalTest_){try{delete goog.global._evalTest_}catch(e){}goog.evalWorksForGlobals_=!0}else goog.evalWorksForGlobals_=!1;if(goog.evalWorksForGlobals_)goog.global.eval(e);else{var t=goog.global.document,r=t.createElement("SCRIPT");r.type="text/javascript",r.defer=!1,r.appendChild(t.createTextNode(e)),t.body.appendChild(r),t.body.removeChild(r)}}},goog.evalWorksForGlobals_=null,goog.getCssName=function(e,t){var r=function n(e){return goog.cssNameMapping_[e]||e},o=function i(e){e=e.split("-");for(var t=[],i=0;i<e.length;i++)t.push(r(e[i]));return t.join("-")},o=goog.cssNameMapping_?"BY_WHOLE"==goog.cssNameMappingStyle_?r:o:function(e){return e};return t?e+"-"+o(t):o(e)},goog.setCssNameMapping=function(e,t){goog.cssNameMapping_=e,goog.cssNameMappingStyle_=t},!COMPILED&&goog.global.CLOSURE_CSS_NAME_MAPPING&&(goog.cssNameMapping_=goog.global.CLOSURE_CSS_NAME_MAPPING),goog.getMsg=function(e,t){return t&&(e=e.replace(/\{\$([^}]+)}/g,function(e,r){return null!=t&&r in t?t[r]:e})),e},goog.getMsgWithFallback=function(e,t){return e},goog.exportSymbol=function(e,t,r){goog.exportPath_(e,t,r)},goog.exportProperty=function(e,t,r){e[t]=r},goog.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.superClass_=t.prototype,e.prototype=new r,e.prototype.constructor=e,e.base=function(e,r,o){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return t.prototype[r].apply(e,n)}},goog.base=function(e,t,r){var o=arguments.callee.caller;if(goog.STRICT_MODE_COMPATIBLE||goog.DEBUG&&!o)throw Error("arguments.caller not defined.  goog.base() cannot be used with strict mode code. See http://www.ecma-international.org/ecma-262/5.1/#sec-C");if(o.superClass_){for(var n=Array(arguments.length-1),i=1;i<arguments.length;i++)n[i-1]=arguments[i];return o.superClass_.constructor.apply(e,n)}for(n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];for(var i=!1,s=e.constructor;s;s=s.superClass_&&s.superClass_.constructor)if(s.prototype[t]===o)i=!0;else if(i)return s.prototype[t].apply(e,n);if(e[t]===o)return e.constructor.prototype[t].apply(e,n);throw Error("goog.base called from a method of one name to a method of a different name")},goog.scope=function(e){e.call(goog.global)},COMPILED||(goog.global.COMPILED=COMPILED),goog.defineClass=function(e,t){var r=t.constructor,o=t.statics;return r&&r!=Object.prototype.constructor||(r=function n(){throw Error("cannot instantiate an interface (no constructor defined).")}),r=goog.defineClass.createSealingConstructor_(r,e),e&&goog.inherits(r,e),delete t.constructor,delete t.statics,goog.defineClass.applyProperties_(r.prototype,t),null!=o&&(o instanceof Function?o(r):goog.defineClass.applyProperties_(r,o)),r},goog.defineClass.SEAL_CLASS_INSTANCES=goog.DEBUG,goog.defineClass.createSealingConstructor_=function(e,t){if(goog.defineClass.SEAL_CLASS_INSTANCES&&_seal2["default"]instanceof Function){if(t&&t.prototype&&t.prototype[goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_])return e;var r=function o(){var t=e.apply(this,arguments)||this;return t[goog.UID_PROPERTY_]=t[goog.UID_PROPERTY_],this.constructor===o&&(0,_seal2["default"])(t),t};return r}return e},goog.defineClass.OBJECT_PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),goog.defineClass.applyProperties_=function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);for(var o=0;o<goog.defineClass.OBJECT_PROTOTYPE_FIELDS_.length;o++)r=goog.defineClass.OBJECT_PROTOTYPE_FIELDS_[o],Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},goog.tagUnsealableClass=function(e){!COMPILED&&goog.defineClass.SEAL_CLASS_INSTANCES&&(e.prototype[goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_]=!0)},goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_="goog_defineClass_legacy_unsealable",goog.dom={},goog.dom.NodeType={ELEMENT:1,ATTRIBUTE:2,TEXT:3,CDATA_SECTION:4,ENTITY_REFERENCE:5,ENTITY:6,PROCESSING_INSTRUCTION:7,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,NOTATION:12},goog.debug={},goog.debug.Error=function(e){if(Error.captureStackTrace)Error.captureStackTrace(this,goog.debug.Error);else{var t=Error().stack;t&&(this.stack=t)}e&&(this.message=String(e)),this.reportErrorToServer=!0},goog.inherits(goog.debug.Error,Error),goog.debug.Error.prototype.name="CustomError",goog.string={},goog.string.DETECT_DOUBLE_ESCAPING=!1,goog.string.FORCE_NON_DOM_HTML_UNESCAPING=!1,goog.string.Unicode={NBSP:" "},goog.string.startsWith=function(e,t){return 0==e.lastIndexOf(t,0)},goog.string.endsWith=function(e,t){var r=e.length-t.length;return 0<=r&&e.indexOf(t,r)==r},goog.string.caseInsensitiveStartsWith=function(e,t){return 0==goog.string.caseInsensitiveCompare(t,e.substr(0,t.length))},goog.string.caseInsensitiveEndsWith=function(e,t){return 0==goog.string.caseInsensitiveCompare(t,e.substr(e.length-t.length,t.length))},goog.string.caseInsensitiveEquals=function(e,t){return e.toLowerCase()==t.toLowerCase()},goog.string.subs=function(e,t){for(var r=e.split("%s"),o="",n=Array.prototype.slice.call(arguments,1);n.length&&1<r.length;)o+=r.shift()+n.shift();return o+r.join("%s")},goog.string.collapseWhitespace=function(e){return e.replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")},goog.string.isEmptyOrWhitespace=function(e){return/^[\s\xa0]*$/.test(e)},goog.string.isEmptyString=function(e){return 0==e.length},goog.string.isEmpty=goog.string.isEmptyOrWhitespace,goog.string.isEmptyOrWhitespaceSafe=function(e){return goog.string.isEmptyOrWhitespace(goog.string.makeSafe(e))},goog.string.isEmptySafe=goog.string.isEmptyOrWhitespaceSafe,goog.string.isBreakingWhitespace=function(e){return!/[^\t\n\r ]/.test(e)},goog.string.isAlpha=function(e){return!/[^a-zA-Z]/.test(e)},goog.string.isNumeric=function(e){return!/[^0-9]/.test(e)},goog.string.isAlphaNumeric=function(e){return!/[^a-zA-Z0-9]/.test(e)},goog.string.isSpace=function(e){return" "==e},goog.string.isUnicodeChar=function(e){return 1==e.length&&" "<=e&&"~">=e||""<=e&&"�">=e},goog.string.stripNewlines=function(e){return e.replace(/(\r\n|\r|\n)+/g," ")},goog.string.canonicalizeNewlines=function(e){return e.replace(/(\r\n|\r|\n)/g,"\n")},goog.string.normalizeWhitespace=function(e){return e.replace(/\xa0|\s/g," ")},goog.string.normalizeSpaces=function(e){return e.replace(/\xa0|[ \t]+/g," ")},goog.string.collapseBreakingSpaces=function(e){return e.replace(/[\t\r\n ]+/g," ").replace(/^[\t\r\n ]+|[\t\r\n ]+$/g,"")},goog.string.trim=goog.TRUSTED_SITE&&String.prototype.trim?function(e){return e.trim()}:function(e){return e.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")},goog.string.trimLeft=function(e){return e.replace(/^[\s\xa0]+/,"")},goog.string.trimRight=function(e){return e.replace(/[\s\xa0]+$/,"")},goog.string.caseInsensitiveCompare=function(e,t){var r=String(e).toLowerCase(),o=String(t).toLowerCase();return r<o?-1:r==o?0:1},goog.string.numberAwareCompare_=function(e,t,r){if(e==t)return 0;if(!e)return-1;if(!t)return 1;for(var o=e.toLowerCase().match(r),n=t.toLowerCase().match(r),i=Math.min(o.length,n.length),s=0;s<i;s++){r=o[s];var a=n[s];if(r!=a)return e=parseInt(r,10),!isNaN(e)&&(t=parseInt(a,10),!isNaN(t)&&e-t)?e-t:r<a?-1:1}return o.length!=n.length?o.length-n.length:e<t?-1:1},goog.string.intAwareCompare=function(e,t){return goog.string.numberAwareCompare_(e,t,/\d+|\D+/g)},goog.string.floatAwareCompare=function(e,t){return goog.string.numberAwareCompare_(e,t,/\d+|\.\d+|\D+/g)},goog.string.numerateCompare=goog.string.floatAwareCompare,goog.string.urlEncode=function(e){return encodeURIComponent(String(e))},goog.string.urlDecode=function(e){return decodeURIComponent(e.replace(/\+/g," "))},goog.string.newLineToBr=function(e,t){return e.replace(/(\r\n|\r|\n)/g,t?"<br />":"<br>")},goog.string.htmlEscape=function(e,t){if(t)e=e.replace(goog.string.AMP_RE_,"&amp;").replace(goog.string.LT_RE_,"&lt;").replace(goog.string.GT_RE_,"&gt;").replace(goog.string.QUOT_RE_,"&quot;").replace(goog.string.SINGLE_QUOTE_RE_,"&#39;").replace(goog.string.NULL_RE_,"&#0;"),goog.string.DETECT_DOUBLE_ESCAPING&&(e=e.replace(goog.string.E_RE_,"&#101;"));else{if(!goog.string.ALL_RE_.test(e))return e;-1!=e.indexOf("&")&&(e=e.replace(goog.string.AMP_RE_,"&amp;")),-1!=e.indexOf("<")&&(e=e.replace(goog.string.LT_RE_,"&lt;")),-1!=e.indexOf(">")&&(e=e.replace(goog.string.GT_RE_,"&gt;")),-1!=e.indexOf('"')&&(e=e.replace(goog.string.QUOT_RE_,"&quot;")),-1!=e.indexOf("'")&&(e=e.replace(goog.string.SINGLE_QUOTE_RE_,"&#39;")),-1!=e.indexOf("\0")&&(e=e.replace(goog.string.NULL_RE_,"&#0;")),goog.string.DETECT_DOUBLE_ESCAPING&&-1!=e.indexOf("e")&&(e=e.replace(goog.string.E_RE_,"&#101;"))}return e},goog.string.AMP_RE_=/&/g,goog.string.LT_RE_=/</g,goog.string.GT_RE_=/>/g,goog.string.QUOT_RE_=/"/g,goog.string.SINGLE_QUOTE_RE_=/'/g,goog.string.NULL_RE_=/\x00/g,goog.string.E_RE_=/e/g,goog.string.ALL_RE_=goog.string.DETECT_DOUBLE_ESCAPING?/[\x00&<>"'e]/:/[\x00&<>"']/,goog.string.unescapeEntities=function(e){return goog.string.contains(e,"&")?!goog.string.FORCE_NON_DOM_HTML_UNESCAPING&&"document"in goog.global?goog.string.unescapeEntitiesUsingDom_(e):goog.string.unescapePureXmlEntities_(e):e},goog.string.unescapeEntitiesWithDocument=function(e,t){return goog.string.contains(e,"&")?goog.string.unescapeEntitiesUsingDom_(e,t):e},goog.string.unescapeEntitiesUsingDom_=function(e,t){var r,o={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};return r=t?t.createElement("div"):goog.global.document.createElement("div"),e.replace(goog.string.HTML_ENTITY_PATTERN_,function(e,t){var n=o[e];if(n)return n;if("#"==t.charAt(0)){var i=Number("0"+t.substr(1));isNaN(i)||(n=String.fromCharCode(i))}return n||(r.innerHTML=e+" ",n=r.firstChild.nodeValue.slice(0,-1)),o[e]=n})},goog.string.unescapePureXmlEntities_=function(e){return e.replace(/&([^;]+);/g,function(e,t){switch(t){case"amp":return"&";case"lt":return"<";case"gt":return">";case"quot":return'"';default:if("#"==t.charAt(0)){var r=Number("0"+t.substr(1));if(!isNaN(r))return String.fromCharCode(r)}return e}})},goog.string.HTML_ENTITY_PATTERN_=/&([^;\s<&]+);?/g,goog.string.whitespaceEscape=function(e,t){return goog.string.newLineToBr(e.replace(/  /g," &#160;"),t)},goog.string.preserveSpaces=function(e){return e.replace(/(^|[\n ]) /g,"$1"+goog.string.Unicode.NBSP)},goog.string.stripQuotes=function(e,t){for(var r=t.length,o=0;o<r;o++){var n=1==r?t:t.charAt(o);if(e.charAt(0)==n&&e.charAt(e.length-1)==n)return e.substring(1,e.length-1)}return e},goog.string.truncate=function(e,t,r){return r&&(e=goog.string.unescapeEntities(e)),e.length>t&&(e=e.substring(0,t-3)+"..."),r&&(e=goog.string.htmlEscape(e)),e},goog.string.truncateMiddle=function(e,t,r,o){if(r&&(e=goog.string.unescapeEntities(e)),o&&e.length>t){o>t&&(o=t);var n=e.length-o;e=e.substring(0,t-o)+"..."+e.substring(n)}else e.length>t&&(o=Math.floor(t/2),n=e.length-o,e=e.substring(0,o+t%2)+"..."+e.substring(n));return r&&(e=goog.string.htmlEscape(e)),e},goog.string.specialEscapeChars_={"\0":"\\0","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\x0B":"\\x0B",'"':'\\"',"\\":"\\\\","<":"<"},goog.string.jsEscapeCache_={"'":"\\'"},goog.string.quote=function(e){e=String(e);for(var t=['"'],r=0;r<e.length;r++){var o=e.charAt(r),n=o.charCodeAt(0);t[r+1]=goog.string.specialEscapeChars_[o]||(31<n&&127>n?o:goog.string.escapeChar(o))}return t.push('"'),t.join("")},goog.string.escapeString=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=goog.string.escapeChar(e.charAt(r));return t.join("")},goog.string.escapeChar=function(e){if(e in goog.string.jsEscapeCache_)return goog.string.jsEscapeCache_[e];if(e in goog.string.specialEscapeChars_)return goog.string.jsEscapeCache_[e]=goog.string.specialEscapeChars_[e];var t,r=e.charCodeAt(0);return 31<r&&127>r?t=e:(256>r?(t="\\x",(16>r||256<r)&&(t+="0")):(t="\\u",4096>r&&(t+="0")),t+=r.toString(16).toUpperCase()),goog.string.jsEscapeCache_[e]=t},goog.string.contains=function(e,t){return-1!=e.indexOf(t)},goog.string.caseInsensitiveContains=function(e,t){return goog.string.contains(e.toLowerCase(),t.toLowerCase())},goog.string.countOf=function(e,t){return e&&t?e.split(t).length-1:0},goog.string.removeAt=function(e,t,r){var o=e;return 0<=t&&t<e.length&&0<r&&(o=e.substr(0,t)+e.substr(t+r,e.length-t-r)),o},goog.string.remove=function(e,t){var r=new RegExp(goog.string.regExpEscape(t),"");return e.replace(r,"")},goog.string.removeAll=function(e,t){var r=new RegExp(goog.string.regExpEscape(t),"g");return e.replace(r,"")},goog.string.regExpEscape=function(e){return String(e).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")},goog.string.repeat=String.prototype.repeat?function(e,t){return e.repeat(t)}:function(e,t){return Array(t+1).join(e)},goog.string.padNumber=function(e,t,r){return e=goog.isDef(r)?e.toFixed(r):String(e),r=e.indexOf("."),-1==r&&(r=e.length),goog.string.repeat("0",Math.max(0,t-r))+e},goog.string.makeSafe=function(e){return null==e?"":String(e)},goog.string.buildString=function(e){return Array.prototype.join.call(arguments,"")},goog.string.getRandomString=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^goog.now()).toString(36)},goog.string.compareVersions=function(e,t){for(var r=0,o=goog.string.trim(String(e)).split("."),n=goog.string.trim(String(t)).split("."),i=Math.max(o.length,n.length),s=0;0==r&&s<i;s++){var a=o[s]||"",u=n[s]||"",p=RegExp("(\\d*)(\\D*)","g"),g=RegExp("(\\d*)(\\D*)","g");do{var c=p.exec(a)||["","",""],l=g.exec(u)||["","",""];if(0==c[0].length&&0==l[0].length)break;var r=0==c[1].length?0:parseInt(c[1],10),h=0==l[1].length?0:parseInt(l[1],10),r=goog.string.compareElements_(r,h)||goog.string.compareElements_(0==c[2].length,0==l[2].length)||goog.string.compareElements_(c[2],l[2])}while(0==r)}return r},goog.string.compareElements_=function(e,t){return e<t?-1:e>t?1:0},goog.string.hashCode=function(e){for(var t=0,r=0;r<e.length;++r)t=31*t+e.charCodeAt(r)>>>0;return t},goog.string.uniqueStringCounter_=2147483648*Math.random()|0,goog.string.createUniqueString=function(){return"goog_"+goog.string.uniqueStringCounter_++},goog.string.toNumber=function(e){var t=Number(e);return 0==t&&goog.string.isEmptyOrWhitespace(e)?NaN:t},goog.string.isLowerCamelCase=function(e){return/^[a-z]+([A-Z][a-z]*)*$/.test(e)},goog.string.isUpperCamelCase=function(e){return/^([A-Z][a-z]*)+$/.test(e)},goog.string.toCamelCase=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})},goog.string.toSelectorCase=function(e){return String(e).replace(/([A-Z])/g,"-$1").toLowerCase()},goog.string.toTitleCase=function(e,t){var r=goog.isString(t)?goog.string.regExpEscape(t):"\\s";return e.replace(new RegExp("(^"+(r?"|["+r+"]+":"")+")([a-z])","g"),function(e,t,r){return t+r.toUpperCase()})},goog.string.capitalize=function(e){return String(e.charAt(0)).toUpperCase()+String(e.substr(1)).toLowerCase()},goog.string.parseInt=function(e){return isFinite(e)&&(e=String(e)),goog.isString(e)?/^\s*-?0x/i.test(e)?parseInt(e,16):parseInt(e,10):NaN},goog.string.splitLimit=function(e,t,r){e=e.split(t);for(var o=[];0<r&&e.length;)o.push(e.shift()),r--;return e.length&&o.push(e.join(t)),o},goog.string.editDistance=function(e,t){var r=[],o=[];if(e==t)return 0;if(!e.length||!t.length)return Math.max(e.length,t.length);for(var n=0;n<t.length+1;n++)r[n]=n;for(n=0;n<e.length;n++){o[0]=n+1;for(var i=0;i<t.length;i++)o[i+1]=Math.min(o[i]+1,r[i+1]+1,r[i]+Number(e[n]!=t[i]));for(i=0;i<r.length;i++)r[i]=o[i]}return o[t.length]},goog.asserts={},goog.asserts.ENABLE_ASSERTS=goog.DEBUG,goog.asserts.AssertionError=function(e,t){t.unshift(e),goog.debug.Error.call(this,goog.string.subs.apply(null,t)),t.shift(),this.messagePattern=e},goog.inherits(goog.asserts.AssertionError,goog.debug.Error),goog.asserts.AssertionError.prototype.name="AssertionError",goog.asserts.DEFAULT_ERROR_HANDLER=function(e){throw e},goog.asserts.errorHandler_=goog.asserts.DEFAULT_ERROR_HANDLER,goog.asserts.doAssertFailure_=function(e,t,r,o){var n="Assertion failed";if(r)var n=n+": "+r,i=o;else e&&(n+=": "+e,i=t);e=new goog.asserts.AssertionError(""+n,i||[]),goog.asserts.errorHandler_(e)},goog.asserts.setErrorHandler=function(e){goog.asserts.ENABLE_ASSERTS&&(goog.asserts.errorHandler_=e)},goog.asserts.assert=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!e&&goog.asserts.doAssertFailure_("",null,t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.fail=function(e,t){goog.asserts.ENABLE_ASSERTS&&goog.asserts.errorHandler_(new goog.asserts.AssertionError("Failure"+(e?": "+e:""),Array.prototype.slice.call(arguments,1)))},goog.asserts.assertNumber=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isNumber(e)&&goog.asserts.doAssertFailure_("Expected number but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertString=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isString(e)&&goog.asserts.doAssertFailure_("Expected string but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertFunction=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isFunction(e)&&goog.asserts.doAssertFailure_("Expected function but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertObject=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isObject(e)&&goog.asserts.doAssertFailure_("Expected object but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertArray=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isArray(e)&&goog.asserts.doAssertFailure_("Expected array but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertBoolean=function(e,t,r){return goog.asserts.ENABLE_ASSERTS&&!goog.isBoolean(e)&&goog.asserts.doAssertFailure_("Expected boolean but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertElement=function(e,t,r){return!goog.asserts.ENABLE_ASSERTS||goog.isObject(e)&&e.nodeType==goog.dom.NodeType.ELEMENT||goog.asserts.doAssertFailure_("Expected Element but got %s: %s.",[goog.typeOf(e),e],t,Array.prototype.slice.call(arguments,2)),e},goog.asserts.assertInstanceof=function(e,t,r,o){return!goog.asserts.ENABLE_ASSERTS||e instanceof t||goog.asserts.doAssertFailure_("Expected instanceof %s but got %s.",[goog.asserts.getType_(t),goog.asserts.getType_(e)],r,Array.prototype.slice.call(arguments,3)),e},goog.asserts.assertObjectPrototypeIsIntact=function(){for(var e in Object.prototype)goog.asserts.fail(e+" should not be enumerable in Object.prototype.")},goog.asserts.getType_=function(e){return e instanceof Function?e.displayName||e.name||"unknown type name":e instanceof Object?e.constructor.displayName||e.constructor.name||Object.prototype.toString.call(e):null===e?"null":"undefined"==typeof e?"undefined":(0,_typeof3["default"])(e)};var jspb={Map:function t(e,r){this.arr_=e,this.valueCtor_=r,this.map_={},this.arrClean=!0,0<this.arr_.length&&this.loadFromArray_()}};jspb.Map.prototype.loadFromArray_=function(){for(var e=0;e<this.arr_.length;e++){var t=this.arr_[e],r=t[0];this.map_[r.toString()]=new jspb.Map.Entry_(r,t[1])}this.arrClean=!0},jspb.Map.prototype.toArray=function(){if(this.arrClean){if(this.valueCtor_){var e,t=this.map_;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var r=t[e].valueWrapper;r&&r.toArray()}}}else{for(this.arr_.length=0,t=this.stringKeys_(),t.sort(),e=0;e<t.length;e++){var o=this.map_[t[e]];(r=o.valueWrapper)&&r.toArray(),this.arr_.push([o.key,o.value])}this.arrClean=!0}return this.arr_},jspb.Map.prototype.toObject=function(e,t){for(var r=this.toArray(),o=[],n=0;n<r.length;n++){var i=this.map_[r[n][0].toString()];this.wrapEntry_(i);var s=i.valueWrapper;s?(goog.asserts.assert(t),o.push([i.key,t(e,s)])):o.push([i.key,i.value])}return o},jspb.Map.fromObject=function(e,t,r){t=new jspb.Map([],t);for(var o=0;o<e.length;o++){var n=e[o][0],i=r(e[o][1]);t.set(n,i)}return t},jspb.Map.ArrayIteratorIterable_=function(e){this.idx_=0,this.arr_=e},jspb.Map.ArrayIteratorIterable_.prototype.next=function(){return this.idx_<this.arr_.length?{done:!1,value:this.arr_[this.idx_++]}:{done:!0,value:void 0}},$jscomp.initSymbol(),"undefined"!=typeof _symbol2["default"]&&($jscomp.initSymbol(),$jscomp.initSymbolIterator(),jspb.Map.ArrayIteratorIterable_.prototype[_iterator2["default"]]=function(){return this}),jspb.Map.prototype.getLength=function(){return this.stringKeys_().length},jspb.Map.prototype.clear=function(){this.map_={},this.arrClean=!1},jspb.Map.prototype.del=function(e){e=e.toString();var t=this.map_.hasOwnProperty(e);return delete this.map_[e],this.arrClean=!1,t},jspb.Map.prototype.getEntryList=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++){var o=this.map_[t[r]];e.push([o.key,o.value])}return e},jspb.Map.prototype.entries=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++){var o=this.map_[t[r]];e.push([o.key,this.wrapEntry_(o)])}return new jspb.Map.ArrayIteratorIterable_(e)},jspb.Map.prototype.keys=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++)e.push(this.map_[t[r]].key);return new jspb.Map.ArrayIteratorIterable_(e)},jspb.Map.prototype.values=function(){var e=[],t=this.stringKeys_();t.sort();for(var r=0;r<t.length;r++)e.push(this.wrapEntry_(this.map_[t[r]]));return new jspb.Map.ArrayIteratorIterable_(e)},jspb.Map.prototype.forEach=function(e,t){var r=this.stringKeys_();r.sort();for(var o=0;o<r.length;o++){var n=this.map_[r[o]];e.call(t,this.wrapEntry_(n),n.key,this)}},jspb.Map.prototype.set=function(e,t){var r=new jspb.Map.Entry_(e);return this.valueCtor_?(r.valueWrapper=t,r.value=t.toArray()):r.value=t,this.map_[e.toString()]=r,this.arrClean=!1,this},jspb.Map.prototype.wrapEntry_=function(e){return this.valueCtor_?(e.valueWrapper||(e.valueWrapper=new this.valueCtor_(e.value)),e.valueWrapper):e.value},jspb.Map.prototype.get=function(e){if(e=this.map_[e.toString()])return this.wrapEntry_(e)},jspb.Map.prototype.has=function(e){return e.toString()in this.map_},jspb.Map.prototype.serializeBinary=function(e,t,r,o,n){var i=this.stringKeys_();i.sort();for(var s=0;s<i.length;s++){var a=this.map_[i[s]];t.beginSubMessage(e),r.call(t,1,a.key),this.valueCtor_?o.call(t,2,this.wrapEntry_(a),n):o.call(t,2,a.value),t.endSubMessage()}},jspb.Map.deserializeBinary=function(e,t,r,o,n){for(var i=void 0,s=void 0;t.nextField()&&!t.isEndGroup();){var a=t.getFieldNumber();1==a?i=r.call(t):2==a&&(e.valueCtor_?(s=new e.valueCtor_,o.call(t,s,n)):s=o.call(t))}goog.asserts.assert(void 0!=i),goog.asserts.assert(void 0!=s),e.set(i,s)},jspb.Map.prototype.stringKeys_=function(){var e,t=this.map_,r=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&r.push(e);return r},jspb.Map.Entry_=function(e,t){this.key=e,this.value=t,this.valueWrapper=void 0},goog.array={},goog.NATIVE_ARRAY_PROTOTYPES=goog.TRUSTED_SITE,goog.array.ASSUME_NATIVE_FUNCTIONS=!1,goog.array.peek=function(e){return e[e.length-1]},goog.array.last=goog.array.peek,goog.array.indexOf=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.indexOf)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.indexOf.call(e,t,r)}:function(e,t,r){if(r=null==r?0:0>r?Math.max(0,e.length+r):r,goog.isString(e))return goog.isString(t)&&1==t.length?e.indexOf(t,r):-1;for(;r<e.length;r++)if(r in e&&e[r]===t)return r;return-1},goog.array.lastIndexOf=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.lastIndexOf)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.lastIndexOf.call(e,t,null==r?e.length-1:r)}:function(e,t,r){if(r=null==r?e.length-1:r,0>r&&(r=Math.max(0,e.length+r)),goog.isString(e))return goog.isString(t)&&1==t.length?e.lastIndexOf(t,r):-1;for(;0<=r;r--)if(r in e&&e[r]===t)return r;return-1},goog.array.forEach=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.forEach)?function(e,t,r){goog.asserts.assert(null!=e.length),Array.prototype.forEach.call(e,t,r)}:function(e,t,r){for(var o=e.length,n=goog.isString(e)?e.split(""):e,i=0;i<o;i++)i in n&&t.call(r,n[i],i,e)},goog.array.forEachRight=function(e,t,r){for(var o=e.length,n=goog.isString(e)?e.split(""):e,o=o-1;0<=o;--o)o in n&&t.call(r,n[o],o,e)},goog.array.filter=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.filter)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.filter.call(e,t,r)}:function(e,t,r){for(var o=e.length,n=[],i=0,s=goog.isString(e)?e.split(""):e,a=0;a<o;a++)if(a in s){var u=s[a];t.call(r,u,a,e)&&(n[i++]=u)}return n},goog.array.map=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.map)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.map.call(e,t,r)}:function(e,t,r){for(var o=e.length,n=Array(o),i=goog.isString(e)?e.split(""):e,s=0;s<o;s++)s in i&&(n[s]=t.call(r,i[s],s,e));return n},goog.array.reduce=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduce)?function(e,t,r,o){return goog.asserts.assert(null!=e.length),o&&(t=goog.bind(t,o)),Array.prototype.reduce.call(e,t,r)}:function(e,t,r,o){var n=r;return goog.array.forEach(e,function(r,i){n=t.call(o,n,r,i,e)}),n},goog.array.reduceRight=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduceRight)?function(e,t,r,o){return goog.asserts.assert(null!=e.length),goog.asserts.assert(null!=t),o&&(t=goog.bind(t,o)),Array.prototype.reduceRight.call(e,t,r)}:function(e,t,r,o){var n=r;return goog.array.forEachRight(e,function(r,i){n=t.call(o,n,r,i,e)}),n},goog.array.some=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.some)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.some.call(e,t,r)}:function(e,t,r){for(var o=e.length,n=goog.isString(e)?e.split(""):e,i=0;i<o;i++)if(i in n&&t.call(r,n[i],i,e))return!0;return!1},goog.array.every=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.every)?function(e,t,r){return goog.asserts.assert(null!=e.length),Array.prototype.every.call(e,t,r)}:function(e,t,r){for(var o=e.length,n=goog.isString(e)?e.split(""):e,i=0;i<o;i++)if(i in n&&!t.call(r,n[i],i,e))return!1;return!0},goog.array.count=function(e,t,r){var o=0;return goog.array.forEach(e,function(e,n,i){t.call(r,e,n,i)&&++o},r),o},goog.array.find=function(e,t,r){return t=goog.array.findIndex(e,t,r),0>t?null:goog.isString(e)?e.charAt(t):e[t]},goog.array.findIndex=function(e,t,r){for(var o=e.length,n=goog.isString(e)?e.split(""):e,i=0;i<o;i++)if(i in n&&t.call(r,n[i],i,e))return i;return-1},goog.array.findRight=function(e,t,r){return t=goog.array.findIndexRight(e,t,r),
0>t?null:goog.isString(e)?e.charAt(t):e[t]},goog.array.findIndexRight=function(e,t,r){for(var o=e.length,n=goog.isString(e)?e.split(""):e,o=o-1;0<=o;o--)if(o in n&&t.call(r,n[o],o,e))return o;return-1},goog.array.contains=function(e,t){return 0<=goog.array.indexOf(e,t)},goog.array.isEmpty=function(e){return 0==e.length},goog.array.clear=function(e){if(!goog.isArray(e))for(var t=e.length-1;0<=t;t--)delete e[t];e.length=0},goog.array.insert=function(e,t){goog.array.contains(e,t)||e.push(t)},goog.array.insertAt=function(e,t,r){goog.array.splice(e,r,0,t)},goog.array.insertArrayAt=function(e,t,r){goog.partial(goog.array.splice,e,r,0).apply(null,t)},goog.array.insertBefore=function(e,t,r){var o;2==arguments.length||0>(o=goog.array.indexOf(e,r))?e.push(t):goog.array.insertAt(e,t,o)},goog.array.remove=function(e,t){var r,o=goog.array.indexOf(e,t);return(r=0<=o)&&goog.array.removeAt(e,o),r},goog.array.removeAt=function(e,t){return goog.asserts.assert(null!=e.length),1==Array.prototype.splice.call(e,t,1).length},goog.array.removeIf=function(e,t,r){return 0<=(t=goog.array.findIndex(e,t,r))&&(goog.array.removeAt(e,t),!0)},goog.array.removeAllIf=function(e,t,r){var o=0;return goog.array.forEachRight(e,function(n,i){t.call(r,n,i,e)&&goog.array.removeAt(e,i)&&o++}),o},goog.array.concat=function(e){return Array.prototype.concat.apply(Array.prototype,arguments)},goog.array.join=function(e){return Array.prototype.concat.apply(Array.prototype,arguments)},goog.array.toArray=function(e){var t=e.length;if(0<t){for(var r=Array(t),o=0;o<t;o++)r[o]=e[o];return r}return[]},goog.array.clone=goog.array.toArray,goog.array.extend=function(e,t){for(var r=1;r<arguments.length;r++){var o=arguments[r];if(goog.isArrayLike(o)){var n=e.length||0,i=o.length||0;e.length=n+i;for(var s=0;s<i;s++)e[n+s]=o[s]}else e.push(o)}},goog.array.splice=function(e,t,r,o){return goog.asserts.assert(null!=e.length),Array.prototype.splice.apply(e,goog.array.slice(arguments,1))},goog.array.slice=function(e,t,r){return goog.asserts.assert(null!=e.length),2>=arguments.length?Array.prototype.slice.call(e,t):Array.prototype.slice.call(e,t,r)},goog.array.removeDuplicates=function(e,t,r){t=t||e;var o=function u(e){return goog.isObject(e)?"o"+goog.getUid(e):("undefined"==typeof e?"undefined":(0,_typeof3["default"])(e)).charAt(0)+e};r=r||o;for(var o={},n=0,i=0;i<e.length;){var s=e[i++],a=r(s);Object.prototype.hasOwnProperty.call(o,a)||(o[a]=!0,t[n++]=s)}t.length=n},goog.array.binarySearch=function(e,t,r){return goog.array.binarySearch_(e,r||goog.array.defaultCompare,!1,t)},goog.array.binarySelect=function(e,t,r){return goog.array.binarySearch_(e,t,!0,void 0,r)},goog.array.binarySearch_=function(e,t,r,o,n){for(var i,s=0,a=e.length;s<a;){var u,p=s+a>>1;u=r?t.call(n,e[p],p,e):t(o,e[p]),0<u?s=p+1:(a=p,i=!u)}return i?s:~s},goog.array.sort=function(e,t){e.sort(t||goog.array.defaultCompare)},goog.array.stableSort=function(e,t){for(var r=0;r<e.length;r++)e[r]={index:r,value:e[r]};var o=t||goog.array.defaultCompare;for(goog.array.sort(e,function(e,t){return o(e.value,t.value)||e.index-t.index}),r=0;r<e.length;r++)e[r]=e[r].value},goog.array.sortByKey=function(e,t,r){var o=r||goog.array.defaultCompare;goog.array.sort(e,function(e,r){return o(t(e),t(r))})},goog.array.sortObjectsByKey=function(e,t,r){goog.array.sortByKey(e,function(e){return e[t]},r)},goog.array.isSorted=function(e,t,r){t=t||goog.array.defaultCompare;for(var o=1;o<e.length;o++){var n=t(e[o-1],e[o]);if(0<n||0==n&&r)return!1}return!0},goog.array.equals=function(e,t,r){if(!goog.isArrayLike(e)||!goog.isArrayLike(t)||e.length!=t.length)return!1;var o=e.length;r=r||goog.array.defaultCompareEquality;for(var n=0;n<o;n++)if(!r(e[n],t[n]))return!1;return!0},goog.array.compare3=function(e,t,r){r=r||goog.array.defaultCompare;for(var o=Math.min(e.length,t.length),n=0;n<o;n++){var i=r(e[n],t[n]);if(0!=i)return i}return goog.array.defaultCompare(e.length,t.length)},goog.array.defaultCompare=function(e,t){return e>t?1:e<t?-1:0},goog.array.inverseDefaultCompare=function(e,t){return-goog.array.defaultCompare(e,t)},goog.array.defaultCompareEquality=function(e,t){return e===t},goog.array.binaryInsert=function(e,t,r){return 0>(r=goog.array.binarySearch(e,t,r))&&(goog.array.insertAt(e,t,-(r+1)),!0)},goog.array.binaryRemove=function(e,t,r){return 0<=(t=goog.array.binarySearch(e,t,r))&&goog.array.removeAt(e,t)},goog.array.bucket=function(e,t,r){for(var o={},n=0;n<e.length;n++){var i=e[n],s=t.call(r,i,n,e);goog.isDef(s)&&(o[s]||(o[s]=[])).push(i)}return o},goog.array.toObject=function(e,t,r){var o={};return goog.array.forEach(e,function(n,i){o[t.call(r,n,i,e)]=n}),o},goog.array.range=function(e,t,r){var o=[],n=0,i=e;if(r=r||1,void 0!==t&&(n=e,i=t),0>r*(i-n))return[];if(0<r)for(e=n;e<i;e+=r)o.push(e);else for(e=n;e>i;e+=r)o.push(e);return o},goog.array.repeat=function(e,t){for(var r=[],o=0;o<t;o++)r[o]=e;return r},goog.array.flatten=function(e){for(var t=[],r=0;r<arguments.length;r++){var o=arguments[r];if(goog.isArray(o))for(var n=0;n<o.length;n+=8192)for(var i=goog.array.slice(o,n,n+8192),i=goog.array.flatten.apply(null,i),s=0;s<i.length;s++)t.push(i[s]);else t.push(o)}return t},goog.array.rotate=function(e,t){return goog.asserts.assert(null!=e.length),e.length&&(t%=e.length,0<t?Array.prototype.unshift.apply(e,e.splice(-t,t)):0>t&&Array.prototype.push.apply(e,e.splice(0,-t))),e},goog.array.moveItem=function(e,t,r){goog.asserts.assert(0<=t&&t<e.length),goog.asserts.assert(0<=r&&r<e.length),t=Array.prototype.splice.call(e,t,1),Array.prototype.splice.call(e,r,0,t[0])},goog.array.zip=function(e){if(!arguments.length)return[];for(var t=[],r=arguments[0].length,o=1;o<arguments.length;o++)arguments[o].length<r&&(r=arguments[o].length);for(o=0;o<r;o++){for(var n=[],i=0;i<arguments.length;i++)n.push(arguments[i][o]);t.push(n)}return t},goog.array.shuffle=function(e,t){for(var r=t||Math.random,o=e.length-1;0<o;o--){var n=Math.floor(r()*(o+1)),i=e[o];e[o]=e[n],e[n]=i}},goog.array.copyByIndex=function(e,t){var r=[];return goog.array.forEach(t,function(t){r.push(e[t])}),r},goog.crypt={},goog.crypt.stringToByteArray=function(e){for(var t=[],r=0,o=0;o<e.length;o++){for(var n=e.charCodeAt(o);255<n;)t[r++]=255&n,n>>=8;t[r++]=n}return t},goog.crypt.byteArrayToString=function(e){if(8192>=e.length)return String.fromCharCode.apply(null,e);for(var t="",r=0;r<e.length;r+=8192)var o=goog.array.slice(e,r,r+8192),t=t+String.fromCharCode.apply(null,o);return t},goog.crypt.byteArrayToHex=function(e){return goog.array.map(e,function(e){return e=e.toString(16),1<e.length?e:"0"+e}).join("")},goog.crypt.hexToByteArray=function(e){goog.asserts.assert(0==e.length%2,"Key string length must be multiple of 2");for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substring(r,r+2),16));return t},goog.crypt.stringToUtf8ByteArray=function(e){for(var t=[],r=0,o=0;o<e.length;o++){var n=e.charCodeAt(o);128>n?t[r++]=n:(2048>n?t[r++]=n>>6|192:(55296==(64512&n)&&o+1<e.length&&56320==(64512&e.charCodeAt(o+1))?(n=65536+((1023&n)<<10)+(1023&e.charCodeAt(++o)),t[r++]=n>>18|240,t[r++]=n>>12&63|128):t[r++]=n>>12|224,t[r++]=n>>6&63|128),t[r++]=63&n|128)}return t},goog.crypt.utf8ByteArrayToString=function(e){for(var t=[],r=0,o=0;r<e.length;){var n=e[r++];if(128>n)t[o++]=String.fromCharCode(n);else if(191<n&&224>n){var i=e[r++];t[o++]=String.fromCharCode((31&n)<<6|63&i)}else if(239<n&&365>n){var i=e[r++],s=e[r++],a=e[r++],n=((7&n)<<18|(63&i)<<12|(63&s)<<6|63&a)-65536;t[o++]=String.fromCharCode(55296+(n>>10)),t[o++]=String.fromCharCode(56320+(1023&n))}else i=e[r++],s=e[r++],t[o++]=String.fromCharCode((15&n)<<12|(63&i)<<6|63&s)}return t.join("")},goog.crypt.xorByteArray=function(e,t){goog.asserts.assert(e.length==t.length,"XOR array lengths must match");for(var r=[],o=0;o<e.length;o++)r.push(e[o]^t[o]);return r},goog.labs={},goog.labs.userAgent={},goog.labs.userAgent.util={},goog.labs.userAgent.util.getNativeUserAgentString_=function(){var e=goog.labs.userAgent.util.getNavigator_();return e&&(e=e.userAgent)?e:""},goog.labs.userAgent.util.getNavigator_=function(){return goog.global.navigator},goog.labs.userAgent.util.userAgent_=goog.labs.userAgent.util.getNativeUserAgentString_(),goog.labs.userAgent.util.setUserAgent=function(e){goog.labs.userAgent.util.userAgent_=e||goog.labs.userAgent.util.getNativeUserAgentString_()},goog.labs.userAgent.util.getUserAgent=function(){return goog.labs.userAgent.util.userAgent_},goog.labs.userAgent.util.matchUserAgent=function(e){var t=goog.labs.userAgent.util.getUserAgent();return goog.string.contains(t,e)},goog.labs.userAgent.util.matchUserAgentIgnoreCase=function(e){var t=goog.labs.userAgent.util.getUserAgent();return goog.string.caseInsensitiveContains(t,e)},goog.labs.userAgent.util.extractVersionTuples=function(e){for(var t,r=RegExp("(\\w[\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),o=[];t=r.exec(e);)o.push([t[1],t[2],t[3]||void 0]);return o},goog.labs.userAgent.platform={},goog.labs.userAgent.platform.isAndroid=function(){return goog.labs.userAgent.util.matchUserAgent("Android")},goog.labs.userAgent.platform.isIpod=function(){return goog.labs.userAgent.util.matchUserAgent("iPod")},goog.labs.userAgent.platform.isIphone=function(){return goog.labs.userAgent.util.matchUserAgent("iPhone")&&!goog.labs.userAgent.util.matchUserAgent("iPod")&&!goog.labs.userAgent.util.matchUserAgent("iPad")},goog.labs.userAgent.platform.isIpad=function(){return goog.labs.userAgent.util.matchUserAgent("iPad")},goog.labs.userAgent.platform.isIos=function(){return goog.labs.userAgent.platform.isIphone()||goog.labs.userAgent.platform.isIpad()||goog.labs.userAgent.platform.isIpod()},goog.labs.userAgent.platform.isMacintosh=function(){return goog.labs.userAgent.util.matchUserAgent("Macintosh")},goog.labs.userAgent.platform.isLinux=function(){return goog.labs.userAgent.util.matchUserAgent("Linux")},goog.labs.userAgent.platform.isWindows=function(){return goog.labs.userAgent.util.matchUserAgent("Windows")},goog.labs.userAgent.platform.isChromeOS=function(){return goog.labs.userAgent.util.matchUserAgent("CrOS")},goog.labs.userAgent.platform.getVersion=function(){var e=goog.labs.userAgent.util.getUserAgent(),t="";return goog.labs.userAgent.platform.isWindows()?(t=/Windows (?:NT|Phone) ([0-9.]+)/,t=(e=t.exec(e))?e[1]:"0.0"):goog.labs.userAgent.platform.isIos()?(t=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/,t=(e=t.exec(e))&&e[1].replace(/_/g,".")):goog.labs.userAgent.platform.isMacintosh()?(t=/Mac OS X ([0-9_.]+)/,t=(e=t.exec(e))?e[1].replace(/_/g,"."):"10"):goog.labs.userAgent.platform.isAndroid()?(t=/Android\s+([^\);]+)(\)|;)/,t=(e=t.exec(e))&&e[1]):goog.labs.userAgent.platform.isChromeOS()&&(t=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/,t=(e=t.exec(e))&&e[1]),t||""},goog.labs.userAgent.platform.isVersionOrHigher=function(e){return 0<=goog.string.compareVersions(goog.labs.userAgent.platform.getVersion(),e)},goog.object={},goog.object.forEach=function(e,t,r){for(var o in e)t.call(r,e[o],o,e)},goog.object.filter=function(e,t,r){var o,n={};for(o in e)t.call(r,e[o],o,e)&&(n[o]=e[o]);return n},goog.object.map=function(e,t,r){var o,n={};for(o in e)n[o]=t.call(r,e[o],o,e);return n},goog.object.some=function(e,t,r){for(var o in e)if(t.call(r,e[o],o,e))return!0;return!1},goog.object.every=function(e,t,r){for(var o in e)if(!t.call(r,e[o],o,e))return!1;return!0},goog.object.getCount=function(e){var t,r=0;for(t in e)r++;return r},goog.object.getAnyKey=function(e){for(var t in e)return t},goog.object.getAnyValue=function(e){for(var t in e)return e[t]},goog.object.contains=function(e,t){return goog.object.containsValue(e,t)},goog.object.getValues=function(e){var t,r=[],o=0;for(t in e)r[o++]=e[t];return r},goog.object.getKeys=function(e){var t,r=[],o=0;for(t in e)r[o++]=t;return r},goog.object.getValueByKeys=function(e,t){for(var r=goog.isArrayLike(t),o=r?t:arguments,r=r?0:1;r<o.length&&(e=e[o[r]],goog.isDef(e));r++);return e},goog.object.containsKey=function(e,t){return null!==e&&t in e},goog.object.containsValue=function(e,t){for(var r in e)if(e[r]==t)return!0;return!1},goog.object.findKey=function(e,t,r){for(var o in e)if(t.call(r,e[o],o,e))return o},goog.object.findValue=function(e,t,r){return(t=goog.object.findKey(e,t,r))&&e[t]},goog.object.isEmpty=function(e){for(var t in e)return!1;return!0},goog.object.clear=function(e){for(var t in e)delete e[t]},goog.object.remove=function(e,t){var r;return(r=t in e)&&delete e[t],r},goog.object.add=function(e,t,r){if(null!==e&&t in e)throw Error('The object already contains the key "'+t+'"');goog.object.set(e,t,r)},goog.object.get=function(e,t,r){return null!==e&&t in e?e[t]:r},goog.object.set=function(e,t,r){e[t]=r},goog.object.setIfUndefined=function(e,t,r){return t in e?e[t]:e[t]=r},goog.object.setWithReturnValueIfNotSet=function(e,t,r){return t in e?e[t]:(r=r(),e[t]=r)},goog.object.equals=function(e,t){for(var r in e)if(!(r in t)||e[r]!==t[r])return!1;for(r in t)if(!(r in e))return!1;return!0},goog.object.clone=function(e){var t,r={};for(t in e)r[t]=e[t];return r},goog.object.unsafeClone=function(e){var t=goog.typeOf(e);if("object"==t||"array"==t){if(goog.isFunction(e.clone))return e.clone();var r,t="array"==t?[]:{};for(r in e)t[r]=goog.object.unsafeClone(e[r]);return t}return e},goog.object.transpose=function(e){var t,r={};for(t in e)r[e[t]]=t;return r},goog.object.PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),goog.object.extend=function(e,t){for(var r,o,n=1;n<arguments.length;n++){o=arguments[n];for(r in o)e[r]=o[r];for(var i=0;i<goog.object.PROTOTYPE_FIELDS_.length;i++)r=goog.object.PROTOTYPE_FIELDS_[i],Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}},goog.object.create=function(e){var t=arguments.length;if(1==t&&goog.isArray(arguments[0]))return goog.object.create.apply(null,arguments[0]);if(t%2)throw Error("Uneven number of arguments");for(var r={},o=0;o<t;o+=2)r[arguments[o]]=arguments[o+1];return r},goog.object.createSet=function(e){var t=arguments.length;if(1==t&&goog.isArray(arguments[0]))return goog.object.createSet.apply(null,arguments[0]);for(var r={},o=0;o<t;o++)r[arguments[o]]=!0;return r},goog.object.createImmutableView=function(e){var t=e;return _isFrozen2["default"]&&!(0,_isFrozen2["default"])(e)&&(t=(0,_create2["default"])(e),(0,_freeze2["default"])(t)),t},goog.object.isImmutableView=function(e){return!!_isFrozen2["default"]&&(0,_isFrozen2["default"])(e)},goog.labs.userAgent.browser={},goog.labs.userAgent.browser.matchOpera_=function(){return goog.labs.userAgent.util.matchUserAgent("Opera")||goog.labs.userAgent.util.matchUserAgent("OPR")},goog.labs.userAgent.browser.matchIE_=function(){return goog.labs.userAgent.util.matchUserAgent("Trident")||goog.labs.userAgent.util.matchUserAgent("MSIE")},goog.labs.userAgent.browser.matchEdge_=function(){return goog.labs.userAgent.util.matchUserAgent("Edge")},goog.labs.userAgent.browser.matchFirefox_=function(){return goog.labs.userAgent.util.matchUserAgent("Firefox")},goog.labs.userAgent.browser.matchSafari_=function(){return goog.labs.userAgent.util.matchUserAgent("Safari")&&!(goog.labs.userAgent.browser.matchChrome_()||goog.labs.userAgent.browser.matchCoast_()||goog.labs.userAgent.browser.matchOpera_()||goog.labs.userAgent.browser.matchEdge_()||goog.labs.userAgent.browser.isSilk()||goog.labs.userAgent.util.matchUserAgent("Android"))},goog.labs.userAgent.browser.matchCoast_=function(){return goog.labs.userAgent.util.matchUserAgent("Coast")},goog.labs.userAgent.browser.matchIosWebview_=function(){return(goog.labs.userAgent.util.matchUserAgent("iPad")||goog.labs.userAgent.util.matchUserAgent("iPhone"))&&!goog.labs.userAgent.browser.matchSafari_()&&!goog.labs.userAgent.browser.matchChrome_()&&!goog.labs.userAgent.browser.matchCoast_()&&goog.labs.userAgent.util.matchUserAgent("AppleWebKit")},goog.labs.userAgent.browser.matchChrome_=function(){return(goog.labs.userAgent.util.matchUserAgent("Chrome")||goog.labs.userAgent.util.matchUserAgent("CriOS"))&&!goog.labs.userAgent.browser.matchOpera_()&&!goog.labs.userAgent.browser.matchEdge_()},goog.labs.userAgent.browser.matchAndroidBrowser_=function(){return goog.labs.userAgent.util.matchUserAgent("Android")&&!(goog.labs.userAgent.browser.isChrome()||goog.labs.userAgent.browser.isFirefox()||goog.labs.userAgent.browser.isOpera()||goog.labs.userAgent.browser.isSilk())},goog.labs.userAgent.browser.isOpera=goog.labs.userAgent.browser.matchOpera_,goog.labs.userAgent.browser.isIE=goog.labs.userAgent.browser.matchIE_,goog.labs.userAgent.browser.isEdge=goog.labs.userAgent.browser.matchEdge_,goog.labs.userAgent.browser.isFirefox=goog.labs.userAgent.browser.matchFirefox_,goog.labs.userAgent.browser.isSafari=goog.labs.userAgent.browser.matchSafari_,goog.labs.userAgent.browser.isCoast=goog.labs.userAgent.browser.matchCoast_,goog.labs.userAgent.browser.isIosWebview=goog.labs.userAgent.browser.matchIosWebview_,goog.labs.userAgent.browser.isChrome=goog.labs.userAgent.browser.matchChrome_,goog.labs.userAgent.browser.isAndroidBrowser=goog.labs.userAgent.browser.matchAndroidBrowser_,goog.labs.userAgent.browser.isSilk=function(){return goog.labs.userAgent.util.matchUserAgent("Silk")},goog.labs.userAgent.browser.getVersion=function(){function e(e){return e=goog.array.find(e,o),r[e]||""}var t=goog.labs.userAgent.util.getUserAgent();if(goog.labs.userAgent.browser.isIE())return goog.labs.userAgent.browser.getIEVersion_(t);var t=goog.labs.userAgent.util.extractVersionTuples(t),r={};goog.array.forEach(t,function(e){r[e[0]]=e[1]});var o=goog.partial(goog.object.containsKey,r);return goog.labs.userAgent.browser.isOpera()?e(["Version","Opera","OPR"]):goog.labs.userAgent.browser.isEdge()?e(["Edge"]):goog.labs.userAgent.browser.isChrome()?e(["Chrome","CriOS"]):(t=t[2])&&t[1]||""},goog.labs.userAgent.browser.isVersionOrHigher=function(e){return 0<=goog.string.compareVersions(goog.labs.userAgent.browser.getVersion(),e)},goog.labs.userAgent.browser.getIEVersion_=function(e){var t=/rv: *([\d\.]*)/.exec(e);if(t&&t[1])return t[1];var t="",r=/MSIE +([\d\.]+)/.exec(e);if(r&&r[1])if(e=/Trident\/(\d.\d)/.exec(e),"7.0"==r[1])if(e&&e[1])switch(e[1]){case"4.0":t="8.0";break;case"5.0":t="9.0";break;case"6.0":t="10.0";break;case"7.0":t="11.0"}else t="7.0";else t=r[1];return t},goog.labs.userAgent.engine={},goog.labs.userAgent.engine.isPresto=function(){return goog.labs.userAgent.util.matchUserAgent("Presto")},goog.labs.userAgent.engine.isTrident=function(){return goog.labs.userAgent.util.matchUserAgent("Trident")||goog.labs.userAgent.util.matchUserAgent("MSIE")},goog.labs.userAgent.engine.isEdge=function(){return goog.labs.userAgent.util.matchUserAgent("Edge")},goog.labs.userAgent.engine.isWebKit=function(){return goog.labs.userAgent.util.matchUserAgentIgnoreCase("WebKit")&&!goog.labs.userAgent.engine.isEdge()},goog.labs.userAgent.engine.isGecko=function(){return goog.labs.userAgent.util.matchUserAgent("Gecko")&&!goog.labs.userAgent.engine.isWebKit()&&!goog.labs.userAgent.engine.isTrident()&&!goog.labs.userAgent.engine.isEdge()},goog.labs.userAgent.engine.getVersion=function(){var e=goog.labs.userAgent.util.getUserAgent();if(e){var e=goog.labs.userAgent.util.extractVersionTuples(e),t=goog.labs.userAgent.engine.getEngineTuple_(e);if(t)return"Gecko"==t[0]?goog.labs.userAgent.engine.getVersionForKey_(e,"Firefox"):t[1];var r,e=e[0];if(e&&(r=e[2])&&(r=/Trident\/([^\s;]+)/.exec(r)))return r[1]}return""},goog.labs.userAgent.engine.getEngineTuple_=function(e){if(!goog.labs.userAgent.engine.isEdge())return e[1];for(var t=0;t<e.length;t++){var r=e[t];if("Edge"==r[0])return r}},goog.labs.userAgent.engine.isVersionOrHigher=function(e){return 0<=goog.string.compareVersions(goog.labs.userAgent.engine.getVersion(),e)},goog.labs.userAgent.engine.getVersionForKey_=function(e,t){var r=goog.array.find(e,function(e){return t==e[0]});return r&&r[1]||""},goog.userAgent={},goog.userAgent.ASSUME_IE=!1,goog.userAgent.ASSUME_EDGE=!1,goog.userAgent.ASSUME_GECKO=!1,goog.userAgent.ASSUME_WEBKIT=!1,goog.userAgent.ASSUME_MOBILE_WEBKIT=!1,goog.userAgent.ASSUME_OPERA=!1,goog.userAgent.ASSUME_ANY_VERSION=!1,goog.userAgent.BROWSER_KNOWN_=goog.userAgent.ASSUME_IE||goog.userAgent.ASSUME_EDGE||goog.userAgent.ASSUME_GECKO||goog.userAgent.ASSUME_MOBILE_WEBKIT||goog.userAgent.ASSUME_WEBKIT||goog.userAgent.ASSUME_OPERA,goog.userAgent.getUserAgentString=function(){return goog.labs.userAgent.util.getUserAgent()},goog.userAgent.getNavigator=function(){return goog.global.navigator||null},goog.userAgent.OPERA=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_OPERA:goog.labs.userAgent.browser.isOpera(),goog.userAgent.IE=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_IE:goog.labs.userAgent.browser.isIE(),goog.userAgent.EDGE=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_EDGE:goog.labs.userAgent.engine.isEdge(),goog.userAgent.EDGE_OR_IE=goog.userAgent.EDGE||goog.userAgent.IE,goog.userAgent.GECKO=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_GECKO:goog.labs.userAgent.engine.isGecko(),goog.userAgent.WEBKIT=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_WEBKIT||goog.userAgent.ASSUME_MOBILE_WEBKIT:goog.labs.userAgent.engine.isWebKit(),goog.userAgent.isMobile_=function(){return goog.userAgent.WEBKIT&&goog.labs.userAgent.util.matchUserAgent("Mobile")},goog.userAgent.MOBILE=goog.userAgent.ASSUME_MOBILE_WEBKIT||goog.userAgent.isMobile_(),goog.userAgent.SAFARI=goog.userAgent.WEBKIT,goog.userAgent.determinePlatform_=function(){var e=goog.userAgent.getNavigator();return e&&e.platform||""},goog.userAgent.PLATFORM=goog.userAgent.determinePlatform_(),goog.userAgent.ASSUME_MAC=!1,goog.userAgent.ASSUME_WINDOWS=!1,goog.userAgent.ASSUME_LINUX=!1,goog.userAgent.ASSUME_X11=!1,goog.userAgent.ASSUME_ANDROID=!1,goog.userAgent.ASSUME_IPHONE=!1,goog.userAgent.ASSUME_IPAD=!1,goog.userAgent.PLATFORM_KNOWN_=goog.userAgent.ASSUME_MAC||goog.userAgent.ASSUME_WINDOWS||goog.userAgent.ASSUME_LINUX||goog.userAgent.ASSUME_X11||goog.userAgent.ASSUME_ANDROID||goog.userAgent.ASSUME_IPHONE||goog.userAgent.ASSUME_IPAD,goog.userAgent.MAC=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_MAC:goog.labs.userAgent.platform.isMacintosh(),goog.userAgent.WINDOWS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_WINDOWS:goog.labs.userAgent.platform.isWindows(),goog.userAgent.isLegacyLinux_=function(){return goog.labs.userAgent.platform.isLinux()||goog.labs.userAgent.platform.isChromeOS()},goog.userAgent.LINUX=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_LINUX:goog.userAgent.isLegacyLinux_(),goog.userAgent.isX11_=function(){var e=goog.userAgent.getNavigator();return!!e&&goog.string.contains(e.appVersion||"","X11")},goog.userAgent.X11=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_X11:goog.userAgent.isX11_(),goog.userAgent.ANDROID=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_ANDROID:goog.labs.userAgent.platform.isAndroid(),goog.userAgent.IPHONE=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPHONE:goog.labs.userAgent.platform.isIphone(),goog.userAgent.IPAD=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPAD:goog.labs.userAgent.platform.isIpad(),goog.userAgent.operaVersion_=function(){var e=goog.global.opera.version;try{return e()}catch(t){return e}},goog.userAgent.determineVersion_=function(){if(goog.userAgent.OPERA&&goog.global.opera)return goog.userAgent.operaVersion_();var e="",t=goog.userAgent.getVersionRegexResult_();return t&&(e=t?t[1]:""),goog.userAgent.IE&&(t=goog.userAgent.getDocumentMode_())>parseFloat(e)?String(t):e},goog.userAgent.getVersionRegexResult_=function(){var e=goog.userAgent.getUserAgentString();return goog.userAgent.GECKO?/rv\:([^\);]+)(\)|;)/.exec(e):goog.userAgent.EDGE?/Edge\/([\d\.]+)/.exec(e):goog.userAgent.IE?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(e):goog.userAgent.WEBKIT?/WebKit\/(\S+)/.exec(e):void 0},goog.userAgent.getDocumentMode_=function(){var e=goog.global.document;return e?e.documentMode:void 0},goog.userAgent.VERSION=goog.userAgent.determineVersion_(),goog.userAgent.compare=function(e,t){return goog.string.compareVersions(e,t)},goog.userAgent.isVersionOrHigherCache_={},goog.userAgent.isVersionOrHigher=function(e){return goog.userAgent.ASSUME_ANY_VERSION||goog.userAgent.isVersionOrHigherCache_[e]||(goog.userAgent.isVersionOrHigherCache_[e]=0<=goog.string.compareVersions(goog.userAgent.VERSION,e))},goog.userAgent.isVersion=goog.userAgent.isVersionOrHigher,goog.userAgent.isDocumentModeOrHigher=function(e){return Number(goog.userAgent.DOCUMENT_MODE)>=e},goog.userAgent.isDocumentMode=goog.userAgent.isDocumentModeOrHigher,goog.userAgent.DOCUMENT_MODE=function(){var e=goog.global.document,t=goog.userAgent.getDocumentMode_();return e&&goog.userAgent.IE?t||("CSS1Compat"==e.compatMode?parseInt(goog.userAgent.VERSION,10):5):void 0}(),goog.userAgent.product={},goog.userAgent.product.ASSUME_FIREFOX=!1,goog.userAgent.product.ASSUME_IPHONE=!1,goog.userAgent.product.ASSUME_IPAD=!1,goog.userAgent.product.ASSUME_ANDROID=!1,goog.userAgent.product.ASSUME_CHROME=!1,goog.userAgent.product.ASSUME_SAFARI=!1,goog.userAgent.product.PRODUCT_KNOWN_=goog.userAgent.ASSUME_IE||goog.userAgent.ASSUME_EDGE||goog.userAgent.ASSUME_OPERA||goog.userAgent.product.ASSUME_FIREFOX||goog.userAgent.product.ASSUME_IPHONE||goog.userAgent.product.ASSUME_IPAD||goog.userAgent.product.ASSUME_ANDROID||goog.userAgent.product.ASSUME_CHROME||goog.userAgent.product.ASSUME_SAFARI,goog.userAgent.product.OPERA=goog.userAgent.OPERA,goog.userAgent.product.IE=goog.userAgent.IE,goog.userAgent.product.EDGE=goog.userAgent.EDGE,goog.userAgent.product.FIREFOX=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_FIREFOX:goog.labs.userAgent.browser.isFirefox(),goog.userAgent.product.isIphoneOrIpod_=function(){return goog.labs.userAgent.platform.isIphone()||goog.labs.userAgent.platform.isIpod()},goog.userAgent.product.IPHONE=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_IPHONE:goog.userAgent.product.isIphoneOrIpod_(),goog.userAgent.product.IPAD=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_IPAD:goog.labs.userAgent.platform.isIpad(),goog.userAgent.product.ANDROID=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_ANDROID:goog.labs.userAgent.browser.isAndroidBrowser(),goog.userAgent.product.CHROME=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_CHROME:goog.labs.userAgent.browser.isChrome(),goog.userAgent.product.isSafariDesktop_=function(){return goog.labs.userAgent.browser.isSafari()&&!goog.labs.userAgent.platform.isIos()},goog.userAgent.product.SAFARI=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_SAFARI:goog.userAgent.product.isSafariDesktop_(),goog.crypt.base64={},goog.crypt.base64.byteToCharMap_=null,goog.crypt.base64.charToByteMap_=null,goog.crypt.base64.byteToCharMapWebSafe_=null,goog.crypt.base64.ENCODED_VALS_BASE="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",goog.crypt.base64.ENCODED_VALS=goog.crypt.base64.ENCODED_VALS_BASE+"+/=",goog.crypt.base64.ENCODED_VALS_WEBSAFE=goog.crypt.base64.ENCODED_VALS_BASE+"-_.",goog.crypt.base64.ASSUME_NATIVE_SUPPORT_=goog.userAgent.GECKO||goog.userAgent.WEBKIT&&!goog.userAgent.product.SAFARI||goog.userAgent.OPERA,goog.crypt.base64.HAS_NATIVE_ENCODE_=goog.crypt.base64.ASSUME_NATIVE_SUPPORT_||"function"==typeof goog.global.btoa,goog.crypt.base64.HAS_NATIVE_DECODE_=goog.crypt.base64.ASSUME_NATIVE_SUPPORT_||!goog.userAgent.product.SAFARI&&!goog.userAgent.IE&&"function"==typeof goog.global.atob,goog.crypt.base64.encodeByteArray=function(e,t){goog.asserts.assert(goog.isArrayLike(e),"encodeByteArray takes an array as a parameter"),goog.crypt.base64.init_();for(var r=t?goog.crypt.base64.byteToCharMapWebSafe_:goog.crypt.base64.byteToCharMap_,o=[],n=0;n<e.length;n+=3){var i=e[n],s=n+1<e.length,a=s?e[n+1]:0,u=n+2<e.length,p=u?e[n+2]:0,g=i>>2,i=(3&i)<<4|a>>4,a=(15&a)<<2|p>>6,p=63&p;u||(p=64,s||(a=64)),o.push(r[g],r[i],r[a],r[p])}return o.join("")},goog.crypt.base64.encodeString=function(e,t){return goog.crypt.base64.HAS_NATIVE_ENCODE_&&!t?goog.global.btoa(e):goog.crypt.base64.encodeByteArray(goog.crypt.stringToByteArray(e),t)},goog.crypt.base64.decodeString=function(e,t){if(goog.crypt.base64.HAS_NATIVE_DECODE_&&!t)return goog.global.atob(e);var r="";return goog.crypt.base64.decodeStringInternal_(e,function(e){r+=String.fromCharCode(e)}),r},goog.crypt.base64.decodeStringToByteArray=function(e,t){var r=[];return goog.crypt.base64.decodeStringInternal_(e,function(e){r.push(e)}),r},goog.crypt.base64.decodeStringToUint8Array=function(e){goog.asserts.assert(!goog.userAgent.IE||goog.userAgent.isVersionOrHigher("10"),"Browser does not support typed arrays");var t=new Uint8Array(Math.ceil(3*e.length/4)),r=0;return goog.crypt.base64.decodeStringInternal_(e,function(e){t[r++]=e}),t.subarray(0,r)},goog.crypt.base64.decodeStringInternal_=function(e,t){function r(t){for(;o<e.length;){var r=e.charAt(o++),n=goog.crypt.base64.charToByteMap_[r];if(null!=n)return n;if(!goog.string.isEmptyOrWhitespace(r))throw Error("Unknown base64 encoding at char: "+r)}return t}goog.crypt.base64.init_();for(var o=0;;){var n=r(-1),i=r(0),s=r(64),a=r(64);if(64===a&&-1===n)break;t(n<<2|i>>4),64!=s&&(t(i<<4&240|s>>2),64!=a&&t(s<<6&192|a))}},goog.crypt.base64.init_=function(){if(!goog.crypt.base64.byteToCharMap_){goog.crypt.base64.byteToCharMap_={},goog.crypt.base64.charToByteMap_={},goog.crypt.base64.byteToCharMapWebSafe_={};for(var e=0;e<goog.crypt.base64.ENCODED_VALS.length;e++)goog.crypt.base64.byteToCharMap_[e]=goog.crypt.base64.ENCODED_VALS.charAt(e),goog.crypt.base64.charToByteMap_[goog.crypt.base64.byteToCharMap_[e]]=e,goog.crypt.base64.byteToCharMapWebSafe_[e]=goog.crypt.base64.ENCODED_VALS_WEBSAFE.charAt(e),e>=goog.crypt.base64.ENCODED_VALS_BASE.length&&(goog.crypt.base64.charToByteMap_[goog.crypt.base64.ENCODED_VALS_WEBSAFE.charAt(e)]=e)}},jspb.ExtensionFieldInfo=function(e,t,r,o,n){this.fieldIndex=e,this.fieldName=t,this.ctor=r,this.toObjectFn=o,this.isRepeated=n},jspb.ExtensionFieldBinaryInfo=function(e,t,r,o,n,i){this.fieldInfo=e,this.binaryReaderFn=t,this.binaryWriterFn=r,this.binaryMessageSerializeFn=o,this.binaryMessageDeserializeFn=n,this.isPacked=i},jspb.ExtensionFieldInfo.prototype.isMessageType=function(){return!!this.ctor},jspb.Message=function(){},jspb.Message.GENERATE_TO_OBJECT=!0,jspb.Message.GENERATE_FROM_OBJECT=!goog.DISALLOW_TEST_ONLY_CODE,jspb.Message.GENERATE_TO_STRING=!0,jspb.Message.ASSUME_LOCAL_ARRAYS=!1,jspb.Message.MINIMIZE_MEMORY_ALLOCATIONS=COMPILED,jspb.Message.SUPPORTS_UINT8ARRAY_="function"==typeof Uint8Array,jspb.Message.prototype.getJsPbMessageId=function(){return this.messageId_},jspb.Message.getIndex_=function(e,t){return t+e.arrayIndexOffset_},jspb.Message.initialize=function(e,t,r,o,n,i){if(e.wrappers_=jspb.Message.MINIMIZE_MEMORY_ALLOCATIONS?null:{},t||(t=r?[r]:[]),e.messageId_=r?String(r):void 0,e.arrayIndexOffset_=0===r?-1:0,e.array=t,jspb.Message.initPivotAndExtensionObject_(e,o),e.convertedFloatingPointFields_={},n)for(t=0;t<n.length;t++)r=n[t],r<e.pivot_?(r=jspb.Message.getIndex_(e,r),e.array[r]=e.array[r]||(jspb.Message.MINIMIZE_MEMORY_ALLOCATIONS?jspb.Message.EMPTY_LIST_SENTINEL_:[])):(jspb.Message.maybeInitEmptyExtensionObject_(e),e.extensionObject_[r]=e.extensionObject_[r]||(jspb.Message.MINIMIZE_MEMORY_ALLOCATIONS?jspb.Message.EMPTY_LIST_SENTINEL_:[]));i&&i.length&&goog.array.forEach(i,goog.partial(jspb.Message.computeOneofCase,e))},jspb.Message.EMPTY_LIST_SENTINEL_=goog.DEBUG&&_freeze2["default"]?(0,_freeze2["default"])([]):[],jspb.Message.isArray_=function(e){return jspb.Message.ASSUME_LOCAL_ARRAYS?e instanceof Array:goog.isArray(e)},jspb.Message.initPivotAndExtensionObject_=function(e,t){if(e.array.length){var r=e.array.length-1,o=e.array[r];if(o&&"object"==("undefined"==typeof o?"undefined":(0,_typeof3["default"])(o))&&!jspb.Message.isArray_(o)&&!(jspb.Message.SUPPORTS_UINT8ARRAY_&&o instanceof Uint8Array))return e.pivot_=r-e.arrayIndexOffset_,void(e.extensionObject_=o)}-1<t?(e.pivot_=t,e.extensionObject_=null):e.pivot_=Number.MAX_VALUE},jspb.Message.maybeInitEmptyExtensionObject_=function(e){var t=jspb.Message.getIndex_(e,e.pivot_);
e.array[t]||(e.extensionObject_=e.array[t]={})},jspb.Message.toObjectList=function(e,t,r){for(var o=[],n=0;n<e.length;n++)o[n]=t.call(e[n],r,e[n]);return o},jspb.Message.toObjectExtension=function(e,t,r,o,n){for(var i in r){var s=r[i],a=o.call(e,s);if(null!=a){for(var u in s.fieldName)if(s.fieldName.hasOwnProperty(u))break;t[u]=s.toObjectFn?s.isRepeated?jspb.Message.toObjectList(a,s.toObjectFn,n):s.toObjectFn(n,a):a}}},jspb.Message.serializeBinaryExtensions=function(e,t,r,o){for(var n in r){var i=r[n],s=i.fieldInfo;if(!i.binaryWriterFn)throw Error("Message extension present that was generated without binary serialization support");var a=o.call(e,s);if(null!=a)if(s.isMessageType()){if(!i.binaryMessageSerializeFn)throw Error("Message extension present holding submessage without binary support enabled, and message is being serialized to binary format");i.binaryWriterFn.call(t,s.fieldIndex,a,i.binaryMessageSerializeFn)}else i.binaryWriterFn.call(t,s.fieldIndex,a)}},jspb.Message.readBinaryExtension=function(e,t,r,o,n){var i=r[t.getFieldNumber()];if(i){if(r=i.fieldInfo,!i.binaryReaderFn)throw Error("Deserializing extension whose generated code does not support binary format");var s;r.isMessageType()?(s=new r.ctor,i.binaryReaderFn.call(t,s,i.binaryMessageDeserializeFn)):s=i.binaryReaderFn.call(t),r.isRepeated&&!i.isPacked?(t=o.call(e,r))?t.push(s):n.call(e,r,[s]):n.call(e,r,s)}else t.skipField()},jspb.Message.getField=function(e,t){if(t<e.pivot_){var r=jspb.Message.getIndex_(e,t),o=e.array[r];return o===jspb.Message.EMPTY_LIST_SENTINEL_?e.array[r]=[]:o}if(e.extensionObject_)return o=e.extensionObject_[t],o===jspb.Message.EMPTY_LIST_SENTINEL_?e.extensionObject_[t]=[]:o},jspb.Message.getRepeatedField=function(e,t){if(t<e.pivot_){var r=jspb.Message.getIndex_(e,t),o=e.array[r];return o===jspb.Message.EMPTY_LIST_SENTINEL_?e.array[r]=[]:o}return o=e.extensionObject_[t],o===jspb.Message.EMPTY_LIST_SENTINEL_?e.extensionObject_[t]=[]:o},jspb.Message.getOptionalFloatingPointField=function(e,t){var r=jspb.Message.getField(e,t);return null==r?r:+r},jspb.Message.getRepeatedFloatingPointField=function(e,t){var r=jspb.Message.getRepeatedField(e,t);if(e.convertedFloatingPointFields_||(e.convertedFloatingPointFields_={}),!e.convertedFloatingPointFields_[t]){for(var o=0;o<r.length;o++)r[o]=+r[o];e.convertedFloatingPointFields_[t]=!0}return r},jspb.Message.bytesAsB64=function(e){return null==e||goog.isString(e)?e:jspb.Message.SUPPORTS_UINT8ARRAY_&&e instanceof Uint8Array?goog.crypt.base64.encodeByteArray(e):(goog.asserts.fail("Cannot coerce to b64 string: "+goog.typeOf(e)),null)},jspb.Message.bytesAsU8=function(e){return null==e||e instanceof Uint8Array?e:goog.isString(e)?goog.crypt.base64.decodeStringToUint8Array(e):(goog.asserts.fail("Cannot coerce to Uint8Array: "+goog.typeOf(e)),null)},jspb.Message.bytesListAsB64=function(e){return jspb.Message.assertConsistentTypes_(e),!e.length||goog.isString(e[0])?e:goog.array.map(e,jspb.Message.bytesAsB64)},jspb.Message.bytesListAsU8=function(e){return jspb.Message.assertConsistentTypes_(e),!e.length||e[0]instanceof Uint8Array?e:goog.array.map(e,jspb.Message.bytesAsU8)},jspb.Message.assertConsistentTypes_=function(e){if(goog.DEBUG&&e&&1<e.length){var t=goog.typeOf(e[0]);goog.array.forEach(e,function(e){goog.typeOf(e)!=t&&goog.asserts.fail("Inconsistent type in JSPB repeated field array. Got "+goog.typeOf(e)+" expected "+t)})}},jspb.Message.getFieldWithDefault=function(e,t,r){return e=jspb.Message.getField(e,t),null==e?r:e},jspb.Message.getFieldProto3=jspb.Message.getFieldWithDefault,jspb.Message.getMapField=function(e,t,r,o){return e.wrappers_||(e.wrappers_={}),t in e.wrappers_?e.wrappers_[t]:r?void 0:(r=jspb.Message.getField(e,t),r||(r=[],jspb.Message.setField(e,t,r)),e.wrappers_[t]=new jspb.Map(r,o))},jspb.Message.setField=function(e,t,r){t<e.pivot_?e.array[jspb.Message.getIndex_(e,t)]=r:(jspb.Message.maybeInitEmptyExtensionObject_(e),e.extensionObject_[t]=r)},jspb.Message.addToRepeatedField=function(e,t,r,o){e=jspb.Message.getRepeatedField(e,t),void 0!=o?e.splice(o,0,r):e.push(r)},jspb.Message.setOneofField=function(e,t,r,o){(r=jspb.Message.computeOneofCase(e,r))&&r!==t&&void 0!==o&&(e.wrappers_&&r in e.wrappers_&&(e.wrappers_[r]=void 0),jspb.Message.setField(e,r,void 0)),jspb.Message.setField(e,t,o)},jspb.Message.computeOneofCase=function(e,t){var r,o;return goog.array.forEach(t,function(t){var n=jspb.Message.getField(e,t);goog.isDefAndNotNull(n)&&(r=t,o=n,jspb.Message.setField(e,t,void 0))}),r?(jspb.Message.setField(e,r,o),r):0},jspb.Message.getWrapperField=function(e,t,r,o){if(e.wrappers_||(e.wrappers_={}),!e.wrappers_[r]){var n=jspb.Message.getField(e,r);(o||n)&&(e.wrappers_[r]=new t(n))}return e.wrappers_[r]},jspb.Message.getRepeatedWrapperField=function(e,t,r){return jspb.Message.wrapRepeatedField_(e,t,r),t=e.wrappers_[r],t==jspb.Message.EMPTY_LIST_SENTINEL_&&(t=e.wrappers_[r]=[]),t},jspb.Message.wrapRepeatedField_=function(e,t,r){if(e.wrappers_||(e.wrappers_={}),!e.wrappers_[r]){for(var o=jspb.Message.getRepeatedField(e,r),n=[],i=0;i<o.length;i++)n[i]=new t(o[i]);e.wrappers_[r]=n}},jspb.Message.setWrapperField=function(e,t,r){e.wrappers_||(e.wrappers_={});var o=r?r.toArray():r;e.wrappers_[t]=r,jspb.Message.setField(e,t,o)},jspb.Message.setOneofWrapperField=function(e,t,r,o){e.wrappers_||(e.wrappers_={});var n=o?o.toArray():o;e.wrappers_[t]=o,jspb.Message.setOneofField(e,t,r,n)},jspb.Message.setRepeatedWrapperField=function(e,t,r){e.wrappers_||(e.wrappers_={}),r=r||[];for(var o=[],n=0;n<r.length;n++)o[n]=r[n].toArray();e.wrappers_[t]=r,jspb.Message.setField(e,t,o)},jspb.Message.addToRepeatedWrapperField=function(e,t,r,o,n){jspb.Message.wrapRepeatedField_(e,o,t);var i=e.wrappers_[t];return i||(i=e.wrappers_[t]=[]),r=r||new o,e=jspb.Message.getRepeatedField(e,t),void 0!=n?(i.splice(n,0,r),e.splice(n,0,r.toArray())):(i.push(r),e.push(r.toArray())),r},jspb.Message.toMap=function(e,t,r,o){for(var n={},i=0;i<e.length;i++)n[t.call(e[i])]=r?r.call(e[i],o,e[i]):e[i];return n},jspb.Message.prototype.syncMapFields_=function(){if(this.wrappers_)for(var e in this.wrappers_){var t=this.wrappers_[e];if(goog.isArray(t))for(var r=0;r<t.length;r++)t[r]&&t[r].toArray();else t&&t.toArray()}},jspb.Message.prototype.toArray=function(){return this.syncMapFields_(),this.array},jspb.Message.GENERATE_TO_STRING&&(jspb.Message.prototype.toString=function(){return this.syncMapFields_(),this.array.toString()}),jspb.Message.prototype.getExtension=function(e){if(this.extensionObject_){this.wrappers_||(this.wrappers_={});var t=e.fieldIndex;if(e.isRepeated){if(e.isMessageType())return this.wrappers_[t]||(this.wrappers_[t]=goog.array.map(this.extensionObject_[t]||[],function(t){return new e.ctor(t)})),this.wrappers_[t]}else if(e.isMessageType())return!this.wrappers_[t]&&this.extensionObject_[t]&&(this.wrappers_[t]=new e.ctor(this.extensionObject_[t])),this.wrappers_[t];return this.extensionObject_[t]}},jspb.Message.prototype.setExtension=function(e,t){this.wrappers_||(this.wrappers_={}),jspb.Message.maybeInitEmptyExtensionObject_(this);var r=e.fieldIndex;return e.isRepeated?(t=t||[],e.isMessageType()?(this.wrappers_[r]=t,this.extensionObject_[r]=goog.array.map(t,function(e){return e.toArray()})):this.extensionObject_[r]=t):e.isMessageType()?(this.wrappers_[r]=t,this.extensionObject_[r]=t?t.toArray():t):this.extensionObject_[r]=t,this},jspb.Message.difference=function(e,t){if(!(e instanceof t.constructor))throw Error("Messages have different types.");var r=e.toArray(),o=t.toArray(),n=[],i=0,s=r.length>o.length?r.length:o.length;for(e.getJsPbMessageId()&&(n[0]=e.getJsPbMessageId(),i=1);i<s;i++)jspb.Message.compareFields(r[i],o[i])||(n[i]=o[i]);return new e.constructor(n)},jspb.Message.equals=function(e,t){return e==t||!(!e||!t)&&e instanceof t.constructor&&jspb.Message.compareFields(e.toArray(),t.toArray())},jspb.Message.compareExtensions=function(e,t){e=e||{},t=t||{};var r,o={};for(r in e)o[r]=0;for(r in t)o[r]=0;for(r in o)if(!jspb.Message.compareFields(e[r],t[r]))return!1;return!0},jspb.Message.compareFields=function(e,t){if(e==t)return!0;if(!goog.isObject(e)||!goog.isObject(t)||e.constructor!=t.constructor)return!1;if(jspb.Message.SUPPORTS_UINT8ARRAY_&&e.constructor===Uint8Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0}if(e.constructor===Array){for(var o=void 0,n=void 0,i=Math.max(e.length,t.length),r=0;r<i;r++){var s=e[r],a=t[r];if(s&&s.constructor==Object&&(goog.asserts.assert(void 0===o),goog.asserts.assert(r===e.length-1),o=s,s=void 0),a&&a.constructor==Object&&(goog.asserts.assert(void 0===n),goog.asserts.assert(r===t.length-1),n=a,a=void 0),!jspb.Message.compareFields(s,a))return!1}return!o&&!n||(o=o||{},n=n||{},jspb.Message.compareExtensions(o,n))}if(e.constructor===Object)return jspb.Message.compareExtensions(e,t);throw Error("Invalid type in JSPB array")},jspb.Message.prototype.cloneMessage=function(){return jspb.Message.cloneMessage(this)},jspb.Message.prototype.clone=function(){return jspb.Message.cloneMessage(this)},jspb.Message.clone=function(e){return jspb.Message.cloneMessage(e)},jspb.Message.cloneMessage=function(e){return new e.constructor(jspb.Message.clone_(e.toArray()))},jspb.Message.copyInto=function(e,t){goog.asserts.assertInstanceof(e,jspb.Message),goog.asserts.assertInstanceof(t,jspb.Message),goog.asserts.assert(e.constructor==t.constructor,"Copy source and target message should have the same type.");for(var r=jspb.Message.clone(e),o=t.toArray(),n=r.toArray(),i=o.length=0;i<n.length;i++)o[i]=n[i];t.wrappers_=r.wrappers_,t.extensionObject_=r.extensionObject_},jspb.Message.clone_=function(e){var t;if(goog.isArray(e)){for(var r=Array(e.length),o=0;o<e.length;o++)null!=(t=e[o])&&(r[o]="object"==("undefined"==typeof t?"undefined":(0,_typeof3["default"])(t))?jspb.Message.clone_(t):t);return r}if(jspb.Message.SUPPORTS_UINT8ARRAY_&&e instanceof Uint8Array)return new Uint8Array(e);r={};for(o in e)null!=(t=e[o])&&(r[o]="object"==("undefined"==typeof t?"undefined":(0,_typeof3["default"])(t))?jspb.Message.clone_(t):t);return r},jspb.Message.registerMessageType=function(e,t){jspb.Message.registry_[e]=t,t.messageId=e},jspb.Message.registry_={},jspb.Message.messageSetExtensions={},jspb.Message.messageSetExtensionsBinary={},jspb.arith={},jspb.arith.UInt64=function(e,t){this.lo=e,this.hi=t},jspb.arith.UInt64.prototype.cmp=function(e){return this.hi<e.hi||this.hi==e.hi&&this.lo<e.lo?-1:this.hi==e.hi&&this.lo==e.lo?0:1},jspb.arith.UInt64.prototype.rightShift=function(){return new jspb.arith.UInt64((this.lo>>>1|(1&this.hi)<<31)>>>0,this.hi>>>1>>>0)},jspb.arith.UInt64.prototype.leftShift=function(){return new jspb.arith.UInt64(this.lo<<1>>>0,(this.hi<<1|this.lo>>>31)>>>0)},jspb.arith.UInt64.prototype.msb=function(){return!!(2147483648&this.hi)},jspb.arith.UInt64.prototype.lsb=function(){return!!(1&this.lo)},jspb.arith.UInt64.prototype.zero=function(){return 0==this.lo&&0==this.hi},jspb.arith.UInt64.prototype.add=function(e){return new jspb.arith.UInt64((this.lo+e.lo&4294967295)>>>0>>>0,((this.hi+e.hi&4294967295)>>>0)+(4294967296<=this.lo+e.lo?1:0)>>>0)},jspb.arith.UInt64.prototype.sub=function(e){return new jspb.arith.UInt64((this.lo-e.lo&4294967295)>>>0>>>0,((this.hi-e.hi&4294967295)>>>0)-(0>this.lo-e.lo?1:0)>>>0)},jspb.arith.UInt64.mul32x32=function(e,t){for(var r=65535&e,o=e>>>16,n=65535&t,i=t>>>16,s=r*n+65536*(r*i&65535)+65536*(o*n&65535),r=o*i+(r*i>>>16)+(o*n>>>16);4294967296<=s;)s-=4294967296,r+=1;return new jspb.arith.UInt64(s>>>0,r>>>0)},jspb.arith.UInt64.prototype.mul=function(e){var t=jspb.arith.UInt64.mul32x32(this.lo,e);return e=jspb.arith.UInt64.mul32x32(this.hi,e),e.hi=e.lo,e.lo=0,t.add(e)},jspb.arith.UInt64.prototype.div=function(e){if(0==e)return[];var t=new jspb.arith.UInt64(0,0),r=new jspb.arith.UInt64(this.lo,this.hi);e=new jspb.arith.UInt64(e,0);for(var o=new jspb.arith.UInt64(1,0);!e.msb();)e=e.leftShift(),o=o.leftShift();for(;!o.zero();)0>=e.cmp(r)&&(t=t.add(o),r=r.sub(e)),e=e.rightShift(),o=o.rightShift();return[t,r]},jspb.arith.UInt64.prototype.toString=function(){for(var e="",t=this;!t.zero();)var t=t.div(10),r=t[0],e=t[1].lo+e,t=r;return""==e&&(e="0"),e},jspb.arith.UInt64.fromString=function(e){for(var t=new jspb.arith.UInt64(0,0),r=new jspb.arith.UInt64(0,0),o=0;o<e.length;o++){if("0">e[o]||"9"<e[o])return null;var n=parseInt(e[o],10);r.lo=n,t=t.mul(10).add(r)}return t},jspb.arith.UInt64.prototype.clone=function(){return new jspb.arith.UInt64(this.lo,this.hi)},jspb.arith.Int64=function(e,t){this.lo=e,this.hi=t},jspb.arith.Int64.prototype.add=function(e){return new jspb.arith.Int64((this.lo+e.lo&4294967295)>>>0>>>0,((this.hi+e.hi&4294967295)>>>0)+(4294967296<=this.lo+e.lo?1:0)>>>0)},jspb.arith.Int64.prototype.sub=function(e){return new jspb.arith.Int64((this.lo-e.lo&4294967295)>>>0>>>0,((this.hi-e.hi&4294967295)>>>0)-(0>this.lo-e.lo?1:0)>>>0)},jspb.arith.Int64.prototype.clone=function(){return new jspb.arith.Int64(this.lo,this.hi)},jspb.arith.Int64.prototype.toString=function(){var e=0!=(2147483648&this.hi),t=new jspb.arith.UInt64(this.lo,this.hi);return e&&(t=new jspb.arith.UInt64(0,0).sub(t)),(e?"-":"")+t.toString()},jspb.arith.Int64.fromString=function(e){var t=0<e.length&&"-"==e[0];return t&&(e=e.substring(1)),null===(e=jspb.arith.UInt64.fromString(e))?null:(t&&(e=new jspb.arith.UInt64(0,0).sub(e)),new jspb.arith.Int64(e.lo,e.hi))},jspb.BinaryConstants={},jspb.ConstBinaryMessage=function(){},jspb.BinaryMessage=function(){},jspb.BinaryConstants.FieldType={INVALID:-1,DOUBLE:1,FLOAT:2,INT64:3,UINT64:4,INT32:5,FIXED64:6,FIXED32:7,BOOL:8,STRING:9,GROUP:10,MESSAGE:11,BYTES:12,UINT32:13,ENUM:14,SFIXED32:15,SFIXED64:16,SINT32:17,SINT64:18,FHASH64:30,VHASH64:31},jspb.BinaryConstants.WireType={INVALID:-1,VARINT:0,FIXED64:1,DELIMITED:2,START_GROUP:3,END_GROUP:4,FIXED32:5},jspb.BinaryConstants.FieldTypeToWireType=function(e){var t=jspb.BinaryConstants.FieldType,r=jspb.BinaryConstants.WireType;switch(e){case t.INT32:case t.INT64:case t.UINT32:case t.UINT64:case t.SINT32:case t.SINT64:case t.BOOL:case t.ENUM:case t.VHASH64:return r.VARINT;case t.DOUBLE:case t.FIXED64:case t.SFIXED64:case t.FHASH64:return r.FIXED64;case t.STRING:case t.MESSAGE:case t.BYTES:return r.DELIMITED;case t.FLOAT:case t.FIXED32:case t.SFIXED32:return r.FIXED32;default:return r.INVALID}},jspb.BinaryConstants.INVALID_FIELD_NUMBER=-1,jspb.BinaryConstants.FLOAT32_EPS=1.401298464324817e-45,jspb.BinaryConstants.FLOAT32_MIN=1.1754943508222875e-38,jspb.BinaryConstants.FLOAT32_MAX=3.4028234663852886e38,jspb.BinaryConstants.FLOAT64_EPS=5e-324,jspb.BinaryConstants.FLOAT64_MIN=2.2250738585072014e-308,jspb.BinaryConstants.FLOAT64_MAX=1.7976931348623157e308,jspb.BinaryConstants.TWO_TO_20=1048576,jspb.BinaryConstants.TWO_TO_23=8388608,jspb.BinaryConstants.TWO_TO_31=2147483648,jspb.BinaryConstants.TWO_TO_32=4294967296,jspb.BinaryConstants.TWO_TO_52=4503599627370496,jspb.BinaryConstants.TWO_TO_63=0x8000000000000000,jspb.BinaryConstants.TWO_TO_64=0x10000000000000000,jspb.BinaryConstants.ZERO_HASH="\0\0\0\0\0\0\0\0",jspb.utils={},jspb.utils.split64Low=0,jspb.utils.split64High=0,jspb.utils.splitUint64=function(e){var t=e>>>0;e=Math.floor((e-t)/jspb.BinaryConstants.TWO_TO_32)>>>0,jspb.utils.split64Low=t,jspb.utils.split64High=e},jspb.utils.splitInt64=function(e){var t=0>e;e=Math.abs(e);var r=e>>>0;e=Math.floor((e-r)/jspb.BinaryConstants.TWO_TO_32),e>>>=0,t&&(e=~e>>>0,4294967295<(r=1+(~r>>>0))&&(r=0,4294967295<++e&&(e=0))),jspb.utils.split64Low=r,jspb.utils.split64High=e},jspb.utils.splitZigzag64=function(e){var t=0>e;e=2*Math.abs(e),jspb.utils.splitUint64(e),e=jspb.utils.split64Low;var r=jspb.utils.split64High;t&&(0==e?0==r?r=e=4294967295:(r--,e=4294967295):e--),jspb.utils.split64Low=e,jspb.utils.split64High=r},jspb.utils.splitFloat32=function(e){var t=0>e?1:0;e=t?-e:e;var r;0===e?0<1/e?(jspb.utils.split64High=0,jspb.utils.split64Low=0):(jspb.utils.split64High=0,jspb.utils.split64Low=2147483648):isNaN(e)?(jspb.utils.split64High=0,jspb.utils.split64Low=2147483647):e>jspb.BinaryConstants.FLOAT32_MAX?(jspb.utils.split64High=0,jspb.utils.split64Low=(t<<31|2139095040)>>>0):e<jspb.BinaryConstants.FLOAT32_MIN?(e=Math.round(e/Math.pow(2,-149)),jspb.utils.split64High=0,jspb.utils.split64Low=(t<<31|e)>>>0):(r=Math.floor(Math.log(e)/Math.LN2),e*=Math.pow(2,-r),e=8388607&Math.round(e*jspb.BinaryConstants.TWO_TO_23),jspb.utils.split64High=0,jspb.utils.split64Low=(t<<31|r+127<<23|e)>>>0)},jspb.utils.splitFloat64=function(e){var t=0>e?1:0;if(0===(e=t?-e:e))jspb.utils.split64High=0<1/e?0:2147483648,jspb.utils.split64Low=0;else if(isNaN(e))jspb.utils.split64High=2147483647,jspb.utils.split64Low=4294967295;else if(e>jspb.BinaryConstants.FLOAT64_MAX)jspb.utils.split64High=(t<<31|2146435072)>>>0,jspb.utils.split64Low=0;else if(e<jspb.BinaryConstants.FLOAT64_MIN){var r=e/Math.pow(2,-1074);e=r/jspb.BinaryConstants.TWO_TO_32,jspb.utils.split64High=(t<<31|e)>>>0,jspb.utils.split64Low=r>>>0}else{var o=Math.floor(Math.log(e)/Math.LN2);1024==o&&(o=1023),r=e*Math.pow(2,-o),e=r*jspb.BinaryConstants.TWO_TO_20&1048575,r=r*jspb.BinaryConstants.TWO_TO_52>>>0,jspb.utils.split64High=(t<<31|o+1023<<20|e)>>>0,jspb.utils.split64Low=r}},jspb.utils.splitHash64=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),o=e.charCodeAt(2),n=e.charCodeAt(3),i=e.charCodeAt(4),s=e.charCodeAt(5),a=e.charCodeAt(6);e=e.charCodeAt(7),jspb.utils.split64Low=t+(r<<8)+(o<<16)+(n<<24)>>>0,jspb.utils.split64High=i+(s<<8)+(a<<16)+(e<<24)>>>0},jspb.utils.joinUint64=function(e,t){return t*jspb.BinaryConstants.TWO_TO_32+e},jspb.utils.joinInt64=function(e,t){var r=2147483648&t;r&&(e=1+~e>>>0,t=~t>>>0,0==e&&(t=t+1>>>0));var o=jspb.utils.joinUint64(e,t);return r?-o:o},jspb.utils.joinZigzag64=function(e,t){var r=1&e;e=(e>>>1|t<<31)>>>0,t>>>=1,r&&0==(e=e+1>>>0)&&(t=t+1>>>0);var o=jspb.utils.joinUint64(e,t);return r?-o:o},jspb.utils.joinFloat32=function(e,t){var r=2*(e>>31)+1,o=e>>>23&255,n=8388607&e;return 255==o?n?NaN:1/0*r:0==o?r*Math.pow(2,-149)*n:r*Math.pow(2,o-150)*(n+Math.pow(2,23))},jspb.utils.joinFloat64=function(e,t){var r=2*(t>>31)+1,o=t>>>20&2047,n=jspb.BinaryConstants.TWO_TO_32*(1048575&t)+e;return 2047==o?n?NaN:1/0*r:0==o?r*Math.pow(2,-1074)*n:r*Math.pow(2,o-1075)*(n+jspb.BinaryConstants.TWO_TO_52)},jspb.utils.joinHash64=function(e,t){return String.fromCharCode(e>>>0&255,e>>>8&255,e>>>16&255,e>>>24&255,t>>>0&255,t>>>8&255,t>>>16&255,t>>>24&255)},jspb.utils.DIGITS="0123456789abcdef".split(""),jspb.utils.joinUnsignedDecimalString=function(e,t){function r(e){for(var t=1e7,r=0;7>r;r++){var t=t/10,o=e/t%10>>>0;(0!=o||a)&&(a=!0,u+=s[o])}}if(2097151>=t)return""+(jspb.BinaryConstants.TWO_TO_32*t+e);var o=(e>>>24|t<<8)>>>0&16777215,n=t>>16&65535,i=(16777215&e)+6777216*o+6710656*n,o=o+8147497*n,n=2*n;1e7<=i&&(o+=Math.floor(i/1e7),i%=1e7),1e7<=o&&(n+=Math.floor(o/1e7),o%=1e7);var s=jspb.utils.DIGITS,a=!1,u="";return(n||a)&&r(n),(o||a)&&r(o),(i||a)&&r(i),u},jspb.utils.joinSignedDecimalString=function(e,t){var r=2147483648&t;r&&(e=1+~e>>>0,t=~t+(0==e?1:0)>>>0);var o=jspb.utils.joinUnsignedDecimalString(e,t);return r?"-"+o:o},jspb.utils.hash64ToDecimalString=function(e,t){jspb.utils.splitHash64(e);var r=jspb.utils.split64Low,o=jspb.utils.split64High;return t?jspb.utils.joinSignedDecimalString(r,o):jspb.utils.joinUnsignedDecimalString(r,o)},jspb.utils.hash64ArrayToDecimalStrings=function(e,t){for(var r=Array(e.length),o=0;o<e.length;o++)r[o]=jspb.utils.hash64ToDecimalString(e[o],t);return r},jspb.utils.decimalStringToHash64=function(e){function t(e,t){for(var r=0;8>r&&(1!==e||0<t);r++){var n=e*o[r]+t;o[r]=255&n,t=n>>>8}}goog.asserts.assert(0<e.length);var r=!1;"-"===e[0]&&(r=!0,e=e.slice(1));for(var o=[0,0,0,0,0,0,0,0],n=0;n<e.length;n++)t(10,jspb.utils.DIGITS.indexOf(e[n]));return r&&(function(){for(var e=0;8>e;e++)o[e]=255&~o[e]}(),t(1,1)),goog.crypt.byteArrayToString(o)},jspb.utils.splitDecimalString=function(e){jspb.utils.splitHash64(jspb.utils.decimalStringToHash64(e))},jspb.utils.hash64ToHexString=function(e){var t=Array(18);t[0]="0",t[1]="x";for(var r=0;8>r;r++){var o=e.charCodeAt(7-r);t[2*r+2]=jspb.utils.DIGITS[o>>4],t[2*r+3]=jspb.utils.DIGITS[15&o]}return t.join("")},jspb.utils.hexStringToHash64=function(e){e=e.toLowerCase(),goog.asserts.assert(18==e.length),goog.asserts.assert("0"==e[0]),goog.asserts.assert("x"==e[1]);for(var t="",r=0;8>r;r++)var o=jspb.utils.DIGITS.indexOf(e[2*r+2]),n=jspb.utils.DIGITS.indexOf(e[2*r+3]),t=String.fromCharCode(16*o+n)+t;return t},jspb.utils.hash64ToNumber=function(e,t){jspb.utils.splitHash64(e);var r=jspb.utils.split64Low,o=jspb.utils.split64High;return t?jspb.utils.joinInt64(r,o):jspb.utils.joinUint64(r,o)},jspb.utils.numberToHash64=function(e){return jspb.utils.splitInt64(e),jspb.utils.joinHash64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.utils.countVarints=function(e,t,r){for(var o=0,n=t;n<r;n++)o+=e[n]>>7;return r-t-o},jspb.utils.countVarintFields=function(e,t,r,o){var n=0;if(128>(o=8*o+jspb.BinaryConstants.WireType.VARINT))for(;t<r&&e[t++]==o;)for(n++;;){var i=e[t++];if(0==(128&i))break}else for(;t<r;){for(i=o;128<i;){if(e[t]!=(127&i|128))return n;t++,i>>=7}if(e[t++]!=i)break;for(n++;0!=(128&(i=e[t++])););}return n},jspb.utils.countFixedFields_=function(e,t,r,o,n){var i=0;if(128>o)for(;t<r&&e[t++]==o;)i++,t+=n;else for(;t<r;){for(var s=o;128<s;){if(e[t++]!=(127&s|128))return i;s>>=7}if(e[t++]!=s)break;i++,t+=n}return i},jspb.utils.countFixed32Fields=function(e,t,r,o){return jspb.utils.countFixedFields_(e,t,r,8*o+jspb.BinaryConstants.WireType.FIXED32,4)},jspb.utils.countFixed64Fields=function(e,t,r,o){return jspb.utils.countFixedFields_(e,t,r,8*o+jspb.BinaryConstants.WireType.FIXED64,8)},jspb.utils.countDelimitedFields=function(e,t,r,o){var n=0;for(o=8*o+jspb.BinaryConstants.WireType.DELIMITED;t<r;){for(var i=o;128<i;){if(e[t++]!=(127&i|128))return n;i>>=7}if(e[t++]!=i)break;n++;for(var s=0,a=1;i=e[t++],s+=(127&i)*a,a*=128,0!=(128&i););t+=s}return n},jspb.utils.debugBytesToTextFormat=function(e){var t='"';if(e){e=jspb.utils.byteSourceToUint8Array(e);for(var r=0;r<e.length;r++)t+="\\x",16>e[r]&&(t+="0"),t+=e[r].toString(16)}return t+'"'},jspb.utils.debugScalarToTextFormat=function(e){return goog.isString(e)?goog.string.quote(e):e.toString()},jspb.utils.stringToByteArray=function(e){for(var t=new Uint8Array(e.length),r=0;r<e.length;r++){var o=e.charCodeAt(r);if(255<o)throw Error("Conversion error: string contains codepoint outside of byte range");t[r]=o}return t},jspb.utils.byteSourceToUint8Array=function(e){return e.constructor===Uint8Array?e:e.constructor===ArrayBuffer||e.constructor===Array?new Uint8Array(e):e.constructor===String?goog.crypt.base64.decodeStringToUint8Array(e):(goog.asserts.fail("Type not convertible to Uint8Array."),new Uint8Array(0))},jspb.BinaryEncoder=function(){this.buffer_=[]},jspb.BinaryEncoder.prototype.length=function(){return this.buffer_.length},jspb.BinaryEncoder.prototype.end=function(){var e=this.buffer_;return this.buffer_=[],e},jspb.BinaryEncoder.prototype.writeSplitVarint64=function(e,t){for(goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(t==Math.floor(t)),goog.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32),goog.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32);0<t||127<e;)this.buffer_.push(127&e|128),e=(e>>>7|t<<25)>>>0,t>>>=7;this.buffer_.push(e)},jspb.BinaryEncoder.prototype.writeSplitFixed64=function(e,t){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(t==Math.floor(t)),goog.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32),goog.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeUint32(e),this.writeUint32(t)},jspb.BinaryEncoder.prototype.writeUnsignedVarint32=function(e){for(goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32);127<e;)this.buffer_.push(127&e|128),e>>>=7;this.buffer_.push(e)},jspb.BinaryEncoder.prototype.writeSignedVarint32=function(e){if(goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),0<=e)this.writeUnsignedVarint32(e);else{for(var t=0;9>t;t++)this.buffer_.push(127&e|128),e>>=7;this.buffer_.push(1)}},jspb.BinaryEncoder.prototype.writeUnsignedVarint64=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_64),jspb.utils.splitInt64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeSignedVarint64=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_63&&e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitInt64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeZigzagVarint32=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),this.writeUnsignedVarint32((e<<1^e>>31)>>>0)},jspb.BinaryEncoder.prototype.writeZigzagVarint64=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_63&&e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitZigzag64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeZigzagVarint64String=function(e){this.writeZigzagVarint64(parseInt(e,10))},jspb.BinaryEncoder.prototype.writeUint8=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(0<=e&&256>e),this.buffer_.push(e>>>0&255)},jspb.BinaryEncoder.prototype.writeUint16=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(0<=e&&65536>e),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255)},jspb.BinaryEncoder.prototype.writeUint32=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_32),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255),this.buffer_.push(e>>>16&255),this.buffer_.push(e>>>24&255)},jspb.BinaryEncoder.prototype.writeUint64=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(0<=e&&e<jspb.BinaryConstants.TWO_TO_64),jspb.utils.splitUint64(e),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeInt8=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(-128<=e&&128>e),this.buffer_.push(e>>>0&255)},jspb.BinaryEncoder.prototype.writeInt16=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(-32768<=e&&32768>e),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255)},jspb.BinaryEncoder.prototype.writeInt32=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),this.buffer_.push(e>>>0&255),this.buffer_.push(e>>>8&255),this.buffer_.push(e>>>16&255),this.buffer_.push(e>>>24&255)},jspb.BinaryEncoder.prototype.writeInt64=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_63&&e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitInt64(e),this.writeSplitFixed64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeInt64String=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(+e>=-jspb.BinaryConstants.TWO_TO_63&&+e<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitHash64(jspb.utils.decimalStringToHash64(e)),this.writeSplitFixed64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeFloat=function(e){goog.asserts.assert(e>=-jspb.BinaryConstants.FLOAT32_MAX&&e<=jspb.BinaryConstants.FLOAT32_MAX),jspb.utils.splitFloat32(e),this.writeUint32(jspb.utils.split64Low)},jspb.BinaryEncoder.prototype.writeDouble=function(e){goog.asserts.assert(e>=-jspb.BinaryConstants.FLOAT64_MAX&&e<=jspb.BinaryConstants.FLOAT64_MAX),jspb.utils.splitFloat64(e),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeBool=function(e){goog.asserts.assert(goog.isBoolean(e)||goog.isNumber(e)),this.buffer_.push(e?1:0)},jspb.BinaryEncoder.prototype.writeEnum=function(e){goog.asserts.assert(e==Math.floor(e)),goog.asserts.assert(e>=-jspb.BinaryConstants.TWO_TO_31&&e<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32(e)},jspb.BinaryEncoder.prototype.writeBytes=function(e){this.buffer_.push.apply(this.buffer_,e)},jspb.BinaryEncoder.prototype.writeVarintHash64=function(e){jspb.utils.splitHash64(e),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeFixedHash64=function(e){jspb.utils.splitHash64(e),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeString=function(e){for(var t=this.buffer_.length,r=0;r<e.length;r++){var o=e.charCodeAt(r);if(128>o)this.buffer_.push(o);else if(2048>o)this.buffer_.push(o>>6|192),this.buffer_.push(63&o|128);else if(65536>o)if(55296<=o&&56319>=o&&r+1<e.length){var n=e.charCodeAt(r+1);56320<=n&&57343>=n&&(o=1024*(o-55296)+n-56320+65536,this.buffer_.push(o>>18|240),this.buffer_.push(o>>12&63|128),this.buffer_.push(o>>6&63|128),this.buffer_.push(63&o|128),r++)}else this.buffer_.push(o>>12|224),this.buffer_.push(o>>6&63|128),this.buffer_.push(63&o|128)}return this.buffer_.length-t},jspb.BinaryWriter=function(){this.blocks_=[],this.totalLength_=0,this.encoder_=new jspb.BinaryEncoder,this.bookmarks_=[]},jspb.BinaryWriter.prototype.appendUint8Array_=function(e){var t=this.encoder_.end();this.blocks_.push(t),this.blocks_.push(e),this.totalLength_+=t.length+e.length},jspb.BinaryWriter.prototype.beginDelimited_=function(e){return this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),e=this.encoder_.end(),this.blocks_.push(e),this.totalLength_+=e.length,e.push(this.totalLength_),e},jspb.BinaryWriter.prototype.endDelimited_=function(e){var t=e.pop(),t=this.totalLength_+this.encoder_.length()-t;for(goog.asserts.assert(0<=t);127<t;)e.push(127&t|128),t>>>=7,this.totalLength_++;e.push(t),this.totalLength_++},jspb.BinaryWriter.prototype.writeSerializedMessage=function(e,t,r){this.appendUint8Array_(e.subarray(t,r))},jspb.BinaryWriter.prototype.maybeWriteSerializedMessage=function(e,t,r){null!=e&&null!=t&&null!=r&&this.writeSerializedMessage(e,t,r)},jspb.BinaryWriter.prototype.reset=function(){this.blocks_=[],this.encoder_.end(),this.totalLength_=0,this.bookmarks_=[]},jspb.BinaryWriter.prototype.getResultBuffer=function(){goog.asserts.assert(0==this.bookmarks_.length);for(var e=new Uint8Array(this.totalLength_+this.encoder_.length()),t=this.blocks_,r=t.length,o=0,n=0;n<r;n++){var i=t[n];e.set(i,o),o+=i.length}return t=this.encoder_.end(),e.set(t,o),o+=t.length,goog.asserts.assert(o==e.length),this.blocks_=[e],e},jspb.BinaryWriter.prototype.getResultBase64String=function(){return goog.crypt.base64.encodeByteArray(this.getResultBuffer())},jspb.BinaryWriter.prototype.beginSubMessage=function(e){this.bookmarks_.push(this.beginDelimited_(e))},jspb.BinaryWriter.prototype.endSubMessage=function(){goog.asserts.assert(0<=this.bookmarks_.length),this.endDelimited_(this.bookmarks_.pop())},jspb.BinaryWriter.prototype.writeFieldHeader_=function(e,t){goog.asserts.assert(1<=e&&e==Math.floor(e)),this.encoder_.writeUnsignedVarint32(8*e+t)},jspb.BinaryWriter.prototype.writeAny=function(e,t,r){var o=jspb.BinaryConstants.FieldType;switch(e){case o.DOUBLE:this.writeDouble(t,r);break;case o.FLOAT:this.writeFloat(t,r);break;case o.INT64:this.writeInt64(t,r);break;case o.UINT64:this.writeUint64(t,r);break;case o.INT32:this.writeInt32(t,r);break;case o.FIXED64:this.writeFixed64(t,r);break;case o.FIXED32:this.writeFixed32(t,r);break;case o.BOOL:this.writeBool(t,r);break;case o.STRING:this.writeString(t,r);break;case o.GROUP:goog.asserts.fail("Group field type not supported in writeAny()");break;case o.MESSAGE:goog.asserts.fail("Message field type not supported in writeAny()");break;case o.BYTES:this.writeBytes(t,r);break;case o.UINT32:this.writeUint32(t,r);break;case o.ENUM:this.writeEnum(t,r);break;case o.SFIXED32:this.writeSfixed32(t,r);break;case o.SFIXED64:
this.writeSfixed64(t,r);break;case o.SINT32:this.writeSint32(t,r);break;case o.SINT64:this.writeSint64(t,r);break;case o.FHASH64:this.writeFixedHash64(t,r);break;case o.VHASH64:this.writeVarintHash64(t,r);break;default:goog.asserts.fail("Invalid field type in writeAny()")}},jspb.BinaryWriter.prototype.writeUnsignedVarint32_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeUnsignedVarint32(t))},jspb.BinaryWriter.prototype.writeSignedVarint32_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(t))},jspb.BinaryWriter.prototype.writeUnsignedVarint64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeUnsignedVarint64(t))},jspb.BinaryWriter.prototype.writeSignedVarint64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint64(t))},jspb.BinaryWriter.prototype.writeZigzagVarint32_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint32(t))},jspb.BinaryWriter.prototype.writeZigzagVarint64_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint64(t))},jspb.BinaryWriter.prototype.writeZigzagVarint64String_=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint64String(t))},jspb.BinaryWriter.prototype.writeInt32=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32_(e,t))},jspb.BinaryWriter.prototype.writeInt32String=function(e,t){if(null!=t){var r=parseInt(t,10);goog.asserts.assert(r>=-jspb.BinaryConstants.TWO_TO_31&&r<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32_(e,r)}},jspb.BinaryWriter.prototype.writeInt64=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_63&&t<jspb.BinaryConstants.TWO_TO_63),this.writeSignedVarint64_(e,t))},jspb.BinaryWriter.prototype.writeInt64String=function(e,t){if(null!=t){var r=jspb.arith.Int64.fromString(t);this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(r.lo,r.hi)}},jspb.BinaryWriter.prototype.writeUint32=function(e,t){null!=t&&(goog.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeUnsignedVarint32_(e,t))},jspb.BinaryWriter.prototype.writeUint32String=function(e,t){if(null!=t){var r=parseInt(t,10);goog.asserts.assert(0<=r&&r<jspb.BinaryConstants.TWO_TO_32),this.writeUnsignedVarint32_(e,r)}},jspb.BinaryWriter.prototype.writeUint64=function(e,t){null!=t&&(goog.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_64),this.writeUnsignedVarint64_(e,t))},jspb.BinaryWriter.prototype.writeUint64String=function(e,t){if(null!=t){var r=jspb.arith.UInt64.fromString(t);this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(r.lo,r.hi)}},jspb.BinaryWriter.prototype.writeSint32=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeZigzagVarint32_(e,t))},jspb.BinaryWriter.prototype.writeSint64=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_63&&t<jspb.BinaryConstants.TWO_TO_63),this.writeZigzagVarint64_(e,t))},jspb.BinaryWriter.prototype.writeSint64String=function(e,t){null!=t&&(goog.asserts.assert(+t>=-jspb.BinaryConstants.TWO_TO_63&&+t<jspb.BinaryConstants.TWO_TO_63),this.writeZigzagVarint64String_(e,t))},jspb.BinaryWriter.prototype.writeFixed32=function(e,t){null!=t&&(goog.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_32),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeUint32(t))},jspb.BinaryWriter.prototype.writeFixed64=function(e,t){null!=t&&(goog.asserts.assert(0<=t&&t<jspb.BinaryConstants.TWO_TO_64),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeUint64(t))},jspb.BinaryWriter.prototype.writeFixed64String=function(e,t){if(null!=t){var r=jspb.arith.UInt64.fromString(t);this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(r.lo,r.hi)}},jspb.BinaryWriter.prototype.writeSfixed32=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeInt32(t))},jspb.BinaryWriter.prototype.writeSfixed64=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_63&&t<jspb.BinaryConstants.TWO_TO_63),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeInt64(t))},jspb.BinaryWriter.prototype.writeSfixed64String=function(e,t){if(null!=t){var r=jspb.arith.Int64.fromString(t);this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(r.lo,r.hi)}},jspb.BinaryWriter.prototype.writeFloat=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeFloat(t))},jspb.BinaryWriter.prototype.writeDouble=function(e,t){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeDouble(t))},jspb.BinaryWriter.prototype.writeBool=function(e,t){null!=t&&(goog.asserts.assert(goog.isBoolean(t)||goog.isNumber(t)),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeBool(t))},jspb.BinaryWriter.prototype.writeEnum=function(e,t){null!=t&&(goog.asserts.assert(t>=-jspb.BinaryConstants.TWO_TO_31&&t<jspb.BinaryConstants.TWO_TO_31),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(t))},jspb.BinaryWriter.prototype.writeString=function(e,t){if(null!=t){var r=this.beginDelimited_(e);this.encoder_.writeString(t),this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writeBytes=function(e,t){if(null!=t){var r=jspb.utils.byteSourceToUint8Array(t);this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(r.length),this.appendUint8Array_(r)}},jspb.BinaryWriter.prototype.writeMessage=function(e,t,r){null!=t&&(e=this.beginDelimited_(e),r(t,this),this.endDelimited_(e))},jspb.BinaryWriter.prototype.writeGroup=function(e,t,r){null!=t&&(this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.START_GROUP),r(t,this),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.END_GROUP))},jspb.BinaryWriter.prototype.writeFixedHash64=function(e,t){null!=t&&(goog.asserts.assert(8==t.length),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeFixedHash64(t))},jspb.BinaryWriter.prototype.writeVarintHash64=function(e,t){null!=t&&(goog.asserts.assert(8==t.length),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeVarintHash64(t))},jspb.BinaryWriter.prototype.writeRepeatedInt32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSignedVarint32_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedInt32String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeInt32String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedInt64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSignedVarint64_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedInt64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeInt64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedUint32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUnsignedVarint32_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedUint32String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUint32String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedUint64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUnsignedVarint64_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedUint64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeUint64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSint32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarint32_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSint64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarint64_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSint64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeZigzagVarint64String_(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedFixed32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixed32(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedFixed64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixed64(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedFixed64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixed64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSfixed32=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSfixed32(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSfixed64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSfixed64(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedSfixed64String=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeSfixed64String(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedFloat=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFloat(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedDouble=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeDouble(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedBool=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeBool(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedEnum=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeEnum(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedString=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeString(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedBytes=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeBytes(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedMessage=function(e,t,r){if(null!=t)for(var o=0;o<t.length;o++){var n=this.beginDelimited_(e);r(t[o],this),this.endDelimited_(n)}},jspb.BinaryWriter.prototype.writeRepeatedGroup=function(e,t,r){if(null!=t)for(var o=0;o<t.length;o++)this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.START_GROUP),r(t[o],this),this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.END_GROUP)},jspb.BinaryWriter.prototype.writeRepeatedFixedHash64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeFixedHash64(e,t[r])},jspb.BinaryWriter.prototype.writeRepeatedVarintHash64=function(e,t){if(null!=t)for(var r=0;r<t.length;r++)this.writeVarintHash64(e,t[r])},jspb.BinaryWriter.prototype.writePackedInt32=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeSignedVarint32(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedInt32String=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeSignedVarint32(parseInt(t[o],10));this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedInt64=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeSignedVarint64(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedInt64String=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++){var n=jspb.arith.Int64.fromString(t[o]);this.encoder_.writeSplitVarint64(n.lo,n.hi)}this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedUint32=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeUnsignedVarint32(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedUint32String=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeUnsignedVarint32(parseInt(t[o],10));this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedUint64=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeUnsignedVarint64(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedUint64String=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++){var n=jspb.arith.UInt64.fromString(t[o]);this.encoder_.writeSplitVarint64(n.lo,n.hi)}this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedSint32=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeZigzagVarint32(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedSint64=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeZigzagVarint64(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedSint64String=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeZigzagVarint64(parseInt(t[o],10));this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedFixed32=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeUint32(t[r])}},jspb.BinaryWriter.prototype.writePackedFixed64=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeUint64(t[r])}},jspb.BinaryWriter.prototype.writePackedFixed64String=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length);for(var r=0;r<t.length;r++){var o=jspb.arith.UInt64.fromString(t[r]);this.encoder_.writeSplitFixed64(o.lo,o.hi)}}},jspb.BinaryWriter.prototype.writePackedSfixed32=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeInt32(t[r])}},jspb.BinaryWriter.prototype.writePackedSfixed64=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeInt64(t[r])}},jspb.BinaryWriter.prototype.writePackedSfixed64String=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeInt64String(t[r])}},jspb.BinaryWriter.prototype.writePackedFloat=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeFloat(t[r])}},jspb.BinaryWriter.prototype.writePackedDouble=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeDouble(t[r])}},jspb.BinaryWriter.prototype.writePackedBool=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(t.length);for(var r=0;r<t.length;r++)this.encoder_.writeBool(t[r])}},jspb.BinaryWriter.prototype.writePackedEnum=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeEnum(t[o]);this.endDelimited_(r)}},jspb.BinaryWriter.prototype.writePackedFixedHash64=function(e,t){if(null!=t&&t.length){this.writeFieldHeader_(e,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*t.length);for(var r=0;r<t.length;r++)this.encoder_.writeFixedHash64(t[r])}},jspb.BinaryWriter.prototype.writePackedVarintHash64=function(e,t){if(null!=t&&t.length){for(var r=this.beginDelimited_(e),o=0;o<t.length;o++)this.encoder_.writeVarintHash64(t[o]);this.endDelimited_(r)}},jspb.BinaryIterator=function(e,t,r){this.elements_=this.nextMethod_=this.decoder_=null,this.cursor_=0,this.nextValue_=null,this.atEnd_=!0,this.init_(e,t,r)},jspb.BinaryIterator.prototype.init_=function(e,t,r){e&&t&&(this.decoder_=e,this.nextMethod_=t),this.elements_=r||null,this.cursor_=0,this.nextValue_=null,this.atEnd_=!this.decoder_&&!this.elements_,this.next()},jspb.BinaryIterator.instanceCache_=[],jspb.BinaryIterator.alloc=function(e,t,r){if(jspb.BinaryIterator.instanceCache_.length){var o=jspb.BinaryIterator.instanceCache_.pop();return o.init_(e,t,r),o}return new jspb.BinaryIterator(e,t,r)},jspb.BinaryIterator.prototype.free=function(){this.clear(),100>jspb.BinaryIterator.instanceCache_.length&&jspb.BinaryIterator.instanceCache_.push(this)},jspb.BinaryIterator.prototype.clear=function(){this.decoder_&&this.decoder_.free(),this.elements_=this.nextMethod_=this.decoder_=null,this.cursor_=0,this.nextValue_=null,this.atEnd_=!0},jspb.BinaryIterator.prototype.get=function(){return this.nextValue_},jspb.BinaryIterator.prototype.atEnd=function(){return this.atEnd_},jspb.BinaryIterator.prototype.next=function(){var e=this.nextValue_;return this.decoder_?this.decoder_.atEnd()?(this.nextValue_=null,this.atEnd_=!0):this.nextValue_=this.nextMethod_.call(this.decoder_):this.elements_&&(this.cursor_==this.elements_.length?(this.nextValue_=null,this.atEnd_=!0):this.nextValue_=this.elements_[this.cursor_++]),e},jspb.BinaryDecoder=function(e,t,r){this.bytes_=null,this.tempHigh_=this.tempLow_=this.cursor_=this.end_=this.start_=0,this.error_=!1,e&&this.setBlock(e,t,r)},jspb.BinaryDecoder.instanceCache_=[],jspb.BinaryDecoder.alloc=function(e,t,r){if(jspb.BinaryDecoder.instanceCache_.length){var o=jspb.BinaryDecoder.instanceCache_.pop();return e&&o.setBlock(e,t,r),o}return new jspb.BinaryDecoder(e,t,r)},jspb.BinaryDecoder.prototype.free=function(){this.clear(),100>jspb.BinaryDecoder.instanceCache_.length&&jspb.BinaryDecoder.instanceCache_.push(this)},jspb.BinaryDecoder.prototype.clone=function(){return jspb.BinaryDecoder.alloc(this.bytes_,this.start_,this.end_-this.start_)},jspb.BinaryDecoder.prototype.clear=function(){this.bytes_=null,this.cursor_=this.end_=this.start_=0,this.error_=!1},jspb.BinaryDecoder.prototype.getBuffer=function(){return this.bytes_},jspb.BinaryDecoder.prototype.setBlock=function(e,t,r){this.bytes_=jspb.utils.byteSourceToUint8Array(e),this.start_=goog.isDef(t)?t:0,this.end_=goog.isDef(r)?this.start_+r:this.bytes_.length,this.cursor_=this.start_},jspb.BinaryDecoder.prototype.getEnd=function(){return this.end_},jspb.BinaryDecoder.prototype.setEnd=function(e){this.end_=e},jspb.BinaryDecoder.prototype.reset=function(){this.cursor_=this.start_},jspb.BinaryDecoder.prototype.getCursor=function(){return this.cursor_},jspb.BinaryDecoder.prototype.setCursor=function(e){this.cursor_=e},jspb.BinaryDecoder.prototype.advance=function(e){this.cursor_+=e,goog.asserts.assert(this.cursor_<=this.end_)},jspb.BinaryDecoder.prototype.atEnd=function(){return this.cursor_==this.end_},jspb.BinaryDecoder.prototype.pastEnd=function(){return this.cursor_>this.end_},jspb.BinaryDecoder.prototype.getError=function(){return this.error_||0>this.cursor_||this.cursor_>this.end_},jspb.BinaryDecoder.prototype.readSplitVarint64_=function(){for(var e,t,r=0,o=0;4>o;o++)if(e=this.bytes_[this.cursor_++],r|=(127&e)<<7*o,128>e)return this.tempLow_=r>>>0,void(this.tempHigh_=0);if(e=this.bytes_[this.cursor_++],r|=(127&e)<<28,t=0|(127&e)>>4,128>e)this.tempLow_=r>>>0,this.tempHigh_=t>>>0;else{for(o=0;5>o;o++)if(e=this.bytes_[this.cursor_++],t|=(127&e)<<7*o+3,128>e)return this.tempLow_=r>>>0,void(this.tempHigh_=t>>>0);goog.asserts.fail("Failed to read varint, encoding is invalid."),this.error_=!0}},jspb.BinaryDecoder.prototype.skipVarint=function(){for(;128&this.bytes_[this.cursor_];)this.cursor_++;this.cursor_++},jspb.BinaryDecoder.prototype.unskipVarint=function(e){for(;128<e;)this.cursor_--,e>>>=7;this.cursor_--},jspb.BinaryDecoder.prototype.readUnsignedVarint32=function(){var e,t=this.bytes_;e=t[this.cursor_+0];var r=127&e;return 128>e?(this.cursor_+=1,goog.asserts.assert(this.cursor_<=this.end_),r):(e=t[this.cursor_+1],r|=(127&e)<<7,128>e?(this.cursor_+=2,goog.asserts.assert(this.cursor_<=this.end_),r):(e=t[this.cursor_+2],r|=(127&e)<<14,128>e?(this.cursor_+=3,goog.asserts.assert(this.cursor_<=this.end_),r):(e=t[this.cursor_+3],r|=(127&e)<<21,128>e?(this.cursor_+=4,goog.asserts.assert(this.cursor_<=this.end_),r):(e=t[this.cursor_+4],r|=(15&e)<<28,128>e?(goog.asserts.assert(0==(240&e)),this.cursor_+=5,goog.asserts.assert(this.cursor_<=this.end_),r>>>0):(goog.asserts.assert(240==(240&e)),goog.asserts.assert(255==t[this.cursor_+5]),goog.asserts.assert(255==t[this.cursor_+6]),goog.asserts.assert(255==t[this.cursor_+7]),goog.asserts.assert(255==t[this.cursor_+8]),goog.asserts.assert(1==t[this.cursor_+9]),this.cursor_+=10,goog.asserts.assert(this.cursor_<=this.end_),r)))))},jspb.BinaryDecoder.prototype.readSignedVarint32=jspb.BinaryDecoder.prototype.readUnsignedVarint32,jspb.BinaryDecoder.prototype.readUnsignedVarint32String=function(){return this.readUnsignedVarint32().toString()},jspb.BinaryDecoder.prototype.readSignedVarint32String=function(){return this.readSignedVarint32().toString()},jspb.BinaryDecoder.prototype.readZigzagVarint32=function(){var e=this.readUnsignedVarint32();return e>>>1^-(1&e)},jspb.BinaryDecoder.prototype.readUnsignedVarint64=function(){return this.readSplitVarint64_(),jspb.utils.joinUint64(this.tempLow_,this.tempHigh_)},jspb.BinaryDecoder.prototype.readUnsignedVarint64String=function(){return this.readSplitVarint64_(),jspb.utils.joinUnsignedDecimalString(this.tempLow_,this.tempHigh_)},jspb.BinaryDecoder.prototype.readSignedVarint64=function(){return this.readSplitVarint64_(),jspb.utils.joinInt64(this.tempLow_,this.tempHigh_)},jspb.BinaryDecoder.prototype.readSignedVarint64String=function(){return this.readSplitVarint64_(),jspb.utils.joinSignedDecimalString(this.tempLow_,this.tempHigh_)},jspb.BinaryDecoder.prototype.readZigzagVarint64=function(){return this.readSplitVarint64_(),jspb.utils.joinZigzag64(this.tempLow_,this.tempHigh_)},jspb.BinaryDecoder.prototype.readZigzagVarint64String=function(){return this.readZigzagVarint64().toString()},jspb.BinaryDecoder.prototype.readUint8=function(){var e=this.bytes_[this.cursor_+0];return this.cursor_+=1,goog.asserts.assert(this.cursor_<=this.end_),e},jspb.BinaryDecoder.prototype.readUint16=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1];return this.cursor_+=2,goog.asserts.assert(this.cursor_<=this.end_),e<<0|t<<8},jspb.BinaryDecoder.prototype.readUint32=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1],r=this.bytes_[this.cursor_+2],o=this.bytes_[this.cursor_+3];return this.cursor_+=4,goog.asserts.assert(this.cursor_<=this.end_),(e<<0|t<<8|r<<16|o<<24)>>>0},jspb.BinaryDecoder.prototype.readUint64=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinUint64(e,t)},jspb.BinaryDecoder.prototype.readUint64String=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinUnsignedDecimalString(e,t)},jspb.BinaryDecoder.prototype.readInt8=function(){var e=this.bytes_[this.cursor_+0];return this.cursor_+=1,goog.asserts.assert(this.cursor_<=this.end_),e<<24>>24},jspb.BinaryDecoder.prototype.readInt16=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1];return this.cursor_+=2,goog.asserts.assert(this.cursor_<=this.end_),(e<<0|t<<8)<<16>>16},jspb.BinaryDecoder.prototype.readInt32=function(){var e=this.bytes_[this.cursor_+0],t=this.bytes_[this.cursor_+1],r=this.bytes_[this.cursor_+2],o=this.bytes_[this.cursor_+3];return this.cursor_+=4,goog.asserts.assert(this.cursor_<=this.end_),e<<0|t<<8|r<<16|o<<24},jspb.BinaryDecoder.prototype.readInt64=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinInt64(e,t)},jspb.BinaryDecoder.prototype.readInt64String=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinSignedDecimalString(e,t)},jspb.BinaryDecoder.prototype.readFloat=function(){var e=this.readUint32();return jspb.utils.joinFloat32(e,0)},jspb.BinaryDecoder.prototype.readDouble=function(){var e=this.readUint32(),t=this.readUint32();return jspb.utils.joinFloat64(e,t)},jspb.BinaryDecoder.prototype.readBool=function(){return!!this.bytes_[this.cursor_++]},jspb.BinaryDecoder.prototype.readEnum=function(){return this.readSignedVarint32()},jspb.BinaryDecoder.prototype.readString=function(e){var t=this.bytes_,r=this.cursor_;e=r+e;for(var o=[],n="";r<e;){var i=t[r++];if(128>i)o.push(i);else{if(192>i)continue;if(224>i){var s=t[r++];o.push((31&i)<<6|63&s)}else if(240>i){var s=t[r++],a=t[r++];o.push((15&i)<<12|(63&s)<<6|63&a)}else if(248>i){var s=t[r++],a=t[r++],u=t[r++],i=(7&i)<<18|(63&s)<<12|(63&a)<<6|63&u,i=i-65536;o.push(55296+(i>>10&1023),56320+(1023&i))}}8192<=o.length&&(n+=String.fromCharCode.apply(null,o),o.length=0)}return n+=goog.crypt.byteArrayToString(o),this.cursor_=r,n},jspb.BinaryDecoder.prototype.readStringWithLength=function(){var e=this.readUnsignedVarint32();return this.readString(e)},jspb.BinaryDecoder.prototype.readBytes=function(e){if(0>e||this.cursor_+e>this.bytes_.length)return this.error_=!0,goog.asserts.fail("Invalid byte length!"),new Uint8Array(0);var t=this.bytes_.subarray(this.cursor_,this.cursor_+e);return this.cursor_+=e,goog.asserts.assert(this.cursor_<=this.end_),t},jspb.BinaryDecoder.prototype.readVarintHash64=function(){return this.readSplitVarint64_(),jspb.utils.joinHash64(this.tempLow_,this.tempHigh_)},jspb.BinaryDecoder.prototype.readFixedHash64=function(){var e=this.bytes_,t=this.cursor_,r=e[t+0],o=e[t+1],n=e[t+2],i=e[t+3],s=e[t+4],a=e[t+5],u=e[t+6],e=e[t+7];return this.cursor_+=8,String.fromCharCode(r,o,n,i,s,a,u,e)},jspb.BinaryReader=function(e,t,r){this.decoder_=jspb.BinaryDecoder.alloc(e,t,r),this.fieldCursor_=this.decoder_.getCursor(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID,this.error_=!1,this.readCallbacks_=null},jspb.BinaryReader.instanceCache_=[],jspb.BinaryReader.alloc=function(e,t,r){if(jspb.BinaryReader.instanceCache_.length){var o=jspb.BinaryReader.instanceCache_.pop();return e&&o.decoder_.setBlock(e,t,r),o}return new jspb.BinaryReader(e,t,r)},jspb.BinaryReader.prototype.alloc=jspb.BinaryReader.alloc,jspb.BinaryReader.prototype.free=function(){this.decoder_.clear(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID,this.error_=!1,this.readCallbacks_=null,100>jspb.BinaryReader.instanceCache_.length&&jspb.BinaryReader.instanceCache_.push(this)},jspb.BinaryReader.prototype.getFieldCursor=function(){return this.fieldCursor_},jspb.BinaryReader.prototype.getCursor=function(){return this.decoder_.getCursor()},jspb.BinaryReader.prototype.getBuffer=function(){return this.decoder_.getBuffer()},jspb.BinaryReader.prototype.getFieldNumber=function(){return this.nextField_},jspb.BinaryReader.prototype.getWireType=function(){return this.nextWireType_},jspb.BinaryReader.prototype.isEndGroup=function(){return this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP},jspb.BinaryReader.prototype.getError=function(){return this.error_||this.decoder_.getError()},jspb.BinaryReader.prototype.setBlock=function(e,t,r){this.decoder_.setBlock(e,t,r),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID},jspb.BinaryReader.prototype.reset=function(){this.decoder_.reset(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID},jspb.BinaryReader.prototype.advance=function(e){this.decoder_.advance(e)},jspb.BinaryReader.prototype.nextField=function(){if(this.decoder_.atEnd())return!1;if(this.getError())return goog.asserts.fail("Decoder hit an error"),!1;this.fieldCursor_=this.decoder_.getCursor();var e=this.decoder_.readUnsignedVarint32(),t=e>>>3,e=7&e;return e!=jspb.BinaryConstants.WireType.VARINT&&e!=jspb.BinaryConstants.WireType.FIXED32&&e!=jspb.BinaryConstants.WireType.FIXED64&&e!=jspb.BinaryConstants.WireType.DELIMITED&&e!=jspb.BinaryConstants.WireType.START_GROUP&&e!=jspb.BinaryConstants.WireType.END_GROUP?(goog.asserts.fail("Invalid wire type"),this.error_=!0,!1):(this.nextField_=t,this.nextWireType_=e,!0)},jspb.BinaryReader.prototype.unskipHeader=function(){this.decoder_.unskipVarint(this.nextField_<<3|this.nextWireType_)},jspb.BinaryReader.prototype.skipMatchingFields=function(){var e=this.nextField_;for(this.unskipHeader();this.nextField()&&this.getFieldNumber()==e;)this.skipField();this.decoder_.atEnd()||this.unskipHeader()},jspb.BinaryReader.prototype.skipVarintField=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.VARINT?(goog.asserts.fail("Invalid wire type for skipVarintField"),this.skipField()):this.decoder_.skipVarint()},jspb.BinaryReader.prototype.skipDelimitedField=function(){if(this.nextWireType_!=jspb.BinaryConstants.WireType.DELIMITED)goog.asserts.fail("Invalid wire type for skipDelimitedField"),this.skipField();else{var e=this.decoder_.readUnsignedVarint32();this.decoder_.advance(e)}},jspb.BinaryReader.prototype.skipFixed32Field=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.FIXED32?(goog.asserts.fail("Invalid wire type for skipFixed32Field"),this.skipField()):this.decoder_.advance(4)},jspb.BinaryReader.prototype.skipFixed64Field=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.FIXED64?(goog.asserts.fail("Invalid wire type for skipFixed64Field"),this.skipField()):this.decoder_.advance(8)},jspb.BinaryReader.prototype.skipGroup=function(){var e=[this.nextField_];do{if(!this.nextField()){goog.asserts.fail("Unmatched start-group tag: stream EOF"),this.error_=!0;break}if(this.nextWireType_==jspb.BinaryConstants.WireType.START_GROUP)e.push(this.nextField_);else if(this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP&&this.nextField_!=e.pop()){goog.asserts.fail("Unmatched end-group tag"),this.error_=!0;break}}while(0<e.length)},jspb.BinaryReader.prototype.skipField=function(){switch(this.nextWireType_){case jspb.BinaryConstants.WireType.VARINT:this.skipVarintField();break;case jspb.BinaryConstants.WireType.FIXED64:this.skipFixed64Field();break;case jspb.BinaryConstants.WireType.DELIMITED:this.skipDelimitedField();break;case jspb.BinaryConstants.WireType.FIXED32:this.skipFixed32Field();break;case jspb.BinaryConstants.WireType.START_GROUP:this.skipGroup();break;default:goog.asserts.fail("Invalid wire encoding for field.")}},jspb.BinaryReader.prototype.registerReadCallback=function(e,t){goog.isNull(this.readCallbacks_)&&(this.readCallbacks_={}),goog.asserts.assert(!this.readCallbacks_[e]),this.readCallbacks_[e]=t},jspb.BinaryReader.prototype.runReadCallback=function(e){return goog.asserts.assert(!goog.isNull(this.readCallbacks_)),e=this.readCallbacks_[e],goog.asserts.assert(e),e(this)},jspb.BinaryReader.prototype.readAny=function(e){this.nextWireType_=jspb.BinaryConstants.FieldTypeToWireType(e);var t=jspb.BinaryConstants.FieldType;switch(e){case t.DOUBLE:return this.readDouble();case t.FLOAT:return this.readFloat();case t.INT64:return this.readInt64();case t.UINT64:return this.readUint64();case t.INT32:return this.readInt32();case t.FIXED64:return this.readFixed64();case t.FIXED32:return this.readFixed32();case t.BOOL:return this.readBool();case t.STRING:return this.readString();case t.GROUP:goog.asserts.fail("Group field type not supported in readAny()");case t.MESSAGE:goog.asserts.fail("Message field type not supported in readAny()");case t.BYTES:return this.readBytes();case t.UINT32:return this.readUint32();case t.ENUM:return this.readEnum();case t.SFIXED32:return this.readSfixed32();case t.SFIXED64:return this.readSfixed64();case t.SINT32:return this.readSint32();case t.SINT64:return this.readSint64();case t.FHASH64:return this.readFixedHash64();case t.VHASH64:
return this.readVarintHash64();default:goog.asserts.fail("Invalid field type in readAny()")}return 0},jspb.BinaryReader.prototype.readMessage=function(e,t){goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var r=this.decoder_.getEnd(),o=this.decoder_.readUnsignedVarint32(),o=this.decoder_.getCursor()+o;this.decoder_.setEnd(o),t(e,this),this.decoder_.setCursor(o),this.decoder_.setEnd(r)},jspb.BinaryReader.prototype.readGroup=function(e,t,r){goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.START_GROUP),goog.asserts.assert(this.nextField_==e),r(t,this),this.error_||this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP||(goog.asserts.fail("Group submessage did not end with an END_GROUP tag"),this.error_=!0)},jspb.BinaryReader.prototype.getFieldDecoder=function(){goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var e=this.decoder_.readUnsignedVarint32(),t=this.decoder_.getCursor(),r=t+e,e=jspb.BinaryDecoder.alloc(this.decoder_.getBuffer(),t,e);return this.decoder_.setCursor(r),e},jspb.BinaryReader.prototype.readInt32=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint32()},jspb.BinaryReader.prototype.readInt32String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint32String()},jspb.BinaryReader.prototype.readInt64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64()},jspb.BinaryReader.prototype.readInt64String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64String()},jspb.BinaryReader.prototype.readUint32=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint32()},jspb.BinaryReader.prototype.readUint32String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint32String()},jspb.BinaryReader.prototype.readUint64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint64()},jspb.BinaryReader.prototype.readUint64String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint64String()},jspb.BinaryReader.prototype.readSint32=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint32()},jspb.BinaryReader.prototype.readSint64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint64()},jspb.BinaryReader.prototype.readSint64String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint64String()},jspb.BinaryReader.prototype.readFixed32=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readUint32()},jspb.BinaryReader.prototype.readFixed64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readUint64()},jspb.BinaryReader.prototype.readFixed64String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readUint64String()},jspb.BinaryReader.prototype.readSfixed32=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readInt32()},jspb.BinaryReader.prototype.readSfixed32String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readInt32().toString()},jspb.BinaryReader.prototype.readSfixed64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readInt64()},jspb.BinaryReader.prototype.readSfixed64String=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readInt64String()},jspb.BinaryReader.prototype.readFloat=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readFloat()},jspb.BinaryReader.prototype.readDouble=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readDouble()},jspb.BinaryReader.prototype.readBool=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),!!this.decoder_.readUnsignedVarint32()},jspb.BinaryReader.prototype.readEnum=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64()},jspb.BinaryReader.prototype.readString=function(){goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var e=this.decoder_.readUnsignedVarint32();return this.decoder_.readString(e)},jspb.BinaryReader.prototype.readBytes=function(){goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var e=this.decoder_.readUnsignedVarint32();return this.decoder_.readBytes(e)},jspb.BinaryReader.prototype.readVarintHash64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readVarintHash64()},jspb.BinaryReader.prototype.readFixedHash64=function(){return goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readFixedHash64()},jspb.BinaryReader.prototype.readPackedField_=function(e){goog.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);for(var t=this.decoder_.readUnsignedVarint32(),t=this.decoder_.getCursor()+t,r=[];this.decoder_.getCursor()<t;)r.push(e.call(this.decoder_));return r},jspb.BinaryReader.prototype.readPackedInt32=function(){return this.readPackedField_(this.decoder_.readSignedVarint32)},jspb.BinaryReader.prototype.readPackedInt32String=function(){return this.readPackedField_(this.decoder_.readSignedVarint32String)},jspb.BinaryReader.prototype.readPackedInt64=function(){return this.readPackedField_(this.decoder_.readSignedVarint64)},jspb.BinaryReader.prototype.readPackedInt64String=function(){return this.readPackedField_(this.decoder_.readSignedVarint64String)},jspb.BinaryReader.prototype.readPackedUint32=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint32)},jspb.BinaryReader.prototype.readPackedUint32String=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint32String)},jspb.BinaryReader.prototype.readPackedUint64=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint64)},jspb.BinaryReader.prototype.readPackedUint64String=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint64String)},jspb.BinaryReader.prototype.readPackedSint32=function(){return this.readPackedField_(this.decoder_.readZigzagVarint32)},jspb.BinaryReader.prototype.readPackedSint64=function(){return this.readPackedField_(this.decoder_.readZigzagVarint64)},jspb.BinaryReader.prototype.readPackedSint64String=function(){return this.readPackedField_(this.decoder_.readZigzagVarint64String)},jspb.BinaryReader.prototype.readPackedFixed32=function(){return this.readPackedField_(this.decoder_.readUint32)},jspb.BinaryReader.prototype.readPackedFixed64=function(){return this.readPackedField_(this.decoder_.readUint64)},jspb.BinaryReader.prototype.readPackedFixed64String=function(){return this.readPackedField_(this.decoder_.readUint64String)},jspb.BinaryReader.prototype.readPackedSfixed32=function(){return this.readPackedField_(this.decoder_.readInt32)},jspb.BinaryReader.prototype.readPackedSfixed64=function(){return this.readPackedField_(this.decoder_.readInt64)},jspb.BinaryReader.prototype.readPackedSfixed64String=function(){return this.readPackedField_(this.decoder_.readInt64String)},jspb.BinaryReader.prototype.readPackedFloat=function(){return this.readPackedField_(this.decoder_.readFloat)},jspb.BinaryReader.prototype.readPackedDouble=function(){return this.readPackedField_(this.decoder_.readDouble)},jspb.BinaryReader.prototype.readPackedBool=function(){return this.readPackedField_(this.decoder_.readBool)},jspb.BinaryReader.prototype.readPackedEnum=function(){return this.readPackedField_(this.decoder_.readEnum)},jspb.BinaryReader.prototype.readPackedVarintHash64=function(){return this.readPackedField_(this.decoder_.readVarintHash64)},jspb.BinaryReader.prototype.readPackedFixedHash64=function(){return this.readPackedField_(this.decoder_.readFixedHash64)},jspb.Export={},exports.Map=jspb.Map,exports.Message=jspb.Message,exports.BinaryReader=jspb.BinaryReader,exports.BinaryWriter=jspb.BinaryWriter,exports.ExtensionFieldInfo=jspb.ExtensionFieldInfo,exports.ExtensionFieldBinaryInfo=jspb.ExtensionFieldBinaryInfo,exports.exportSymbol=goog.exportSymbol,exports.inherits=goog.inherits,exports.object={extend:goog.object.extend},exports.typeOf=goog.typeOf}).call(exports,__webpack_require__(3))},function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"==("undefined"==typeof window?"undefined":(0,_typeof3["default"])(window))&&(r=window)}e.exports=r}]),module.exports=SVGAPackaging},function(e,t,r){e.exports={"default":r(58),__esModule:!0}},function(e,t,r){r(59),e.exports=r(10).Object.freeze},function(e,t,r){var o=r(16),n=r(22).onFreeze;r(60)("freeze",function(e){return function t(r){return e&&o(r)?e(n(r)):r}})},function(e,t,r){var o=r(9),n=r(10),i=r(8);e.exports=function(e,t){var r=(n.Object||{})[e]||Object[e],s={};s[e]=t(r),o(o.S+o.F*i(function(){r(1)}),"Object",s)}},function(e,t,r){e.exports={"default":r(62),__esModule:!0}},function(e,t,r){r(63);var o=r(10).Object;e.exports=function n(e,t){return o.create(e,t)}},function(e,t,r){var o=r(9);o(o.S,"Object",{create:r(47)})},function(e,t,r){e.exports={"default":r(65),__esModule:!0}},function(e,t,r){r(66),e.exports=r(10).Object.isFrozen},function(e,t,r){var o=r(16);r(60)("isFrozen",function(e){return function t(r){return!o(r)||!!e&&e(r)}})},function(e,t,r){e.exports={"default":r(68),__esModule:!0}},function(e,t,r){r(69),e.exports=r(10).Object.seal},function(e,t,r){var o=r(16),n=r(22).onFreeze;r(60)("seal",function(e){return function t(r){return e&&o(r)?e(n(r)):r}})},function(e,t,r){e.exports={"default":r(71),__esModule:!0}},function(e,t,r){r(72);var o=r(10).Object;e.exports=function n(e,t){return o.getOwnPropertyDescriptor(e,t)}},function(e,t,r){var o=r(33),n=r(52).f;r(60)("getOwnPropertyDescriptor",function(){return function e(t,r){return n(o(t),r)}})},function(e,t,r){e.exports={"default":r(74),__esModule:!0}},function(e,t,r){r(75);var o=r(10).Object;e.exports=function n(e,t){return o.defineProperties(e,t)}},function(e,t,r){var o=r(9);o(o.S+o.F*!r(7),"Object",{defineProperties:r(48)})},function(e,t,r){e.exports={"default":r(77),__esModule:!0}},function(e,t,r){r(78),r(85),e.exports=r(27).f("iterator")},function(e,t,r){"use strict";var o=r(79)(!0);r(80)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e=this._t,t=this._i,r;return t>=e.length?{value:void 0,done:!0}:(r=o(e,t),this._i+=r.length,{value:r,done:!1})})},function(e,t,r){var o=r(39),n=r(36);e.exports=function(e){return function(t,r){var i=String(n(t)),s=o(r),a=i.length,u,p;return s<0||s>=a?e?"":void 0:(u=i.charCodeAt(s),u<55296||u>56319||s+1===a||(p=i.charCodeAt(s+1))<56320||p>57343?e?i.charAt(s):u:e?i.slice(s,s+2):(u-55296<<10)+(p-56320)+65536)}}},function(e,t,r){"use strict";var o=r(29),n=r(9),i=r(21),s=r(13),a=r(6),u=r(81),p=r(82),g=r(25),c=r(83),l=r(26)("iterator"),h=!([].keys&&"next"in[].keys()),f="@@iterator",d="keys",y="values",_=function(){return this};e.exports=function(e,t,r,m,b,v,S){p(r,t,m);var E=function(e){if(!h&&e in T)return T[e];switch(e){case d:return function t(){return new r(this,e)};case y:return function o(){return new r(this,e)}}return function n(){return new r(this,e)}},w=t+" Iterator",A=b==y,j=!1,T=e.prototype,I=T[l]||T[f]||b&&T[b],B=I||E(b),M=b?A?E("entries"):B:void 0,O="Array"==t?T.entries||I:I,x,C,F;if(O&&(F=c(O.call(new e)),F!==Object.prototype&&(g(F,w,!0),o||a(F,l)||s(F,l,_))),A&&I&&I.name!==y&&(j=!0,B=function k(){return I.call(this)}),o&&!S||!h&&!j&&T[l]||s(T,l,B),u[t]=B,u[w]=_,b)if(x={values:A?B:E(y),keys:v?B:E(d),entries:M},S)for(C in x)C in T||i(T,C,x[C]);else n(n.P+n.F*(h||j),t,x);return x}},function(e,t){e.exports={}},function(e,t,r){"use strict";var o=r(47),n=r(20),i=r(25),s={};r(13)(s,r(26)("iterator"),function(){return this}),e.exports=function(e,t,r){e.prototype=o(s,{next:n(1,r)}),i(e,t+" Iterator")}},function(e,t,r){var o=r(6),n=r(84),i=r(41)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=n(e),o(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},function(e,t,r){var o=r(36);e.exports=function(e){return Object(o(e))}},function(e,t,r){r(86);for(var o=r(5),n=r(13),i=r(81),s=r(26)("toStringTag"),a=["NodeList","DOMTokenList","MediaList","StyleSheetList","CSSRuleList"],u=0;u<5;u++){var p=a[u],g=o[p],c=g&&g.prototype;c&&!c[s]&&n(c,s,p),i[p]=i.Array}},function(e,t,r){"use strict";var o=r(87),n=r(88),i=r(81),s=r(33);e.exports=r(80)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,n(1)):"keys"==t?n(0,r):"values"==t?n(0,e[r]):n(0,[r,e[r]])},"values"),i.Arguments=i.Array,o("keys"),o("values"),o("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){e.exports={"default":r(90),__esModule:!0}},function(e,t,r){r(91);var o=r(10).Object;e.exports=function n(e,t,r){return o.defineProperty(e,t,r)}},function(e,t,r){var o=r(9);o(o.S+o.F*!r(7),"Object",{defineProperty:r(14).f})},function(e,t,r){e.exports={"default":r(93),__esModule:!0}},function(e,t,r){var o=r(10),n=o.JSON||(o.JSON={stringify:JSON.stringify});e.exports=function i(e){return n.stringify.apply(n,arguments)}},function(e,t,r){var o,o;(function(t,r){!function(t){var r;e.exports=t()}(function(){return function e(t,r,n){function i(a,u){if(!r[a]){if(!t[a]){var p="function"==typeof o&&o;if(!u&&p)return o(a,!0);if(s)return s(a,!0);var g=new Error("Cannot find module '"+a+"'");throw g.code="MODULE_NOT_FOUND",g}var c=r[a]={exports:{}};t[a][0].call(c.exports,function(e){var r=t[a][1][e];return i(r?r:e)},c,c.exports,e,t,r,n)}return r[a].exports}for(var s="function"==typeof o&&o,a=0;a<n.length;a++)i(n[a]);return i}({1:[function(e,t,r){"use strict";var o=e("./utils"),n=e("./support"),i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(e){for(var t,r,n,s,a,u,p,g=[],c=0,l=e.length,h=l,f="string"!==o.getTypeOf(e);c<e.length;)h=l-c,f?(t=e[c++],r=c<l?e[c++]:0,n=c<l?e[c++]:0):(t=e.charCodeAt(c++),r=c<l?e.charCodeAt(c++):0,n=c<l?e.charCodeAt(c++):0),s=t>>2,a=(3&t)<<4|r>>4,u=h>1?(15&r)<<2|n>>6:64,p=h>2?63&n:64,g.push(i.charAt(s)+i.charAt(a)+i.charAt(u)+i.charAt(p));return g.join("")},r.decode=function(e){var t,r,o,s,a,u,p,g=0,c=0,l="data:";if(e.substr(0,l.length)===l)throw new Error("Invalid base64 input, it looks like a data url.");e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");var h=3*e.length/4;if(e.charAt(e.length-1)===i.charAt(64)&&h--,e.charAt(e.length-2)===i.charAt(64)&&h--,h%1!==0)throw new Error("Invalid base64 input, bad content length.");var f;for(f=n.uint8array?new Uint8Array(0|h):new Array(0|h);g<e.length;)s=i.indexOf(e.charAt(g++)),a=i.indexOf(e.charAt(g++)),u=i.indexOf(e.charAt(g++)),p=i.indexOf(e.charAt(g++)),t=s<<2|a>>4,r=(15&a)<<4|u>>2,o=(3&u)<<6|p,f[c++]=t,64!==u&&(f[c++]=r),64!==p&&(f[c++]=o);return f}},{"./support":30,"./utils":32}],2:[function(e,t,r){"use strict";function o(e,t,r,o,n){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=o,this.compressedContent=n}var n=e("./external"),i=e("./stream/DataWorker"),s=e("./stream/DataLengthProbe"),a=e("./stream/Crc32Probe"),s=e("./stream/DataLengthProbe");o.prototype={getContentWorker:function(){var e=new i(n.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new s("data_length")),t=this;return e.on("end",function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),e},getCompressedWorker:function(){return new i(n.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(e,t,r){return e.pipe(new a).pipe(new s("uncompressedSize")).pipe(t.compressWorker(r)).pipe(new s("compressedSize")).withStreamInfo("compression",t)},t.exports=o},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(e,t,r){"use strict";var o=e("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(e){return new o("STORE compression")},uncompressWorker:function(){return new o("STORE decompression")}},r.DEFLATE=e("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(e,t,r){"use strict";function o(){for(var e,t=[],r=0;r<256;r++){e=r;for(var o=0;o<8;o++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}function n(e,t,r,o){var n=a,i=o+r;e^=-1;for(var s=o;s<i;s++)e=e>>>8^n[255&(e^t[s])];return e^-1}function i(e,t,r,o){var n=a,i=o+r;e^=-1;for(var s=o;s<i;s++)e=e>>>8^n[255&(e^t.charCodeAt(s))];return e^-1}var s=e("./utils"),a=o();t.exports=function(e,t){if("undefined"==typeof e||!e.length)return 0;var r="string"!==s.getTypeOf(e);return r?n(0|t,e,e.length,0):i(0|t,e,e.length,0)}},{"./utils":32}],5:[function(e,t,r){"use strict";r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(e,t,r){"use strict";var o=null;o="undefined"!=typeof Promise?Promise:e("lie"),t.exports={Promise:o}},{lie:58}],7:[function(e,t,r){"use strict";function o(e,t){a.call(this,"FlateWorker/"+e),this._pako=new i[e]({raw:!0,level:t.level||-1}),this.meta={};var r=this;this._pako.onData=function(e){r.push({data:e,meta:r.meta})}}var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,i=e("pako"),s=e("./utils"),a=e("./stream/GenericWorker"),u=n?"uint8array":"array";r.magic="\b\0",s.inherits(o,a),o.prototype.processChunk=function(e){this.meta=e.meta,this._pako.push(s.transformTo(u,e.data),!1)},o.prototype.flush=function(){a.prototype.flush.call(this),this._pako.push([],!0)},o.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this._pako=null},r.compressWorker=function(e){return new o("Deflate",e)},r.uncompressWorker=function(){return new o("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:59}],8:[function(e,t,r){"use strict";function o(e,t,r,o){i.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=r,this.encodeFileName=o,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}var n=e("../utils"),i=e("../stream/GenericWorker"),s=e("../utf8"),a=e("../crc32"),u=e("../signature"),p=function(e,t){var r,o="";for(r=0;r<t;r++)o+=String.fromCharCode(255&e),e>>>=8;return o},g=function(e,t){var r=e;return e||(r=t?16893:33204),(65535&r)<<16},c=function(e,t){return 63&(e||0)},l=function(e,t,r,o,i,l){var h,f,d=e.file,y=e.compression,_=l!==s.utf8encode,m=n.transformTo("string",l(d.name)),b=n.transformTo("string",s.utf8encode(d.name)),v=d.comment,S=n.transformTo("string",l(v)),E=n.transformTo("string",s.utf8encode(v)),w=b.length!==d.name.length,A=E.length!==v.length,j="",T="",I="",B=d.dir,M=d.date,O={crc32:0,compressedSize:0,uncompressedSize:0};t&&!r||(O.crc32=e.crc32,O.compressedSize=e.compressedSize,O.uncompressedSize=e.uncompressedSize);var x=0;t&&(x|=8),_||!w&&!A||(x|=2048);var C=0,F=0;B&&(C|=16),"UNIX"===i?(F=798,C|=g(d.unixPermissions,B)):(F=20,C|=c(d.dosPermissions,B)),h=M.getUTCHours(),h<<=6,h|=M.getUTCMinutes(),h<<=5,h|=M.getUTCSeconds()/2,f=M.getUTCFullYear()-1980,f<<=4,f|=M.getUTCMonth()+1,f<<=5,f|=M.getUTCDate(),w&&(T=p(1,1)+p(a(m),4)+b,j+="up"+p(T.length,2)+T),A&&(I=p(1,1)+p(a(S),4)+E,j+="uc"+p(I.length,2)+I);var k="";k+="\n\0",k+=p(x,2),k+=y.magic,k+=p(h,2),k+=p(f,2),k+=p(O.crc32,4),k+=p(O.compressedSize,4),k+=p(O.uncompressedSize,4),k+=p(m.length,2),k+=p(j.length,2);var R=u.LOCAL_FILE_HEADER+k+m+j,D=u.CENTRAL_FILE_HEADER+p(F,2)+k+p(S.length,2)+"\0\0\0\0"+p(C,4)+p(o,4)+m+j+S;return{fileRecord:R,dirRecord:D}},h=function(e,t,r,o,i){var s="",a=n.transformTo("string",i(o));return s=u.CENTRAL_DIRECTORY_END+"\0\0\0\0"+p(e,2)+p(e,2)+p(t,4)+p(r,4)+p(a.length,2)+a},f=function(e){var t="";return t=u.DATA_DESCRIPTOR+p(e.crc32,4)+p(e.compressedSize,4)+p(e.uncompressedSize,4)};n.inherits(o,i),o.prototype.push=function(e){var t=e.meta.percent||0,r=this.entriesCount,o=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,i.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:r?(t+100*(r-o-1))/r:100}}))},o.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=l(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},o.prototype.closedSource=function(e){this.accumulate=!1;var t=this.streamFiles&&!e.file.dir,r=l(e,t,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(r.dirRecord),t)this.push({data:f(e),meta:{percent:100}});else for(this.push({data:r.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},o.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}});var r=this.bytesWritten-e,o=h(this.dirRecords.length,r,e,this.zipComment,this.encodeFileName);this.push({data:o,meta:{percent:100}})},o.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},o.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on("data",function(e){t.processChunk(e)}),e.on("end",function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()}),e.on("error",function(e){t.error(e)}),this},o.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},o.prototype.error=function(e){var t=this._sources;if(!i.prototype.error.call(this,e))return!1;for(var r=0;r<t.length;r++)try{t[r].error(e)}catch(e){}return!0},o.prototype.lock=function(){i.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},t.exports=o},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(e,t,r){"use strict";var o=e("../compressions"),n=e("./ZipFileWorker"),i=function(e,t){var r=e||t,n=o[r];if(!n)throw new Error(r+" is not a valid compression method !");return n};r.generateWorker=function(e,t,r){var o=new n(t.streamFiles,r,t.platform,t.encodeFileName),s=0;try{e.forEach(function(e,r){s++;var n=i(r.options.compression,t.compression),a=r.options.compressionOptions||t.compressionOptions||{},u=r.dir,p=r.date;r._compressWorker(n,a).withStreamInfo("file",{name:e,dir:u,date:p,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(o)}),o.entriesCount=s}catch(a){o.error(a)}return o}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(e,t,r){"use strict";function o(){if(!(this instanceof o))return new o;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files={},this.comment=null,this.root="",this.clone=function(){var e=new o;for(var t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}o.prototype=e("./object"),o.prototype.loadAsync=e("./load"),o.support=e("./support"),o.defaults=e("./defaults"),o.version="3.1.3",o.loadAsync=function(e,t){return(new o).loadAsync(e,t)},o.external=e("./external"),t.exports=o},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(e,t,r){"use strict";function o(e){return new i.Promise(function(t,r){var o=e.decompressed.getContentWorker().pipe(new u);o.on("error",function(e){r(e)}).on("end",function(){o.streamInfo.crc32!==e.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):t()}).resume()})}var n=e("./utils"),i=e("./external"),s=e("./utf8"),n=e("./utils"),a=e("./zipEntries"),u=e("./stream/Crc32Probe"),p=e("./nodejsUtils");t.exports=function(e,t){var r=this;return t=n.extend(t||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),p.isNode&&p.isStream(e)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):n.prepareContent("the loaded zip file",e,!0,t.optimizedBinaryString,t.base64).then(function(e){var r=new a(t);return r.load(e),r}).then(function(e){var r=[i.Promise.resolve(e)],n=e.files;if(t.checkCRC32)for(var s=0;s<n.length;s++)r.push(o(n[s]));return i.Promise.all(r)}).then(function(e){for(var o=e.shift(),n=o.files,i=0;i<n.length;i++){var s=n[i];r.file(s.fileNameStr,s.decompressed,{binary:!0,optimizedBinaryString:!0,date:s.date,dir:s.dir,comment:s.fileCommentStr.length?s.fileCommentStr:null,unixPermissions:s.unixPermissions,dosPermissions:s.dosPermissions,createFolders:t.createFolders})}return o.zipComment.length&&(r.comment=o.zipComment),r})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(e,t,r){"use strict";function o(e,t){i.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(t)}var n=e("../utils"),i=e("../stream/GenericWorker");n.inherits(o,i),o.prototype._bindStream=function(e){var t=this;this._stream=e,e.pause(),e.on("data",function(e){t.push({data:e,meta:{percent:0}})}).on("error",function(e){t.isPaused?this.generatedError=e:t.error(e)}).on("end",function(){t.isPaused?t._upstreamEnded=!0:t.end()})},o.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},o.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=o},{"../stream/GenericWorker":28,"../utils":32}],13:[function(e,t,r){"use strict";function o(e,t,r){n.call(this,t),this._helper=e;var o=this;e.on("data",function(e,t){o.push(e)||o._helper.pause(),r&&r(t)}).on("error",function(e){o.emit("error",e)}).on("end",function(){o.push(null)})}var n=e("readable-stream").Readable,i=e("util");i.inherits(o,n),o.prototype._read=function(){this._helper.resume()},t.exports=o},{"readable-stream":16,util:void 0}],14:[function(e,r,o){"use strict";r.exports={isNode:"undefined"!=typeof t,newBuffer:function(e,r){return new t(e,r)},isBuffer:function(e){return t.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}}},{}],15:[function(e,t,r){"use strict";function o(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var n=e("./utf8"),i=e("./utils"),s=e("./stream/GenericWorker"),a=e("./stream/StreamHelper"),u=e("./defaults"),p=e("./compressedObject"),g=e("./zipObject"),c=e("./generate"),l=e("./nodejsUtils"),h=e("./nodejs/NodejsStreamInputAdapter"),f=function(e,t,r){var o,n=i.getTypeOf(t),a=i.extend(r||{},u);a.date=a.date||new Date,null!==a.compression&&(a.compression=a.compression.toUpperCase()),"string"==typeof a.unixPermissions&&(a.unixPermissions=parseInt(a.unixPermissions,8)),a.unixPermissions&&16384&a.unixPermissions&&(a.dir=!0),a.dosPermissions&&16&a.dosPermissions&&(a.dir=!0),a.dir&&(e=y(e)),a.createFolders&&(o=d(e))&&_.call(this,o,!0);var c="string"===n&&a.binary===!1&&a.base64===!1;r&&"undefined"!=typeof r.binary||(a.binary=!c);var f=t instanceof p&&0===t.uncompressedSize;(f||a.dir||!t||0===t.length)&&(a.base64=!1,a.binary=!0,t="",a.compression="STORE",n="string");var m=null;m=t instanceof p||t instanceof s?t:l.isNode&&l.isStream(t)?new h(e,t):i.prepareContent(e,t,a.binary,a.optimizedBinaryString,a.base64);var b=new g(e,m,a);this.files[e]=b},d=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return t>0?e.substring(0,t):""},y=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},_=function(e,t){return t="undefined"!=typeof t?t:u.createFolders,e=y(e),this.files[e]||f.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]},m={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,r,o;for(t in this.files)this.files.hasOwnProperty(t)&&(o=this.files[t],r=t.slice(this.root.length,t.length),r&&t.slice(0,this.root.length)===this.root&&e(r,o))},filter:function(e){var t=[];return this.forEach(function(r,o){e(r,o)&&t.push(o)}),t},file:function(e,t,r){if(1===arguments.length){if(o(e)){var n=e;return this.filter(function(e,t){return!t.dir&&n.test(e)})}var i=this.files[this.root+e];return i&&!i.dir?i:null}return e=this.root+e,f.call(this,e,t,r),this},folder:function(e){if(!e)return this;if(o(e))return this.filter(function(t,r){return r.dir&&e.test(t)});var t=this.root+e,r=_.call(this,t),n=this.clone();return n.root=r.name,n},remove:function(e){e=this.root+e;var t=this.files[e];if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var r=this.filter(function(t,r){return r.name.slice(0,e.length)===e}),o=0;o<r.length;o++)delete this.files[r[o].name];return this},generate:function(e){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var t,r={};try{if(r=i.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:n.utf8encode}),r.type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),"binarystring"===r.type&&(r.type="string"),!r.type)throw new Error("No output type specified.");i.checkSupport(r.type),"darwin"!==r.platform&&"freebsd"!==r.platform&&"linux"!==r.platform&&"sunos"!==r.platform||(r.platform="UNIX"),"win32"===r.platform&&(r.platform="DOS");var o=r.comment||this.comment||"";t=c.generateWorker(this,r,o)}catch(u){t=new s("error"),t.error(u)}return new a(t,r.type||"string",r.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return e=e||{},e.type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};t.exports=m},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(e,t,r){t.exports=e("stream")},{stream:void 0}],17:[function(e,t,r){"use strict";function o(e){n.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}var n=e("./DataReader"),i=e("../utils");i.inherits(o,n),o.prototype.byteAt=function(e){return this.data[this.zero+e]},o.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),o=e.charCodeAt(2),n=e.charCodeAt(3),i=this.length-4;i>=0;--i)if(this.data[i]===t&&this.data[i+1]===r&&this.data[i+2]===o&&this.data[i+3]===n)return i-this.zero;
return-1},o.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),o=e.charCodeAt(2),n=e.charCodeAt(3),i=this.readData(4);return t===i[0]&&r===i[1]&&o===i[2]&&n===i[3]},o.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=o},{"../utils":32,"./DataReader":18}],18:[function(e,t,r){"use strict";function o(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}var n=e("../utils");o.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(e){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo("string",this.readData(e))},readData:function(e){},lastIndexOfSignature:function(e){},readAndCheckSignature:function(e){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC((e>>25&127)+1980,(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},t.exports=o},{"../utils":32}],19:[function(e,t,r){"use strict";function o(e){n.call(this,e)}var n=e("./Uint8ArrayReader"),i=e("../utils");i.inherits(o,n),o.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=o},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(e,t,r){"use strict";function o(e){n.call(this,e)}var n=e("./DataReader"),i=e("../utils");i.inherits(o,n),o.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},o.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},o.prototype.readAndCheckSignature=function(e){var t=this.readData(4);return e===t},o.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=o},{"../utils":32,"./DataReader":18}],21:[function(e,t,r){"use strict";function o(e){n.call(this,e)}var n=e("./ArrayReader"),i=e("../utils");i.inherits(o,n),o.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=o},{"../utils":32,"./ArrayReader":17}],22:[function(e,t,r){"use strict";var o=e("../utils"),n=e("../support"),i=e("./ArrayReader"),s=e("./StringReader"),a=e("./NodeBufferReader"),u=e("./Uint8ArrayReader");t.exports=function(e){var t=o.getTypeOf(e);return o.checkSupport(t),"string"!==t||n.uint8array?"nodebuffer"===t?new a(e):n.uint8array?new u(o.transformTo("uint8array",e)):new i(o.transformTo("array",e)):new s(e)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(e,t,r){"use strict";r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],24:[function(e,t,r){"use strict";function o(e){n.call(this,"ConvertWorker to "+e),this.destType=e}var n=e("./GenericWorker"),i=e("../utils");i.inherits(o,n),o.prototype.processChunk=function(e){this.push({data:i.transformTo(this.destType,e.data),meta:e.meta})},t.exports=o},{"../utils":32,"./GenericWorker":28}],25:[function(e,t,r){"use strict";function o(){n.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}var n=e("./GenericWorker"),i=e("../crc32"),s=e("../utils");s.inherits(o,n),o.prototype.processChunk=function(e){this.streamInfo.crc32=i(e.data,this.streamInfo.crc32||0),this.push(e)},t.exports=o},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(e,t,r){"use strict";function o(e){i.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}var n=e("../utils"),i=e("./GenericWorker");n.inherits(o,i),o.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}i.prototype.processChunk.call(this,e)},t.exports=o},{"../utils":32,"./GenericWorker":28}],27:[function(e,t,r){"use strict";function o(e){i.call(this,"DataWorker");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then(function(e){t.dataIsReady=!0,t.data=e,t.max=e&&e.length||0,t.type=n.getTypeOf(e),t.isPaused||t._tickAndRepeat()},function(e){t.error(e)})}var n=e("../utils"),i=e("./GenericWorker"),s=16384;n.inherits(o,i),o.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},o.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,n.delay(this._tickAndRepeat,[],this)),!0)},o.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},o.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=s,t=null,r=Math.min(this.max,this.index+e);if(this.index>=this.max)return this.end();switch(this.type){case"string":t=this.data.substring(this.index,r);break;case"uint8array":t=this.data.subarray(this.index,r);break;case"array":case"nodebuffer":t=this.data.slice(this.index,r)}return this.index=r,this.push({data:t,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=o},{"../utils":32,"./GenericWorker":28}],28:[function(e,t,r){"use strict";function o(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}o.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",function(e){t.processChunk(e)}),e.on("end",function(){t.end()}),e.on("error",function(e){t.error(e)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;this.isPaused=!1;var e=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)this.extraStreamInfo.hasOwnProperty(e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},t.exports=o},{}],29:[function(e,r,o){"use strict";function n(e,t,r,o){var n=null;switch(e){case"blob":return u.newBlob(r,o);case"base64":return n=i(t,r),c.encode(n);default:return n=i(t,r),u.transformTo(e,n)}}function i(e,r){var o,n=0,i=null,s=0;for(o=0;o<r.length;o++)s+=r[o].length;switch(e){case"string":return r.join("");case"array":return Array.prototype.concat.apply([],r);case"uint8array":for(i=new Uint8Array(s),o=0;o<r.length;o++)i.set(r[o],n),n+=r[o].length;return i;case"nodebuffer":return t.concat(r);default:throw new Error("concat : unsupported type '"+e+"'")}}function s(e,t){return new h.Promise(function(r,o){var i=[],s=e._internalType,a=e._outputType,u=e._mimeType;e.on("data",function(e,r){i.push(e),t&&t(r)}).on("error",function(e){i=[],o(e)}).on("end",function(){try{var e=n(a,s,i,u);r(e)}catch(t){o(t)}i=[]}).resume()})}function a(e,t,r){var o=t;switch(t){case"blob":o="arraybuffer";break;case"arraybuffer":o="uint8array";break;case"base64":o="string"}try{this._internalType=o,this._outputType=t,this._mimeType=r,u.checkSupport(o),this._worker=e.pipe(new p(o)),e.lock()}catch(n){this._worker=new g("error"),this._worker.error(n)}}var u=e("../utils"),p=e("./ConvertWorker"),g=e("./GenericWorker"),c=e("../base64"),l=e("../support"),h=e("../external"),f=null;if(l.nodestream)try{f=e("../nodejs/NodejsStreamOutputAdapter")}catch(d){}a.prototype={accumulate:function(e){return s(this,e)},on:function(e,t){var r=this;return"data"===e?this._worker.on(e,function(e){t.call(r,e.data,e.meta)}):this._worker.on(e,function(){u.delay(t,arguments,r)}),this},resume:function(){return u.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(u.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new f(this,{objectMode:"nodebuffer"!==this._outputType},e)}},r.exports=a},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(e,r,o){"use strict";if(o.base64=!0,o.array=!0,o.string=!0,o.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,o.nodebuffer="undefined"!=typeof t,o.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)o.blob=!1;else{var n=new ArrayBuffer(0);try{o.blob=0===new Blob([n],{type:"application/zip"}).size}catch(i){try{var s=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,a=new s;a.append(n),o.blob=0===a.getBlob("application/zip").size}catch(i){o.blob=!1}}}try{o.nodestream=!!e("readable-stream").Readable}catch(i){o.nodestream=!1}},{"readable-stream":16}],31:[function(e,t,r){"use strict";function o(){u.call(this,"utf-8 decode"),this.leftOver=null}function n(){u.call(this,"utf-8 encode")}for(var i=e("./utils"),s=e("./support"),a=e("./nodejsUtils"),u=e("./stream/GenericWorker"),p=new Array(256),g=0;g<256;g++)p[g]=g>=252?6:g>=248?5:g>=240?4:g>=224?3:g>=192?2:1;p[254]=p[254]=1;var c=function(e){var t,r,o,n,i,a=e.length,u=0;for(n=0;n<a;n++)r=e.charCodeAt(n),55296===(64512&r)&&n+1<a&&(o=e.charCodeAt(n+1),56320===(64512&o)&&(r=65536+(r-55296<<10)+(o-56320),n++)),u+=r<128?1:r<2048?2:r<65536?3:4;for(t=s.uint8array?new Uint8Array(u):new Array(u),i=0,n=0;i<u;n++)r=e.charCodeAt(n),55296===(64512&r)&&n+1<a&&(o=e.charCodeAt(n+1),56320===(64512&o)&&(r=65536+(r-55296<<10)+(o-56320),n++)),r<128?t[i++]=r:r<2048?(t[i++]=192|r>>>6,t[i++]=128|63&r):r<65536?(t[i++]=224|r>>>12,t[i++]=128|r>>>6&63,t[i++]=128|63&r):(t[i++]=240|r>>>18,t[i++]=128|r>>>12&63,t[i++]=128|r>>>6&63,t[i++]=128|63&r);return t},l=function(e,t){var r;for(t=t||e.length,t>e.length&&(t=e.length),r=t-1;r>=0&&128===(192&e[r]);)r--;return r<0?t:0===r?t:r+p[e[r]]>t?r:t},h=function(e){var t,r,o,n,s=e.length,a=new Array(2*s);for(r=0,t=0;t<s;)if(o=e[t++],o<128)a[r++]=o;else if(n=p[o],n>4)a[r++]=65533,t+=n-1;else{for(o&=2===n?31:3===n?15:7;n>1&&t<s;)o=o<<6|63&e[t++],n--;n>1?a[r++]=65533:o<65536?a[r++]=o:(o-=65536,a[r++]=55296|o>>10&1023,a[r++]=56320|1023&o)}return a.length!==r&&(a.subarray?a=a.subarray(0,r):a.length=r),i.applyFromCharCode(a)};r.utf8encode=function(e){return s.nodebuffer?a.newBuffer(e,"utf-8"):c(e)},r.utf8decode=function(e){return s.nodebuffer?i.transformTo("nodebuffer",e).toString("utf-8"):(e=i.transformTo(s.uint8array?"uint8array":"array",e),h(e))},i.inherits(o,u),o.prototype.processChunk=function(e){var t=i.transformTo(s.uint8array?"uint8array":"array",e.data);if(this.leftOver&&this.leftOver.length){if(s.uint8array){var o=t;t=new Uint8Array(o.length+this.leftOver.length),t.set(this.leftOver,0),t.set(o,this.leftOver.length)}else t=this.leftOver.concat(t);this.leftOver=null}var n=l(t),a=t;n!==t.length&&(s.uint8array?(a=t.subarray(0,n),this.leftOver=t.subarray(n,t.length)):(a=t.slice(0,n),this.leftOver=t.slice(n,t.length))),this.push({data:r.utf8decode(a),meta:e.meta})},o.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:r.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},r.Utf8DecodeWorker=o,i.inherits(n,u),n.prototype.processChunk=function(e){this.push({data:r.utf8encode(e.data),meta:e.meta})},r.Utf8EncodeWorker=n},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(e,t,r){"use strict";function o(e){var t=null;return t=u.uint8array?new Uint8Array(e.length):new Array(e.length),i(e,t)}function n(e){return e}function i(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}function s(e){var t=65536,o=r.getTypeOf(e),n=!0;if("uint8array"===o?n=h.applyCanBeUsed.uint8array:"nodebuffer"===o&&(n=h.applyCanBeUsed.nodebuffer),n)for(;t>1;)try{return h.stringifyByChunk(e,o,t)}catch(i){t=Math.floor(t/2)}return h.stringifyByChar(e)}function a(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}var u=e("./support"),p=e("./base64"),g=e("./nodejsUtils"),c=e("core-js/library/fn/set-immediate"),l=e("./external");r.newBlob=function(e,t){r.checkSupport("blob");try{return new Blob(e,{type:t})}catch(o){try{for(var n=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,i=new n,s=0;s<e.length;s++)i.append(e[s]);return i.getBlob(t)}catch(o){throw new Error("Bug : can't construct the Blob.")}}};var h={stringifyByChunk:function(e,t,r){var o=[],n=0,i=e.length;if(i<=r)return String.fromCharCode.apply(null,e);for(;n<i;)"array"===t||"nodebuffer"===t?o.push(String.fromCharCode.apply(null,e.slice(n,Math.min(n+r,i)))):o.push(String.fromCharCode.apply(null,e.subarray(n,Math.min(n+r,i)))),n+=r;return o.join("")},stringifyByChar:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return u.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return u.nodebuffer&&1===String.fromCharCode.apply(null,g.newBuffer(1)).length}catch(e){return!1}}()}};r.applyFromCharCode=s;var f={};f.string={string:n,array:function(e){return i(e,new Array(e.length))},arraybuffer:function(e){return f.string.uint8array(e).buffer},uint8array:function(e){return i(e,new Uint8Array(e.length))},nodebuffer:function(e){return i(e,g.newBuffer(e.length))}},f.array={string:s,array:n,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return g.newBuffer(e)}},f.arraybuffer={string:function(e){return s(new Uint8Array(e))},array:function(e){return a(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:n,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return g.newBuffer(new Uint8Array(e))}},f.uint8array={string:s,array:function(e){return a(e,new Array(e.length))},arraybuffer:function(e){var t=new Uint8Array(e.length);return e.length&&t.set(e,0),t.buffer},uint8array:n,nodebuffer:function(e){return g.newBuffer(e)}},f.nodebuffer={string:s,array:function(e){return a(e,new Array(e.length))},arraybuffer:function(e){return f.nodebuffer.uint8array(e).buffer},uint8array:function(e){return a(e,new Uint8Array(e.length))},nodebuffer:n},r.transformTo=function(e,t){if(t||(t=""),!e)return t;r.checkSupport(e);var o=r.getTypeOf(t),n=f[o][e](t);return n},r.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":u.nodebuffer&&g.isBuffer(e)?"nodebuffer":u.uint8array&&e instanceof Uint8Array?"uint8array":u.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(e){var t=u[e.toLowerCase()];if(!t)throw new Error(e+" is not supported by this platform")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(e){var t,r,o="";for(r=0;r<(e||"").length;r++)t=e.charCodeAt(r),o+="\\x"+(t<16?"0":"")+t.toString(16).toUpperCase();return o},r.delay=function(e,t,r){c(function(){e.apply(r||null,t||[])})},r.inherits=function(e,t){var r=function(){};r.prototype=t.prototype,e.prototype=new r},r.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])arguments[e].hasOwnProperty(t)&&"undefined"==typeof r[t]&&(r[t]=arguments[e][t]);return r},r.prepareContent=function(e,t,n,i,s){var a=l.Promise.resolve(t).then(function(e){var t=u.blob&&(e instanceof Blob||["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(e))!==-1);return t&&"undefined"!=typeof FileReader?new l.Promise(function(t,r){var o=new FileReader;o.onload=function(e){t(e.target.result)},o.onerror=function(e){r(e.target.error)},o.readAsArrayBuffer(e)}):e});return a.then(function(t){var a=r.getTypeOf(t);return a?("arraybuffer"===a?t=r.transformTo("uint8array",t):"string"===a&&(s?t=p.decode(t):n&&i!==!0&&(t=o(t))),t):l.Promise.reject(new Error("The data of '"+e+"' is in an unsupported format !"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,"core-js/library/fn/set-immediate":36}],33:[function(e,t,r){"use strict";function o(e){this.files=[],this.loadOptions=e}var n=e("./reader/readerFor"),i=e("./utils"),s=e("./signature"),a=e("./zipEntry"),u=(e("./utf8"),e("./support"));o.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error("Corrupted zip or bug : unexpected signature ("+i.pretty(t)+", expected "+i.pretty(e)+")")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var o=this.reader.readString(4),n=o===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),t=u.uint8array?"uint8array":"array",r=i.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,o=this.zip64EndOfCentralSize-44,n=0;n<o;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)e=new a({zip64:this.zip64},this.loadOptions),e.readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END);if(e<0){var t=!this.isSignature(0,s.LOCAL_FILE_HEADER);throw t?new Error("Can't find end of central directory : is this a zip file ? If it is, see http://stuk.github.io/jszip/documentation/howto/read_zip.html"):new Error("Corrupted zip : can't find end of central directory")}this.reader.setIndex(e);var r=e;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,e=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),e<0)throw new Error("Corrupted zip : can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip : can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var o=this.centralDirOffset+this.centralDirSize;this.zip64&&(o+=20,o+=12+this.zip64EndOfCentralSize);var n=r-o;if(n>0)this.isSignature(r,s.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(e){this.reader=n(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=o},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utf8":31,"./utils":32,"./zipEntry":34}],34:[function(e,t,r){"use strict";function o(e,t){this.options=e,this.loadOptions=t}var n=e("./reader/readerFor"),i=e("./utils"),s=e("./compressedObject"),a=e("./crc32"),u=e("./utf8"),p=e("./compressions"),g=e("./support"),c=0,l=3,h=function(e){for(var t in p)if(p.hasOwnProperty(t)&&p[t].magic===e)return p[t];return null};o.prototype={isEncrypted:function(){return 1===(1&this.bitFlag)},useUTF8:function(){return 2048===(2048&this.bitFlag)},readLocalPart:function(e){var t,r;if(e.skip(22),this.fileNameLength=e.readInt(2),r=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(r),this.compressedSize===-1||this.uncompressedSize===-1)throw new Error("Bug or corrupted zip : didn't get enough informations from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(t=h(this.compressionMethod),null===t)throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new s(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),e===c&&(this.dosPermissions=63&this.externalFileAttributes),e===l&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(e){if(this.extraFields[1]){var t=n(this.extraFields[1].value);this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=t.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=t.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=t.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=t.readInt(4))}},readExtraFields:function(e){var t,r,o,n=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index<n;)t=e.readInt(2),r=e.readInt(2),o=e.readData(r),this.extraFields[t]={id:t,length:r,value:o}},handleUTF8:function(){var e=g.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=u.utf8decode(this.fileName),this.fileCommentStr=u.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var r=i.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var o=this.findExtraFieldUnicodeComment();if(null!==o)this.fileCommentStr=o;else{var n=i.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(n)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=n(e.value);return 1!==t.readInt(1)?null:a(this.fileName)!==t.readInt(4)?null:u.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=n(e.value);return 1!==t.readInt(1)?null:a(this.fileComment)!==t.readInt(4)?null:u.utf8decode(t.readData(e.length-5))}return null}},t.exports=o},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(e,t,r){"use strict";var o=e("./stream/StreamHelper"),n=e("./stream/DataWorker"),i=e("./utf8"),s=e("./compressedObject"),a=e("./stream/GenericWorker"),u=function(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}};u.prototype={internalStream:function(e){var t=e.toLowerCase(),r="string"===t||"text"===t;"binarystring"!==t&&"text"!==t||(t="string");var n=this._decompressWorker(),s=!this._dataBinary;return s&&!r&&(n=n.pipe(new i.Utf8EncodeWorker)),!s&&r&&(n=n.pipe(new i.Utf8DecodeWorker)),new o(n,t,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof s&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new i.Utf8EncodeWorker)),s.createWorkerFrom(r,e,t)},_decompressWorker:function(){return this._data instanceof s?this._data.getContentWorker():this._data instanceof a?this._data:new n(this._data)}};for(var p=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],g=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},c=0;c<p.length;c++)u.prototype[p[c]]=g;t.exports=u},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(e,t,r){e("../modules/web.immediate"),t.exports=e("../modules/_core").setImmediate},{"../modules/_core":40,"../modules/web.immediate":56}],37:[function(e,t,r){t.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},{}],38:[function(e,t,r){var o=e("./_is-object");t.exports=function(e){if(!o(e))throw TypeError(e+" is not an object!");return e}},{"./_is-object":51}],39:[function(e,t,r){var o={}.toString;t.exports=function(e){return o.call(e).slice(8,-1)}},{}],40:[function(e,t,r){var o=t.exports={version:"2.3.0"};"number"==typeof __e&&(__e=o)},{}],41:[function(e,t,r){var o=e("./_a-function");t.exports=function(e,t,r){if(o(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,o){return e.call(t,r,o)};case 3:return function(r,o,n){return e.call(t,r,o,n)}}return function(){return e.apply(t,arguments)}}},{"./_a-function":37}],42:[function(e,t,r){t.exports=!e("./_fails")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},{"./_fails":45}],43:[function(e,t,r){var o=e("./_is-object"),n=e("./_global").document,i=o(n)&&o(n.createElement);t.exports=function(e){return i?n.createElement(e):{}}},{"./_global":46,"./_is-object":51}],44:[function(e,t,r){var o=e("./_global"),n=e("./_core"),i=e("./_ctx"),s=e("./_hide"),a="prototype",u=function(e,t,r){var p,g,c,l=e&u.F,h=e&u.G,f=e&u.S,d=e&u.P,y=e&u.B,_=e&u.W,m=h?n:n[t]||(n[t]={}),b=m[a],v=h?o:f?o[t]:(o[t]||{})[a];h&&(r=t);for(p in r)g=!l&&v&&void 0!==v[p],g&&p in m||(c=g?v[p]:r[p],m[p]=h&&"function"!=typeof v[p]?r[p]:y&&g?i(c,o):_&&v[p]==c?function(e){var t=function(t,r,o){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,r)}return new e(t,r,o)}return e.apply(this,arguments)};return t[a]=e[a],t}(c):d&&"function"==typeof c?i(Function.call,c):c,d&&((m.virtual||(m.virtual={}))[p]=c,e&u.R&&b&&!b[p]&&s(b,p,c)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,t.exports=u},{"./_core":40,"./_ctx":41,"./_global":46,"./_hide":47}],45:[function(e,t,r){t.exports=function(e){try{return!!e()}catch(t){return!0}}},{}],46:[function(e,t,r){var o=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=o)},{}],47:[function(e,t,r){var o=e("./_object-dp"),n=e("./_property-desc");t.exports=e("./_descriptors")?function(e,t,r){return o.f(e,t,n(1,r))}:function(e,t,r){return e[t]=r,e}},{"./_descriptors":42,"./_object-dp":52,"./_property-desc":53}],48:[function(e,t,r){t.exports=e("./_global").document&&document.documentElement},{"./_global":46}],49:[function(e,t,r){t.exports=!e("./_descriptors")&&!e("./_fails")(function(){return 7!=Object.defineProperty(e("./_dom-create")("div"),"a",{get:function(){return 7}}).a})},{"./_descriptors":42,"./_dom-create":43,"./_fails":45}],50:[function(e,t,r){t.exports=function(e,t,r){var o=void 0===r;switch(t.length){case 0:return o?e():e.call(r);case 1:return o?e(t[0]):e.call(r,t[0]);case 2:return o?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return o?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return o?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},{}],51:[function(e,t,r){t.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},{}],52:[function(e,t,r){var o=e("./_an-object"),n=e("./_ie8-dom-define"),i=e("./_to-primitive"),s=Object.defineProperty;r.f=e("./_descriptors")?Object.defineProperty:function(e,t,r){if(o(e),t=i(t,!0),o(r),n)try{return s(e,t,r)}catch(a){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},{"./_an-object":38,"./_descriptors":42,"./_ie8-dom-define":49,"./_to-primitive":55}],53:[function(e,t,r){t.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},{}],54:[function(e,t,r){var o,n,i,s=e("./_ctx"),a=e("./_invoke"),u=e("./_html"),p=e("./_dom-create"),g=e("./_global"),c=g.process,l=g.setImmediate,h=g.clearImmediate,f=g.MessageChannel,d=0,y={},_="onreadystatechange",m=function(){
var e=+this;if(y.hasOwnProperty(e)){var t=y[e];delete y[e],t()}},b=function(e){m.call(e.data)};l&&h||(l=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return y[++d]=function(){a("function"==typeof e?e:Function(e),t)},o(d),d},h=function(e){delete y[e]},"process"==e("./_cof")(c)?o=function(e){c.nextTick(s(m,e,1))}:f?(n=new f,i=n.port2,n.port1.onmessage=b,o=s(i.postMessage,i,1)):g.addEventListener&&"function"==typeof postMessage&&!g.importScripts?(o=function(e){g.postMessage(e+"","*")},g.addEventListener("message",b,!1)):o=_ in p("script")?function(e){u.appendChild(p("script"))[_]=function(){u.removeChild(this),m.call(e)}}:function(e){setTimeout(s(m,e,1),0)}),t.exports={set:l,clear:h}},{"./_cof":39,"./_ctx":41,"./_dom-create":43,"./_global":46,"./_html":48,"./_invoke":50}],55:[function(e,t,r){var o=e("./_is-object");t.exports=function(e,t){if(!o(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!o(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!o(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!o(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")}},{"./_is-object":51}],56:[function(e,t,r){var o=e("./_export"),n=e("./_task");o(o.G+o.B,{setImmediate:n.set,clearImmediate:n.clear})},{"./_export":44,"./_task":54}],57:[function(e,t,o){(function(e){"use strict";function r(){g=!0;for(var e,t,r=c.length;r;){for(t=c,c=[],e=-1;++e<r;)t[e]();r=c.length}g=!1}function o(e){1!==c.push(e)||g||n()}var n,i=e.MutationObserver||e.WebKitMutationObserver;if(i){var s=0,a=new i(r),u=e.document.createTextNode("");a.observe(u,{characterData:!0}),n=function(){u.data=s=++s%2}}else if(e.setImmediate||"undefined"==typeof e.MessageChannel)n="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){r(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(r,0)};else{var p=new e.MessageChannel;p.port1.onmessage=r,n=function(){p.port2.postMessage(0)}}var g,c=[];t.exports=o}).call(this,"undefined"!=typeof r?r:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],58:[function(e,t,r){"use strict";function o(){}function n(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=m,this.queue=[],this.outcome=void 0,e!==o&&u(this,e)}function i(e,t,r){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function s(e,t,r){f(function(){var o;try{o=t(r)}catch(n){return d.reject(e,n)}o===e?d.reject(e,new TypeError("Cannot resolve promise with itself")):d.resolve(e,o)})}function a(e){var t=e&&e.then;if(e&&"object"==typeof e&&"function"==typeof t)return function(){t.apply(e,arguments)}}function u(e,t){function r(t){i||(i=!0,d.reject(e,t))}function o(t){i||(i=!0,d.resolve(e,t))}function n(){t(o,r)}var i=!1,s=p(n);"error"===s.status&&r(s.value)}function p(e,t){var r={};try{r.value=e(t),r.status="success"}catch(o){r.status="error",r.value=o}return r}function g(e){return e instanceof this?e:d.resolve(new this(o),e)}function c(e){var t=new this(o);return d.reject(t,e)}function l(e){function t(e,t){function o(e){s[t]=e,++a!==n||i||(i=!0,d.resolve(p,s))}r.resolve(e).then(o,function(e){i||(i=!0,d.reject(p,e))})}var r=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var n=e.length,i=!1;if(!n)return this.resolve([]);for(var s=new Array(n),a=0,u=-1,p=new this(o);++u<n;)t(e[u],u);return p}function h(e){function t(e){r.resolve(e).then(function(e){i||(i=!0,d.resolve(a,e))},function(e){i||(i=!0,d.reject(a,e))})}var r=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var n=e.length,i=!1;if(!n)return this.resolve([]);for(var s=-1,a=new this(o);++s<n;)t(e[s]);return a}var f=e("immediate"),d={},y=["REJECTED"],_=["FULFILLED"],m=["PENDING"];t.exports=n,n.prototype["catch"]=function(e){return this.then(null,e)},n.prototype.then=function(e,t){if("function"!=typeof e&&this.state===_||"function"!=typeof t&&this.state===y)return this;var r=new this.constructor(o);if(this.state!==m){var n=this.state===_?e:t;s(r,n,this.outcome)}else this.queue.push(new i(r,e,t));return r},i.prototype.callFulfilled=function(e){d.resolve(this.promise,e)},i.prototype.otherCallFulfilled=function(e){s(this.promise,this.onFulfilled,e)},i.prototype.callRejected=function(e){d.reject(this.promise,e)},i.prototype.otherCallRejected=function(e){s(this.promise,this.onRejected,e)},d.resolve=function(e,t){var r=p(a,t);if("error"===r.status)return d.reject(e,r.value);var o=r.value;if(o)u(e,o);else{e.state=_,e.outcome=t;for(var n=-1,i=e.queue.length;++n<i;)e.queue[n].callFulfilled(t)}return e},d.reject=function(e,t){e.state=y,e.outcome=t;for(var r=-1,o=e.queue.length;++r<o;)e.queue[r].callRejected(t);return e},n.resolve=g,n.reject=c,n.all=l,n.race=h},{immediate:57}],59:[function(e,t,r){"use strict";var o=e("./lib/utils/common").assign,n=e("./lib/deflate"),i=e("./lib/inflate"),s=e("./lib/zlib/constants"),a={};o(a,n,i,s),t.exports=a},{"./lib/deflate":60,"./lib/inflate":61,"./lib/utils/common":62,"./lib/zlib/constants":65}],60:[function(e,t,r){"use strict";function o(e){if(!(this instanceof o))return new o(e);this.options=u.assign({level:m,method:v,chunkSize:16384,windowBits:15,memLevel:8,strategy:b,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new c,this.strm.avail_out=0;var r=a.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==d)throw new Error(g[r]);if(t.header&&a.deflateSetHeader(this.strm,t.header),t.dictionary){var n;if(n="string"==typeof t.dictionary?p.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,r=a.deflateSetDictionary(this.strm,n),r!==d)throw new Error(g[r]);this._dict_set=!0}}function n(e,t){var r=new o(t);if(r.push(e,!0),r.err)throw r.msg;return r.result}function i(e,t){return t=t||{},t.raw=!0,n(e,t)}function s(e,t){return t=t||{},t.gzip=!0,n(e,t)}var a=e("./zlib/deflate"),u=e("./utils/common"),p=e("./utils/strings"),g=e("./zlib/messages"),c=e("./zlib/zstream"),l=Object.prototype.toString,h=0,f=4,d=0,y=1,_=2,m=-1,b=0,v=8;o.prototype.push=function(e,t){var r,o,n=this.strm,i=this.options.chunkSize;if(this.ended)return!1;o=t===~~t?t:t===!0?f:h,"string"==typeof e?n.input=p.string2buf(e):"[object ArrayBuffer]"===l.call(e)?n.input=new Uint8Array(e):n.input=e,n.next_in=0,n.avail_in=n.input.length;do{if(0===n.avail_out&&(n.output=new u.Buf8(i),n.next_out=0,n.avail_out=i),r=a.deflate(n,o),r!==y&&r!==d)return this.onEnd(r),this.ended=!0,!1;0!==n.avail_out&&(0!==n.avail_in||o!==f&&o!==_)||("string"===this.options.to?this.onData(p.buf2binstring(u.shrinkBuf(n.output,n.next_out))):this.onData(u.shrinkBuf(n.output,n.next_out)))}while((n.avail_in>0||0===n.avail_out)&&r!==y);return o===f?(r=a.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===d):o!==_||(this.onEnd(d),n.avail_out=0,!0)},o.prototype.onData=function(e){this.chunks.push(e)},o.prototype.onEnd=function(e){e===d&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=u.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=o,r.deflate=n,r.deflateRaw=i,r.gzip=s},{"./utils/common":62,"./utils/strings":63,"./zlib/deflate":67,"./zlib/messages":72,"./zlib/zstream":74}],61:[function(e,t,r){"use strict";function o(e){if(!(this instanceof o))return new o(e);this.options=a.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0===(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new c,this.strm.avail_out=0;var r=s.inflateInit2(this.strm,t.windowBits);if(r!==p.Z_OK)throw new Error(g[r]);this.header=new l,s.inflateGetHeader(this.strm,this.header)}function n(e,t){var r=new o(t);if(r.push(e,!0),r.err)throw r.msg;return r.result}function i(e,t){return t=t||{},t.raw=!0,n(e,t)}var s=e("./zlib/inflate"),a=e("./utils/common"),u=e("./utils/strings"),p=e("./zlib/constants"),g=e("./zlib/messages"),c=e("./zlib/zstream"),l=e("./zlib/gzheader"),h=Object.prototype.toString;o.prototype.push=function(e,t){var r,o,n,i,g,c,l=this.strm,f=this.options.chunkSize,d=this.options.dictionary,y=!1;if(this.ended)return!1;o=t===~~t?t:t===!0?p.Z_FINISH:p.Z_NO_FLUSH,"string"==typeof e?l.input=u.binstring2buf(e):"[object ArrayBuffer]"===h.call(e)?l.input=new Uint8Array(e):l.input=e,l.next_in=0,l.avail_in=l.input.length;do{if(0===l.avail_out&&(l.output=new a.Buf8(f),l.next_out=0,l.avail_out=f),r=s.inflate(l,p.Z_NO_FLUSH),r===p.Z_NEED_DICT&&d&&(c="string"==typeof d?u.string2buf(d):"[object ArrayBuffer]"===h.call(d)?new Uint8Array(d):d,r=s.inflateSetDictionary(this.strm,c)),r===p.Z_BUF_ERROR&&y===!0&&(r=p.Z_OK,y=!1),r!==p.Z_STREAM_END&&r!==p.Z_OK)return this.onEnd(r),this.ended=!0,!1;l.next_out&&(0!==l.avail_out&&r!==p.Z_STREAM_END&&(0!==l.avail_in||o!==p.Z_FINISH&&o!==p.Z_SYNC_FLUSH)||("string"===this.options.to?(n=u.utf8border(l.output,l.next_out),i=l.next_out-n,g=u.buf2string(l.output,n),l.next_out=i,l.avail_out=f-i,i&&a.arraySet(l.output,l.output,n,i,0),this.onData(g)):this.onData(a.shrinkBuf(l.output,l.next_out)))),0===l.avail_in&&0===l.avail_out&&(y=!0)}while((l.avail_in>0||0===l.avail_out)&&r!==p.Z_STREAM_END);return r===p.Z_STREAM_END&&(o=p.Z_FINISH),o===p.Z_FINISH?(r=s.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===p.Z_OK):o!==p.Z_SYNC_FLUSH||(this.onEnd(p.Z_OK),l.avail_out=0,!0)},o.prototype.onData=function(e){this.chunks.push(e)},o.prototype.onEnd=function(e){e===p.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=a.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=o,r.inflate=n,r.inflateRaw=i,r.ungzip=n},{"./utils/common":62,"./utils/strings":63,"./zlib/constants":65,"./zlib/gzheader":68,"./zlib/inflate":70,"./zlib/messages":72,"./zlib/zstream":74}],62:[function(e,t,r){"use strict";var o="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var o in r)r.hasOwnProperty(o)&&(e[o]=r[o])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var n={arraySet:function(e,t,r,o,n){if(t.subarray&&e.subarray)return void e.set(t.subarray(r,r+o),n);for(var i=0;i<o;i++)e[n+i]=t[r+i]},flattenChunks:function(e){var t,r,o,n,i,s;for(o=0,t=0,r=e.length;t<r;t++)o+=e[t].length;for(s=new Uint8Array(o),n=0,t=0,r=e.length;t<r;t++)i=e[t],s.set(i,n),n+=i.length;return s}},i={arraySet:function(e,t,r,o,n){for(var i=0;i<o;i++)e[n+i]=t[r+i]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,n)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,i))},r.setTyped(o)},{}],63:[function(e,t,r){"use strict";function o(e,t){if(t<65537&&(e.subarray&&s||!e.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",o=0;o<t;o++)r+=String.fromCharCode(e[o]);return r}var n=e("./common"),i=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(a){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(a){s=!1}for(var u=new n.Buf8(256),p=0;p<256;p++)u[p]=p>=252?6:p>=248?5:p>=240?4:p>=224?3:p>=192?2:1;u[254]=u[254]=1,r.string2buf=function(e){var t,r,o,i,s,a=e.length,u=0;for(i=0;i<a;i++)r=e.charCodeAt(i),55296===(64512&r)&&i+1<a&&(o=e.charCodeAt(i+1),56320===(64512&o)&&(r=65536+(r-55296<<10)+(o-56320),i++)),u+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(u),s=0,i=0;s<u;i++)r=e.charCodeAt(i),55296===(64512&r)&&i+1<a&&(o=e.charCodeAt(i+1),56320===(64512&o)&&(r=65536+(r-55296<<10)+(o-56320),i++)),r<128?t[s++]=r:r<2048?(t[s++]=192|r>>>6,t[s++]=128|63&r):r<65536?(t[s++]=224|r>>>12,t[s++]=128|r>>>6&63,t[s++]=128|63&r):(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63,t[s++]=128|r>>>6&63,t[s++]=128|63&r);return t},r.buf2binstring=function(e){return o(e,e.length)},r.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,o=t.length;r<o;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,i,s,a=t||e.length,p=new Array(2*a);for(n=0,r=0;r<a;)if(i=e[r++],i<128)p[n++]=i;else if(s=u[i],s>4)p[n++]=65533,r+=s-1;else{for(i&=2===s?31:3===s?15:7;s>1&&r<a;)i=i<<6|63&e[r++],s--;s>1?p[n++]=65533:i<65536?p[n++]=i:(i-=65536,p[n++]=55296|i>>10&1023,p[n++]=56320|1023&i)}return o(p,n)},r.utf8border=function(e,t){var r;for(t=t||e.length,t>e.length&&(t=e.length),r=t-1;r>=0&&128===(192&e[r]);)r--;return r<0?t:0===r?t:r+u[e[r]]>t?r:t}},{"./common":62}],64:[function(e,t,r){"use strict";function o(e,t,r,o){for(var n=65535&e|0,i=e>>>16&65535|0,s=0;0!==r;){s=r>2e3?2e3:r,r-=s;do n=n+t[o++]|0,i=i+n|0;while(--s);n%=65521,i%=65521}return n|i<<16|0}t.exports=o},{}],65:[function(e,t,r){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],66:[function(e,t,r){"use strict";function o(){for(var e,t=[],r=0;r<256;r++){e=r;for(var o=0;o<8;o++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}function n(e,t,r,o){var n=i,s=o+r;e^=-1;for(var a=o;a<s;a++)e=e>>>8^n[255&(e^t[a])];return e^-1}var i=o();t.exports=n},{}],67:[function(e,t,r){"use strict";function o(e,t){return e.msg=k[t],t}function n(e){return(e<<1)-(e>4?9:0)}function i(e){for(var t=e.length;--t>=0;)e[t]=0}function s(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(O.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function a(e,t){x._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,s(e.strm)}function u(e,t){e.pending_buf[e.pending++]=t}function p(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function g(e,t,r,o){var n=e.avail_in;return n>o&&(n=o),0===n?0:(e.avail_in-=n,O.arraySet(t,e.input,e.next_in,n,r),1===e.state.wrap?e.adler=C(e.adler,t,n,r):2===e.state.wrap&&(e.adler=F(e.adler,t,n,r)),e.next_in+=n,e.total_in+=n,n)}function c(e,t){var r,o,n=e.max_chain_length,i=e.strstart,s=e.prev_length,a=e.nice_match,u=e.strstart>e.w_size-ce?e.strstart-(e.w_size-ce):0,p=e.window,g=e.w_mask,c=e.prev,l=e.strstart+ge,h=p[i+s-1],f=p[i+s];e.prev_length>=e.good_match&&(n>>=2),a>e.lookahead&&(a=e.lookahead);do if(r=t,p[r+s]===f&&p[r+s-1]===h&&p[r]===p[i]&&p[++r]===p[i+1]){i+=2,r++;do;while(p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&i<l);if(o=ge-(l-i),i=l-ge,o>s){if(e.match_start=t,s=o,o>=a)break;h=p[i+s-1],f=p[i+s]}}while((t=c[t&g])>u&&0!==--n);return s<=e.lookahead?s:e.lookahead}function l(e){var t,r,o,n,i,s=e.w_size;do{if(n=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-ce)){O.arraySet(e.window,e.window,s,s,0),e.match_start-=s,e.strstart-=s,e.block_start-=s,r=e.hash_size,t=r;do o=e.head[--t],e.head[t]=o>=s?o-s:0;while(--r);r=s,t=r;do o=e.prev[--t],e.prev[t]=o>=s?o-s:0;while(--r);n+=s}if(0===e.strm.avail_in)break;if(r=g(e.strm,e.window,e.strstart+e.lookahead,n),e.lookahead+=r,e.lookahead+e.insert>=pe)for(i=e.strstart-e.insert,e.ins_h=e.window[i],e.ins_h=(e.ins_h<<e.hash_shift^e.window[i+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[i+pe-1])&e.hash_mask,e.prev[i&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=i,i++,e.insert--,!(e.lookahead+e.insert<pe)););}while(e.lookahead<ce&&0!==e.strm.avail_in)}function h(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(l(e),0===e.lookahead&&t===R)return ve;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var o=e.block_start+r;if((0===e.strstart||e.strstart>=o)&&(e.lookahead=e.strstart-o,e.strstart=o,a(e,!1),0===e.strm.avail_out))return ve;if(e.strstart-e.block_start>=e.w_size-ce&&(a(e,!1),0===e.strm.avail_out))return ve}return e.insert=0,t===U?(a(e,!0),0===e.strm.avail_out?Ee:we):e.strstart>e.block_start&&(a(e,!1),0===e.strm.avail_out)?ve:ve}function f(e,t){for(var r,o;;){if(e.lookahead<ce){if(l(e),e.lookahead<ce&&t===R)return ve;if(0===e.lookahead)break}if(r=0,e.lookahead>=pe&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+pe-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-ce&&(e.match_length=c(e,r)),e.match_length>=pe)if(o=x._tr_tally(e,e.strstart-e.match_start,e.match_length-pe),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=pe){e.match_length--;do e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+pe-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(0!==--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else o=x._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(o&&(a(e,!1),0===e.strm.avail_out))return ve}return e.insert=e.strstart<pe-1?e.strstart:pe-1,t===U?(a(e,!0),0===e.strm.avail_out?Ee:we):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?ve:Se}function d(e,t){for(var r,o,n;;){if(e.lookahead<ce){if(l(e),e.lookahead<ce&&t===R)return ve;if(0===e.lookahead)break}if(r=0,e.lookahead>=pe&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+pe-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=pe-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-ce&&(e.match_length=c(e,r),e.match_length<=5&&(e.strategy===Y||e.match_length===pe&&e.strstart-e.match_start>4096)&&(e.match_length=pe-1)),e.prev_length>=pe&&e.match_length<=e.prev_length){n=e.strstart+e.lookahead-pe,o=x._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-pe),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=n&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+pe-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(0!==--e.prev_length);if(e.match_available=0,e.match_length=pe-1,e.strstart++,o&&(a(e,!1),0===e.strm.avail_out))return ve}else if(e.match_available){if(o=x._tr_tally(e,0,e.window[e.strstart-1]),o&&a(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return ve}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(o=x._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<pe-1?e.strstart:pe-1,t===U?(a(e,!0),0===e.strm.avail_out?Ee:we):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?ve:Se}function y(e,t){for(var r,o,n,i,s=e.window;;){if(e.lookahead<=ge){if(l(e),e.lookahead<=ge&&t===R)return ve;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=pe&&e.strstart>0&&(n=e.strstart-1,o=s[n],o===s[++n]&&o===s[++n]&&o===s[++n])){i=e.strstart+ge;do;while(o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&n<i);e.match_length=ge-(i-n),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=pe?(r=x._tr_tally(e,1,e.match_length-pe),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=x._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(a(e,!1),0===e.strm.avail_out))return ve}return e.insert=0,t===U?(a(e,!0),0===e.strm.avail_out?Ee:we):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?ve:Se}function _(e,t){for(var r;;){if(0===e.lookahead&&(l(e),0===e.lookahead)){if(t===R)return ve;break}if(e.match_length=0,r=x._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(a(e,!1),0===e.strm.avail_out))return ve}return e.insert=0,t===U?(a(e,!0),0===e.strm.avail_out?Ee:we):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?ve:Se}function m(e,t,r,o,n){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=o,this.func=n}function b(e){e.window_size=2*e.w_size,i(e.head),e.max_lazy_match=M[e.level].max_lazy,e.good_match=M[e.level].good_length,e.nice_match=M[e.level].nice_length,e.max_chain_length=M[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=pe-1,e.match_available=0,e.ins_h=0}function v(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=$,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new O.Buf16(2*ae),this.dyn_dtree=new O.Buf16(2*(2*ie+1)),this.bl_tree=new O.Buf16(2*(2*se+1)),i(this.dyn_ltree),i(this.dyn_dtree),i(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new O.Buf16(ue+1),this.heap=new O.Buf16(2*ne+1),i(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new O.Buf16(2*ne+1),i(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function S(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=J,t=e.state,t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?he:me,e.adler=2===t.wrap?0:1,t.last_flush=R,x._tr_init(t),P):o(e,z)}function E(e){var t=S(e);return t===P&&b(e.state),t}function w(e,t){return e&&e.state?2!==e.state.wrap?z:(e.state.gzhead=t,P):z}function A(e,t,r,n,i,s){if(!e)return z;var a=1;if(t===G&&(t=6),n<0?(a=0,n=-n):n>15&&(a=2,n-=16),i<1||i>Q||r!==$||n<8||n>15||t<0||t>9||s<0||s>K)return o(e,z);8===n&&(n=9);var u=new v;return e.state=u,u.strm=e,u.wrap=a,u.gzhead=null,u.w_bits=n,u.w_size=1<<u.w_bits,u.w_mask=u.w_size-1,u.hash_bits=i+7,u.hash_size=1<<u.hash_bits,u.hash_mask=u.hash_size-1,u.hash_shift=~~((u.hash_bits+pe-1)/pe),u.window=new O.Buf8(2*u.w_size),u.head=new O.Buf16(u.hash_size),u.prev=new O.Buf16(u.w_size),u.lit_bufsize=1<<i+6,u.pending_buf_size=4*u.lit_bufsize,u.pending_buf=new O.Buf8(u.pending_buf_size),u.d_buf=1*u.lit_bufsize,u.l_buf=3*u.lit_bufsize,u.level=t,u.strategy=s,u.method=r,E(e)}function j(e,t){return A(e,t,$,ee,te,q)}function T(e,t){var r,a,g,c;if(!e||!e.state||t>L||t<0)return e?o(e,z):z;if(a=e.state,!e.output||!e.input&&0!==e.avail_in||a.status===be&&t!==U)return o(e,0===e.avail_out?H:z);if(a.strm=e,r=a.last_flush,a.last_flush=t,a.status===he)if(2===a.wrap)e.adler=0,u(a,31),u(a,139),u(a,8),a.gzhead?(u(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),u(a,255&a.gzhead.time),u(a,a.gzhead.time>>8&255),u(a,a.gzhead.time>>16&255),u(a,a.gzhead.time>>24&255),u(a,9===a.level?2:a.strategy>=Z||a.level<2?4:0),u(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(u(a,255&a.gzhead.extra.length),u(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=F(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=fe):(u(a,0),u(a,0),u(a,0),u(a,0),u(a,0),u(a,9===a.level?2:a.strategy>=Z||a.level<2?4:0),u(a,Ae),a.status=me);else{var l=$+(a.w_bits-8<<4)<<8,h=-1;h=a.strategy>=Z||a.level<2?0:a.level<6?1:6===a.level?2:3,l|=h<<6,0!==a.strstart&&(l|=le),l+=31-l%31,a.status=me,p(a,l),0!==a.strstart&&(p(a,e.adler>>>16),p(a,65535&e.adler)),e.adler=1}if(a.status===fe)if(a.gzhead.extra){for(g=a.pending;a.gzindex<(65535&a.gzhead.extra.length)&&(a.pending!==a.pending_buf_size||(a.gzhead.hcrc&&a.pending>g&&(e.adler=F(e.adler,a.pending_buf,a.pending-g,g)),s(e),g=a.pending,a.pending!==a.pending_buf_size));)u(a,255&a.gzhead.extra[a.gzindex]),a.gzindex++;a.gzhead.hcrc&&a.pending>g&&(e.adler=F(e.adler,a.pending_buf,a.pending-g,g)),a.gzindex===a.gzhead.extra.length&&(a.gzindex=0,a.status=de)}else a.status=de;if(a.status===de)if(a.gzhead.name){g=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>g&&(e.adler=F(e.adler,a.pending_buf,a.pending-g,g)),s(e),g=a.pending,a.pending===a.pending_buf_size)){c=1;break}c=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,u(a,c)}while(0!==c);a.gzhead.hcrc&&a.pending>g&&(e.adler=F(e.adler,a.pending_buf,a.pending-g,g)),0===c&&(a.gzindex=0,a.status=ye)}else a.status=ye;if(a.status===ye)if(a.gzhead.comment){g=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>g&&(e.adler=F(e.adler,a.pending_buf,a.pending-g,g)),s(e),g=a.pending,a.pending===a.pending_buf_size)){c=1;break}c=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,u(a,c)}while(0!==c);a.gzhead.hcrc&&a.pending>g&&(e.adler=F(e.adler,a.pending_buf,a.pending-g,g)),0===c&&(a.status=_e)}else a.status=_e;if(a.status===_e&&(a.gzhead.hcrc?(a.pending+2>a.pending_buf_size&&s(e),a.pending+2<=a.pending_buf_size&&(u(a,255&e.adler),u(a,e.adler>>8&255),e.adler=0,a.status=me)):a.status=me),0!==a.pending){if(s(e),0===e.avail_out)return a.last_flush=-1,P}else if(0===e.avail_in&&n(t)<=n(r)&&t!==U)return o(e,H);if(a.status===be&&0!==e.avail_in)return o(e,H);if(0!==e.avail_in||0!==a.lookahead||t!==R&&a.status!==be){var f=a.strategy===Z?_(a,t):a.strategy===X?y(a,t):M[a.level].func(a,t);if(f!==Ee&&f!==we||(a.status=be),f===ve||f===Ee)return 0===e.avail_out&&(a.last_flush=-1),P;if(f===Se&&(t===D?x._tr_align(a):t!==L&&(x._tr_stored_block(a,0,0,!1),t===N&&(i(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),s(e),0===e.avail_out))return a.last_flush=-1,P}return t!==U?P:a.wrap<=0?W:(2===a.wrap?(u(a,255&e.adler),u(a,e.adler>>8&255),u(a,e.adler>>16&255),u(a,e.adler>>24&255),u(a,255&e.total_in),u(a,e.total_in>>8&255),u(a,e.total_in>>16&255),u(a,e.total_in>>24&255)):(p(a,e.adler>>>16),p(a,65535&e.adler)),s(e),a.wrap>0&&(a.wrap=-a.wrap),0!==a.pending?P:W)}function I(e){var t;return e&&e.state?(t=e.state.status,t!==he&&t!==fe&&t!==de&&t!==ye&&t!==_e&&t!==me&&t!==be?o(e,z):(e.state=null,t===me?o(e,V):P)):z}function B(e,t){var r,o,n,s,a,u,p,g,c=t.length;if(!e||!e.state)return z;if(r=e.state,s=r.wrap,2===s||1===s&&r.status!==he||r.lookahead)return z;for(1===s&&(e.adler=C(e.adler,t,c,0)),r.wrap=0,c>=r.w_size&&(0===s&&(i(r.head),r.strstart=0,r.block_start=0,r.insert=0),g=new O.Buf8(r.w_size),O.arraySet(g,t,c-r.w_size,r.w_size,0),t=g,c=r.w_size),a=e.avail_in,u=e.next_in,p=e.input,e.avail_in=c,e.next_in=0,e.input=t,l(r);r.lookahead>=pe;){o=r.strstart,n=r.lookahead-(pe-1);do r.ins_h=(r.ins_h<<r.hash_shift^r.window[o+pe-1])&r.hash_mask,r.prev[o&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=o,o++;while(--n);r.strstart=o,r.lookahead=pe-1,l(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=pe-1,r.match_available=0,e.next_in=u,e.input=p,e.avail_in=a,r.wrap=s,P}var M,O=e("../utils/common"),x=e("./trees"),C=e("./adler32"),F=e("./crc32"),k=e("./messages"),R=0,D=1,N=3,U=4,L=5,P=0,W=1,z=-2,V=-3,H=-5,G=-1,Y=1,Z=2,X=3,K=4,q=0,J=2,$=8,Q=9,ee=15,te=8,re=29,oe=256,ne=oe+1+re,ie=30,se=19,ae=2*ne+1,ue=15,pe=3,ge=258,ce=ge+pe+1,le=32,he=42,fe=69,de=73,ye=91,_e=103,me=113,be=666,ve=1,Se=2,Ee=3,we=4,Ae=3;M=[new m(0,0,0,0,h),new m(4,4,8,4,f),new m(4,5,16,8,f),new m(4,6,32,32,f),new m(4,4,16,16,d),new m(8,16,32,32,d),new m(8,16,128,128,d),new m(8,32,128,256,d),new m(32,128,258,1024,d),new m(32,258,258,4096,d)],r.deflateInit=j,r.deflateInit2=A,r.deflateReset=E,r.deflateResetKeep=S,r.deflateSetHeader=w,r.deflate=T,r.deflateEnd=I,r.deflateSetDictionary=B,r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":62,"./adler32":64,"./crc32":66,"./messages":72,"./trees":73}],68:[function(e,t,r){"use strict";function o(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}t.exports=o},{}],69:[function(e,t,r){"use strict";var o=30,n=12;t.exports=function(e,t){var r,i,s,a,u,p,g,c,l,h,f,d,y,_,m,b,v,S,E,w,A,j,T,I,B;r=e.state,i=e.next_in,I=e.input,s=i+(e.avail_in-5),a=e.next_out,B=e.output,u=a-(t-e.avail_out),p=a+(e.avail_out-257),g=r.dmax,c=r.wsize,l=r.whave,h=r.wnext,f=r.window,d=r.hold,y=r.bits,_=r.lencode,m=r.distcode,b=(1<<r.lenbits)-1,v=(1<<r.distbits)-1;e:do{y<15&&(d+=I[i++]<<y,y+=8,d+=I[i++]<<y,y+=8),S=_[d&b];t:for(;;){if(E=S>>>24,d>>>=E,y-=E,E=S>>>16&255,0===E)B[a++]=65535&S;else{if(!(16&E)){if(0===(64&E)){S=_[(65535&S)+(d&(1<<E)-1)];continue t}if(32&E){r.mode=n;break e}e.msg="invalid literal/length code",r.mode=o;break e}w=65535&S,E&=15,E&&(y<E&&(d+=I[i++]<<y,y+=8),w+=d&(1<<E)-1,d>>>=E,y-=E),y<15&&(d+=I[i++]<<y,y+=8,d+=I[i++]<<y,y+=8),S=m[d&v];r:for(;;){if(E=S>>>24,d>>>=E,y-=E,E=S>>>16&255,!(16&E)){if(0===(64&E)){S=m[(65535&S)+(d&(1<<E)-1)];continue r}e.msg="invalid distance code",r.mode=o;break e}if(A=65535&S,E&=15,y<E&&(d+=I[i++]<<y,y+=8,y<E&&(d+=I[i++]<<y,y+=8)),A+=d&(1<<E)-1,A>g){e.msg="invalid distance too far back",r.mode=o;break e}if(d>>>=E,y-=E,E=a-u,A>E){if(E=A-E,E>l&&r.sane){e.msg="invalid distance too far back",r.mode=o;break e}if(j=0,T=f,0===h){if(j+=c-E,E<w){w-=E;do B[a++]=f[j++];while(--E);j=a-A,T=B}}else if(h<E){if(j+=c+h-E,E-=h,E<w){w-=E;do B[a++]=f[j++];while(--E);if(j=0,h<w){E=h,w-=E;do B[a++]=f[j++];while(--E);j=a-A,T=B}}}else if(j+=h-E,E<w){w-=E;do B[a++]=f[j++];while(--E);j=a-A,T=B}for(;w>2;)B[a++]=T[j++],B[a++]=T[j++],B[a++]=T[j++],w-=3;w&&(B[a++]=T[j++],w>1&&(B[a++]=T[j++]))}else{j=a-A;do B[a++]=B[j++],B[a++]=B[j++],B[a++]=B[j++],w-=3;while(w>2);w&&(B[a++]=B[j++],w>1&&(B[a++]=B[j++]))}break}}break}}while(i<s&&a<p);w=y>>3,i-=w,y-=w<<3,d&=(1<<y)-1,e.next_in=i,e.next_out=a,e.avail_in=i<s?5+(s-i):5-(i-s),e.avail_out=a<p?257+(p-a):257-(a-p),r.hold=d,r.bits=y}},{}],70:[function(e,t,r){"use strict";function o(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function n(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new m.Buf16(320),this.work=new m.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function i(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=N,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new m.Buf32(de),t.distcode=t.distdyn=new m.Buf32(ye),t.sane=1,t.back=-1,M):C}function s(e){var t;return e&&e.state?(t=e.state,t.wsize=0,t.whave=0,t.wnext=0,i(e)):C}function a(e,t){var r,o;return e&&e.state?(o=e.state,t<0?(r=0,t=-t):(r=(t>>4)+1,t<48&&(t&=15)),t&&(t<8||t>15)?C:(null!==o.window&&o.wbits!==t&&(o.window=null),o.wrap=r,o.wbits=t,s(e))):C}function u(e,t){var r,o;return e?(o=new n,e.state=o,o.window=null,r=a(e,t),
r!==M&&(e.state=null),r):C}function p(e){return u(e,me)}function g(e){if(be){var t;for(y=new m.Buf32(512),_=new m.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(E(A,e.lens,0,288,y,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;E(j,e.lens,0,32,_,0,e.work,{bits:5}),be=!1}e.lencode=y,e.lenbits=9,e.distcode=_,e.distbits=5}function c(e,t,r,o){var n,i=e.state;return null===i.window&&(i.wsize=1<<i.wbits,i.wnext=0,i.whave=0,i.window=new m.Buf8(i.wsize)),o>=i.wsize?(m.arraySet(i.window,t,r-i.wsize,i.wsize,0),i.wnext=0,i.whave=i.wsize):(n=i.wsize-i.wnext,n>o&&(n=o),m.arraySet(i.window,t,r-o,n,i.wnext),o-=n,o?(m.arraySet(i.window,t,r-o,o,0),i.wnext=o,i.whave=i.wsize):(i.wnext+=n,i.wnext===i.wsize&&(i.wnext=0),i.whave<i.wsize&&(i.whave+=n))),0}function l(e,t){var r,n,i,s,a,u,p,l,h,f,d,y,_,de,ye,_e,me,be,ve,Se,Ee,we,Ae,je,Te=0,Ie=new m.Buf8(4),Be=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return C;r=e.state,r.mode===X&&(r.mode=K),a=e.next_out,i=e.output,p=e.avail_out,s=e.next_in,n=e.input,u=e.avail_in,l=r.hold,h=r.bits,f=u,d=p,we=M;e:for(;;)switch(r.mode){case N:if(0===r.wrap){r.mode=K;break}for(;h<16;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(2&r.wrap&&35615===l){r.check=0,Ie[0]=255&l,Ie[1]=l>>>8&255,r.check=v(r.check,Ie,2,0),l=0,h=0,r.mode=U;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&l)<<8)+(l>>8))%31){e.msg="incorrect header check",r.mode=le;break}if((15&l)!==D){e.msg="unknown compression method",r.mode=le;break}if(l>>>=4,h-=4,Ee=(15&l)+8,0===r.wbits)r.wbits=Ee;else if(Ee>r.wbits){e.msg="invalid window size",r.mode=le;break}r.dmax=1<<Ee,e.adler=r.check=1,r.mode=512&l?Y:X,l=0,h=0;break;case U:for(;h<16;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(r.flags=l,(255&r.flags)!==D){e.msg="unknown compression method",r.mode=le;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=le;break}r.head&&(r.head.text=l>>8&1),512&r.flags&&(Ie[0]=255&l,Ie[1]=l>>>8&255,r.check=v(r.check,Ie,2,0)),l=0,h=0,r.mode=L;case L:for(;h<32;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}r.head&&(r.head.time=l),512&r.flags&&(Ie[0]=255&l,Ie[1]=l>>>8&255,Ie[2]=l>>>16&255,Ie[3]=l>>>24&255,r.check=v(r.check,Ie,4,0)),l=0,h=0,r.mode=P;case P:for(;h<16;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}r.head&&(r.head.xflags=255&l,r.head.os=l>>8),512&r.flags&&(Ie[0]=255&l,Ie[1]=l>>>8&255,r.check=v(r.check,Ie,2,0)),l=0,h=0,r.mode=W;case W:if(1024&r.flags){for(;h<16;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}r.length=l,r.head&&(r.head.extra_len=l),512&r.flags&&(Ie[0]=255&l,Ie[1]=l>>>8&255,r.check=v(r.check,Ie,2,0)),l=0,h=0}else r.head&&(r.head.extra=null);r.mode=z;case z:if(1024&r.flags&&(y=r.length,y>u&&(y=u),y&&(r.head&&(Ee=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),m.arraySet(r.head.extra,n,s,y,Ee)),512&r.flags&&(r.check=v(r.check,n,y,s)),u-=y,s+=y,r.length-=y),r.length))break e;r.length=0,r.mode=V;case V:if(2048&r.flags){if(0===u)break e;y=0;do Ee=n[s+y++],r.head&&Ee&&r.length<65536&&(r.head.name+=String.fromCharCode(Ee));while(Ee&&y<u);if(512&r.flags&&(r.check=v(r.check,n,y,s)),u-=y,s+=y,Ee)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=H;case H:if(4096&r.flags){if(0===u)break e;y=0;do Ee=n[s+y++],r.head&&Ee&&r.length<65536&&(r.head.comment+=String.fromCharCode(Ee));while(Ee&&y<u);if(512&r.flags&&(r.check=v(r.check,n,y,s)),u-=y,s+=y,Ee)break e}else r.head&&(r.head.comment=null);r.mode=G;case G:if(512&r.flags){for(;h<16;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(l!==(65535&r.check)){e.msg="header crc mismatch",r.mode=le;break}l=0,h=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=X;break;case Y:for(;h<32;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}e.adler=r.check=o(l),l=0,h=0,r.mode=Z;case Z:if(0===r.havedict)return e.next_out=a,e.avail_out=p,e.next_in=s,e.avail_in=u,r.hold=l,r.bits=h,x;e.adler=r.check=1,r.mode=X;case X:if(t===I||t===B)break e;case K:if(r.last){l>>>=7&h,h-=7&h,r.mode=pe;break}for(;h<3;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}switch(r.last=1&l,l>>>=1,h-=1,3&l){case 0:r.mode=q;break;case 1:if(g(r),r.mode=re,t===B){l>>>=2,h-=2;break e}break;case 2:r.mode=Q;break;case 3:e.msg="invalid block type",r.mode=le}l>>>=2,h-=2;break;case q:for(l>>>=7&h,h-=7&h;h<32;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if((65535&l)!==(l>>>16^65535)){e.msg="invalid stored block lengths",r.mode=le;break}if(r.length=65535&l,l=0,h=0,r.mode=J,t===B)break e;case J:r.mode=$;case $:if(y=r.length){if(y>u&&(y=u),y>p&&(y=p),0===y)break e;m.arraySet(i,n,s,y,a),u-=y,s+=y,p-=y,a+=y,r.length-=y;break}r.mode=X;break;case Q:for(;h<14;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(r.nlen=(31&l)+257,l>>>=5,h-=5,r.ndist=(31&l)+1,l>>>=5,h-=5,r.ncode=(15&l)+4,l>>>=4,h-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=le;break}r.have=0,r.mode=ee;case ee:for(;r.have<r.ncode;){for(;h<3;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}r.lens[Be[r.have++]]=7&l,l>>>=3,h-=3}for(;r.have<19;)r.lens[Be[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,Ae={bits:r.lenbits},we=E(w,r.lens,0,19,r.lencode,0,r.work,Ae),r.lenbits=Ae.bits,we){e.msg="invalid code lengths set",r.mode=le;break}r.have=0,r.mode=te;case te:for(;r.have<r.nlen+r.ndist;){for(;Te=r.lencode[l&(1<<r.lenbits)-1],ye=Te>>>24,_e=Te>>>16&255,me=65535&Te,!(ye<=h);){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(me<16)l>>>=ye,h-=ye,r.lens[r.have++]=me;else{if(16===me){for(je=ye+2;h<je;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(l>>>=ye,h-=ye,0===r.have){e.msg="invalid bit length repeat",r.mode=le;break}Ee=r.lens[r.have-1],y=3+(3&l),l>>>=2,h-=2}else if(17===me){for(je=ye+3;h<je;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}l>>>=ye,h-=ye,Ee=0,y=3+(7&l),l>>>=3,h-=3}else{for(je=ye+7;h<je;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}l>>>=ye,h-=ye,Ee=0,y=11+(127&l),l>>>=7,h-=7}if(r.have+y>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=le;break}for(;y--;)r.lens[r.have++]=Ee}}if(r.mode===le)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=le;break}if(r.lenbits=9,Ae={bits:r.lenbits},we=E(A,r.lens,0,r.nlen,r.lencode,0,r.work,Ae),r.lenbits=Ae.bits,we){e.msg="invalid literal/lengths set",r.mode=le;break}if(r.distbits=6,r.distcode=r.distdyn,Ae={bits:r.distbits},we=E(j,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,Ae),r.distbits=Ae.bits,we){e.msg="invalid distances set",r.mode=le;break}if(r.mode=re,t===B)break e;case re:r.mode=oe;case oe:if(u>=6&&p>=258){e.next_out=a,e.avail_out=p,e.next_in=s,e.avail_in=u,r.hold=l,r.bits=h,S(e,d),a=e.next_out,i=e.output,p=e.avail_out,s=e.next_in,n=e.input,u=e.avail_in,l=r.hold,h=r.bits,r.mode===X&&(r.back=-1);break}for(r.back=0;Te=r.lencode[l&(1<<r.lenbits)-1],ye=Te>>>24,_e=Te>>>16&255,me=65535&Te,!(ye<=h);){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(_e&&0===(240&_e)){for(be=ye,ve=_e,Se=me;Te=r.lencode[Se+((l&(1<<be+ve)-1)>>be)],ye=Te>>>24,_e=Te>>>16&255,me=65535&Te,!(be+ye<=h);){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}l>>>=be,h-=be,r.back+=be}if(l>>>=ye,h-=ye,r.back+=ye,r.length=me,0===_e){r.mode=ue;break}if(32&_e){r.back=-1,r.mode=X;break}if(64&_e){e.msg="invalid literal/length code",r.mode=le;break}r.extra=15&_e,r.mode=ne;case ne:if(r.extra){for(je=r.extra;h<je;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}r.length+=l&(1<<r.extra)-1,l>>>=r.extra,h-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=ie;case ie:for(;Te=r.distcode[l&(1<<r.distbits)-1],ye=Te>>>24,_e=Te>>>16&255,me=65535&Te,!(ye<=h);){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(0===(240&_e)){for(be=ye,ve=_e,Se=me;Te=r.distcode[Se+((l&(1<<be+ve)-1)>>be)],ye=Te>>>24,_e=Te>>>16&255,me=65535&Te,!(be+ye<=h);){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}l>>>=be,h-=be,r.back+=be}if(l>>>=ye,h-=ye,r.back+=ye,64&_e){e.msg="invalid distance code",r.mode=le;break}r.offset=me,r.extra=15&_e,r.mode=se;case se:if(r.extra){for(je=r.extra;h<je;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}r.offset+=l&(1<<r.extra)-1,l>>>=r.extra,h-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=le;break}r.mode=ae;case ae:if(0===p)break e;if(y=d-p,r.offset>y){if(y=r.offset-y,y>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=le;break}y>r.wnext?(y-=r.wnext,_=r.wsize-y):_=r.wnext-y,y>r.length&&(y=r.length),de=r.window}else de=i,_=a-r.offset,y=r.length;y>p&&(y=p),p-=y,r.length-=y;do i[a++]=de[_++];while(--y);0===r.length&&(r.mode=oe);break;case ue:if(0===p)break e;i[a++]=r.length,p--,r.mode=oe;break;case pe:if(r.wrap){for(;h<32;){if(0===u)break e;u--,l|=n[s++]<<h,h+=8}if(d-=p,e.total_out+=d,r.total+=d,d&&(e.adler=r.check=r.flags?v(r.check,i,d,a-d):b(r.check,i,d,a-d)),d=p,(r.flags?l:o(l))!==r.check){e.msg="incorrect data check",r.mode=le;break}l=0,h=0}r.mode=ge;case ge:if(r.wrap&&r.flags){for(;h<32;){if(0===u)break e;u--,l+=n[s++]<<h,h+=8}if(l!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=le;break}l=0,h=0}r.mode=ce;case ce:we=O;break e;case le:we=F;break e;case he:return k;case fe:default:return C}return e.next_out=a,e.avail_out=p,e.next_in=s,e.avail_in=u,r.hold=l,r.bits=h,(r.wsize||d!==e.avail_out&&r.mode<le&&(r.mode<pe||t!==T))&&c(e,e.output,e.next_out,d-e.avail_out)?(r.mode=he,k):(f-=e.avail_in,d-=e.avail_out,e.total_in+=f,e.total_out+=d,r.total+=d,r.wrap&&d&&(e.adler=r.check=r.flags?v(r.check,i,d,e.next_out-d):b(r.check,i,d,e.next_out-d)),e.data_type=r.bits+(r.last?64:0)+(r.mode===X?128:0)+(r.mode===re||r.mode===J?256:0),(0===f&&0===d||t===T)&&we===M&&(we=R),we)}function h(e){if(!e||!e.state)return C;var t=e.state;return t.window&&(t.window=null),e.state=null,M}function f(e,t){var r;return e&&e.state?(r=e.state,0===(2&r.wrap)?C:(r.head=t,t.done=!1,M)):C}function d(e,t){var r,o,n,i=t.length;return e&&e.state?(r=e.state,0!==r.wrap&&r.mode!==Z?C:r.mode===Z&&(o=1,o=b(o,t,i,0),o!==r.check)?F:(n=c(e,t,i,i))?(r.mode=he,k):(r.havedict=1,M)):C}var y,_,m=e("../utils/common"),b=e("./adler32"),v=e("./crc32"),S=e("./inffast"),E=e("./inftrees"),w=0,A=1,j=2,T=4,I=5,B=6,M=0,O=1,x=2,C=-2,F=-3,k=-4,R=-5,D=8,N=1,U=2,L=3,P=4,W=5,z=6,V=7,H=8,G=9,Y=10,Z=11,X=12,K=13,q=14,J=15,$=16,Q=17,ee=18,te=19,re=20,oe=21,ne=22,ie=23,se=24,ae=25,ue=26,pe=27,ge=28,ce=29,le=30,he=31,fe=32,de=852,ye=592,_e=15,me=_e,be=!0;r.inflateReset=s,r.inflateReset2=a,r.inflateResetKeep=i,r.inflateInit=p,r.inflateInit2=u,r.inflate=l,r.inflateEnd=h,r.inflateGetHeader=f,r.inflateSetDictionary=d,r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":62,"./adler32":64,"./crc32":66,"./inffast":69,"./inftrees":71}],71:[function(e,t,r){"use strict";var o=e("../utils/common"),n=15,i=852,s=592,a=0,u=1,p=2,g=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],c=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],l=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],h=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,f,d,y,_,m){var b,v,S,E,w,A,j,T,I,B=m.bits,M=0,O=0,x=0,C=0,F=0,k=0,R=0,D=0,N=0,U=0,L=null,P=0,W=new o.Buf16(n+1),z=new o.Buf16(n+1),V=null,H=0;for(M=0;M<=n;M++)W[M]=0;for(O=0;O<f;O++)W[t[r+O]]++;for(F=B,C=n;C>=1&&0===W[C];C--);if(F>C&&(F=C),0===C)return d[y++]=20971520,d[y++]=20971520,m.bits=1,0;for(x=1;x<C&&0===W[x];x++);for(F<x&&(F=x),D=1,M=1;M<=n;M++)if(D<<=1,D-=W[M],D<0)return-1;if(D>0&&(e===a||1!==C))return-1;for(z[1]=0,M=1;M<n;M++)z[M+1]=z[M]+W[M];for(O=0;O<f;O++)0!==t[r+O]&&(_[z[t[r+O]]++]=O);if(e===a?(L=V=_,A=19):e===u?(L=g,P-=257,V=c,H-=257,A=256):(L=l,V=h,A=-1),U=0,O=0,M=x,w=y,k=F,R=0,S=-1,N=1<<F,E=N-1,e===u&&N>i||e===p&&N>s)return 1;for(var G=0;;){G++,j=M-R,_[O]<A?(T=0,I=_[O]):_[O]>A?(T=V[H+_[O]],I=L[P+_[O]]):(T=96,I=0),b=1<<M-R,v=1<<k,x=v;do v-=b,d[w+(U>>R)+v]=j<<24|T<<16|I|0;while(0!==v);for(b=1<<M-1;U&b;)b>>=1;if(0!==b?(U&=b-1,U+=b):U=0,O++,0===--W[M]){if(M===C)break;M=t[r+_[O]]}if(M>F&&(U&E)!==S){for(0===R&&(R=F),w+=x,k=M-R,D=1<<k;k+R<C&&(D-=W[k+R],!(D<=0));)k++,D<<=1;if(N+=1<<k,e===u&&N>i||e===p&&N>s)return 1;S=U&E,d[S]=F<<24|k<<16|w-y|0}}return 0!==U&&(d[w+U]=M-R<<24|64<<16|0),m.bits=F,0}},{"../utils/common":62}],72:[function(e,t,r){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],73:[function(e,t,r){"use strict";function o(e){for(var t=e.length;--t>=0;)e[t]=0}function n(e,t,r,o,n){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=o,this.max_length=n,this.has_stree=e&&e.length}function i(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function s(e){return e<256?ue[e]:ue[256+(e>>>7)]}function a(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function u(e,t,r){e.bi_valid>K-r?(e.bi_buf|=t<<e.bi_valid&65535,a(e,e.bi_buf),e.bi_buf=t>>K-e.bi_valid,e.bi_valid+=r-K):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function p(e,t,r){u(e,r[2*t],r[2*t+1])}function g(e,t){var r=0;do r|=1&e,e>>>=1,r<<=1;while(--t>0);return r>>>1}function c(e){16===e.bi_valid?(a(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}function l(e,t){var r,o,n,i,s,a,u=t.dyn_tree,p=t.max_code,g=t.stat_desc.static_tree,c=t.stat_desc.has_stree,l=t.stat_desc.extra_bits,h=t.stat_desc.extra_base,f=t.stat_desc.max_length,d=0;for(i=0;i<=X;i++)e.bl_count[i]=0;for(u[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<Z;r++)o=e.heap[r],i=u[2*u[2*o+1]+1]+1,i>f&&(i=f,d++),u[2*o+1]=i,o>p||(e.bl_count[i]++,s=0,o>=h&&(s=l[o-h]),a=u[2*o],e.opt_len+=a*(i+s),c&&(e.static_len+=a*(g[2*o+1]+s)));if(0!==d){do{for(i=f-1;0===e.bl_count[i];)i--;e.bl_count[i]--,e.bl_count[i+1]+=2,e.bl_count[f]--,d-=2}while(d>0);for(i=f;0!==i;i--)for(o=e.bl_count[i];0!==o;)n=e.heap[--r],n>p||(u[2*n+1]!==i&&(e.opt_len+=(i-u[2*n+1])*u[2*n],u[2*n+1]=i),o--)}}function h(e,t,r){var o,n,i=new Array(X+1),s=0;for(o=1;o<=X;o++)i[o]=s=s+r[o-1]<<1;for(n=0;n<=t;n++){var a=e[2*n+1];0!==a&&(e[2*n]=g(i[a]++,a))}}function f(){var e,t,r,o,i,s=new Array(X+1);for(r=0,o=0;o<z-1;o++)for(ge[o]=r,e=0;e<1<<te[o];e++)pe[r++]=o;for(pe[r-1]=o,i=0,o=0;o<16;o++)for(ce[o]=i,e=0;e<1<<re[o];e++)ue[i++]=o;for(i>>=7;o<G;o++)for(ce[o]=i<<7,e=0;e<1<<re[o]-7;e++)ue[256+i++]=o;for(t=0;t<=X;t++)s[t]=0;for(e=0;e<=143;)se[2*e+1]=8,e++,s[8]++;for(;e<=255;)se[2*e+1]=9,e++,s[9]++;for(;e<=279;)se[2*e+1]=7,e++,s[7]++;for(;e<=287;)se[2*e+1]=8,e++,s[8]++;for(h(se,H+1,s),e=0;e<G;e++)ae[2*e+1]=5,ae[2*e]=g(e,5);le=new n(se,te,V+1,H,X),he=new n(ae,re,0,G,X),fe=new n(new Array(0),oe,0,Y,q)}function d(e){var t;for(t=0;t<H;t++)e.dyn_ltree[2*t]=0;for(t=0;t<G;t++)e.dyn_dtree[2*t]=0;for(t=0;t<Y;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*J]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function y(e){e.bi_valid>8?a(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function _(e,t,r,o){y(e),o&&(a(e,r),a(e,~r)),C.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}function m(e,t,r,o){var n=2*t,i=2*r;return e[n]<e[i]||e[n]===e[i]&&o[t]<=o[r]}function b(e,t,r){for(var o=e.heap[r],n=r<<1;n<=e.heap_len&&(n<e.heap_len&&m(t,e.heap[n+1],e.heap[n],e.depth)&&n++,!m(t,o,e.heap[n],e.depth));)e.heap[r]=e.heap[n],r=n,n<<=1;e.heap[r]=o}function v(e,t,r){var o,n,i,a,g=0;if(0!==e.last_lit)do o=e.pending_buf[e.d_buf+2*g]<<8|e.pending_buf[e.d_buf+2*g+1],n=e.pending_buf[e.l_buf+g],g++,0===o?p(e,n,t):(i=pe[n],p(e,i+V+1,t),a=te[i],0!==a&&(n-=ge[i],u(e,n,a)),o--,i=s(o),p(e,i,r),a=re[i],0!==a&&(o-=ce[i],u(e,o,a)));while(g<e.last_lit);p(e,J,t)}function S(e,t){var r,o,n,i=t.dyn_tree,s=t.stat_desc.static_tree,a=t.stat_desc.has_stree,u=t.stat_desc.elems,p=-1;for(e.heap_len=0,e.heap_max=Z,r=0;r<u;r++)0!==i[2*r]?(e.heap[++e.heap_len]=p=r,e.depth[r]=0):i[2*r+1]=0;for(;e.heap_len<2;)n=e.heap[++e.heap_len]=p<2?++p:0,i[2*n]=1,e.depth[n]=0,e.opt_len--,a&&(e.static_len-=s[2*n+1]);for(t.max_code=p,r=e.heap_len>>1;r>=1;r--)b(e,i,r);n=u;do r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],b(e,i,1),o=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=o,i[2*n]=i[2*r]+i[2*o],e.depth[n]=(e.depth[r]>=e.depth[o]?e.depth[r]:e.depth[o])+1,i[2*r+1]=i[2*o+1]=n,e.heap[1]=n++,b(e,i,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],l(e,t),h(i,p,e.bl_count)}function E(e,t,r){var o,n,i=-1,s=t[1],a=0,u=7,p=4;for(0===s&&(u=138,p=3),t[2*(r+1)+1]=65535,o=0;o<=r;o++)n=s,s=t[2*(o+1)+1],++a<u&&n===s||(a<p?e.bl_tree[2*n]+=a:0!==n?(n!==i&&e.bl_tree[2*n]++,e.bl_tree[2*$]++):a<=10?e.bl_tree[2*Q]++:e.bl_tree[2*ee]++,a=0,i=n,0===s?(u=138,p=3):n===s?(u=6,p=3):(u=7,p=4))}function w(e,t,r){var o,n,i=-1,s=t[1],a=0,g=7,c=4;for(0===s&&(g=138,c=3),o=0;o<=r;o++)if(n=s,s=t[2*(o+1)+1],!(++a<g&&n===s)){if(a<c){do p(e,n,e.bl_tree);while(0!==--a)}else 0!==n?(n!==i&&(p(e,n,e.bl_tree),a--),p(e,$,e.bl_tree),u(e,a-3,2)):a<=10?(p(e,Q,e.bl_tree),u(e,a-3,3)):(p(e,ee,e.bl_tree),u(e,a-11,7));a=0,i=n,0===s?(g=138,c=3):n===s?(g=6,c=3):(g=7,c=4)}}function A(e){var t;for(E(e,e.dyn_ltree,e.l_desc.max_code),E(e,e.dyn_dtree,e.d_desc.max_code),S(e,e.bl_desc),t=Y-1;t>=3&&0===e.bl_tree[2*ne[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}function j(e,t,r,o){var n;for(u(e,t-257,5),u(e,r-1,5),u(e,o-4,4),n=0;n<o;n++)u(e,e.bl_tree[2*ne[n]+1],3);w(e,e.dyn_ltree,t-1),w(e,e.dyn_dtree,r-1)}function T(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return k;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return R;for(t=32;t<V;t++)if(0!==e.dyn_ltree[2*t])return R;return k}function I(e){de||(f(),de=!0),e.l_desc=new i(e.dyn_ltree,le),e.d_desc=new i(e.dyn_dtree,he),e.bl_desc=new i(e.bl_tree,fe),e.bi_buf=0,e.bi_valid=0,d(e)}function B(e,t,r,o){u(e,(N<<1)+(o?1:0),3),_(e,t,r,!0)}function M(e){u(e,U<<1,3),p(e,J,se),c(e)}function O(e,t,r,o){var n,i,s=0;e.level>0?(e.strm.data_type===D&&(e.strm.data_type=T(e)),S(e,e.l_desc),S(e,e.d_desc),s=A(e),n=e.opt_len+3+7>>>3,i=e.static_len+3+7>>>3,i<=n&&(n=i)):n=i=r+5,r+4<=n&&t!==-1?B(e,t,r,o):e.strategy===F||i===n?(u(e,(U<<1)+(o?1:0),3),v(e,se,ae)):(u(e,(L<<1)+(o?1:0),3),j(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),v(e,e.dyn_ltree,e.dyn_dtree)),d(e),o&&y(e)}function x(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(pe[r]+V+1)]++,e.dyn_dtree[2*s(t)]++),e.last_lit===e.lit_bufsize-1}var C=e("../utils/common"),F=4,k=0,R=1,D=2,N=0,U=1,L=2,P=3,W=258,z=29,V=256,H=V+1+z,G=30,Y=19,Z=2*H+1,X=15,K=16,q=7,J=256,$=16,Q=17,ee=18,te=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],re=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],oe=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],ne=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],ie=512,se=new Array(2*(H+2));o(se);var ae=new Array(2*G);o(ae);var ue=new Array(ie);o(ue);var pe=new Array(W-P+1);o(pe);var ge=new Array(z);o(ge);var ce=new Array(G);o(ce);var le,he,fe,de=!1;r._tr_init=I,r._tr_stored_block=B,r._tr_flush_block=O,r._tr_tally=x,r._tr_align=M},{"../utils/common":62}],74:[function(e,t,r){"use strict";function o(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}t.exports=o},{}]},{},[10])(10)})}).call(t,r(95).Buffer,function(){return this}())},function(e,t,r){(function(e,o){"use strict";function n(){function e(){}try{var t=new Uint8Array(1);return t.foo=function(){return 42},t.constructor=e,42===t.foo()&&t.constructor===e&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(r){return!1}}function i(){return e.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function e(t){return this instanceof e?(e.TYPED_ARRAY_SUPPORT||(this.length=0,this.parent=void 0),"number"==typeof t?s(this,t):"string"==typeof t?a(this,t,arguments.length>1?arguments[1]:"utf8"):u(this,t)):arguments.length>1?new e(t,arguments[1]):new e(t)}function s(t,r){if(t=d(t,r<0?0:0|y(r)),!e.TYPED_ARRAY_SUPPORT)for(var o=0;o<r;o++)t[o]=0;return t}function a(e,t,r){"string"==typeof r&&""!==r||(r="utf8");var o=0|m(t,r);return e=d(e,o),e.write(t,r),e}function u(t,r){if(e.isBuffer(r))return p(t,r);if(q(r))return g(t,r);if(null==r)throw new TypeError("must start with number, buffer, array or string");if("undefined"!=typeof ArrayBuffer){if(r.buffer instanceof ArrayBuffer)return c(t,r);if(r instanceof ArrayBuffer)return l(t,r)}return r.length?h(t,r):f(t,r)}function p(e,t){var r=0|y(t.length);return e=d(e,r),t.copy(e,0,0,r),e}function g(e,t){var r=0|y(t.length);e=d(e,r);for(var o=0;o<r;o+=1)e[o]=255&t[o];return e}function c(e,t){var r=0|y(t.length);e=d(e,r);for(var o=0;o<r;o+=1)e[o]=255&t[o];return e}function l(t,r){return e.TYPED_ARRAY_SUPPORT?(r.byteLength,t=e._augment(new Uint8Array(r))):t=c(t,new Uint8Array(r)),t}function h(e,t){var r=0|y(t.length);e=d(e,r);for(var o=0;o<r;o+=1)e[o]=255&t[o];return e}function f(e,t){var r,o=0;"Buffer"===t.type&&q(t.data)&&(r=t.data,o=0|y(r.length)),e=d(e,o);for(var n=0;n<o;n+=1)e[n]=255&r[n];return e}function d(t,r){e.TYPED_ARRAY_SUPPORT?(t=e._augment(new Uint8Array(r)),t.__proto__=e.prototype):(t.length=r,t._isBuffer=!0);var o=0!==r&&r<=e.poolSize>>>1;return o&&(t.parent=J),t}function y(e){if(e>=i())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i().toString(16)+" bytes");return 0|e}function _(t,r){if(!(this instanceof _))return new _(t,r);var o=new e(t,r);return delete o.parent,o}function m(e,t){"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var o=!1;;)switch(t){case"ascii":case"binary":case"raw":case"raws":return r;case"utf8":case"utf-8":return V(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return Y(e).length;default:if(o)return V(e).length;t=(""+t).toLowerCase(),o=!0}}function b(e,t,r){var o=!1;if(t=0|t,r=void 0===r||r===1/0?this.length:0|r,e||(e="utf8"),t<0&&(t=0),r>this.length&&(r=this.length),r<=t)return"";for(;;)switch(e){case"hex":return x(this,t,r);case"utf8":case"utf-8":return I(this,t,r);case"ascii":return M(this,t,r);case"binary":return O(this,t,r);case"base64":return T(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,t,r);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function v(e,t,r,o){r=Number(r)||0;var n=e.length-r;o?(o=Number(o),o>n&&(o=n)):o=n;var i=t.length;if(i%2!==0)throw new Error("Invalid hex string");o>i/2&&(o=i/2);for(var s=0;s<o;s++){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))throw new Error("Invalid hex string");e[r+s]=a}return s}function S(e,t,r,o){return Z(V(t,e.length-r),e,r,o)}function E(e,t,r,o){return Z(H(t),e,r,o)}function w(e,t,r,o){return E(e,t,r,o)}function A(e,t,r,o){return Z(Y(t),e,r,o)}function j(e,t,r,o){return Z(G(t,e.length-r),e,r,o)}function T(e,t,r){return 0===t&&r===e.length?X.fromByteArray(e):X.fromByteArray(e.slice(t,r))}function I(e,t,r){r=Math.min(e.length,r);for(var o=[],n=t;n<r;){var i=e[n],s=null,a=i>239?4:i>223?3:i>191?2:1;if(n+a<=r){var u,p,g,c;switch(a){case 1:i<128&&(s=i);break;case 2:u=e[n+1],128===(192&u)&&(c=(31&i)<<6|63&u,c>127&&(s=c));break;case 3:u=e[n+1],p=e[n+2],128===(192&u)&&128===(192&p)&&(c=(15&i)<<12|(63&u)<<6|63&p,c>2047&&(c<55296||c>57343)&&(s=c));break;case 4:u=e[n+1],p=e[n+2],g=e[n+3],128===(192&u)&&128===(192&p)&&128===(192&g)&&(c=(15&i)<<18|(63&u)<<12|(63&p)<<6|63&g,c>65535&&c<1114112&&(s=c))}}null===s?(s=65533,a=1):s>65535&&(s-=65536,o.push(s>>>10&1023|55296),s=56320|1023&s),o.push(s),n+=a}return B(o)}function B(e){var t=e.length;if(t<=Q)return String.fromCharCode.apply(String,e);for(var r="",o=0;o<t;)r+=String.fromCharCode.apply(String,e.slice(o,o+=Q));return r}function M(e,t,r){var o="";r=Math.min(e.length,r);for(var n=t;n<r;n++)o+=String.fromCharCode(127&e[n]);return o}function O(e,t,r){var o="";r=Math.min(e.length,r);for(var n=t;n<r;n++)o+=String.fromCharCode(e[n]);return o}function x(e,t,r){var o=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>o)&&(r=o);for(var n="",i=t;i<r;i++)n+=z(e[i]);return n}function C(e,t,r){for(var o=e.slice(t,r),n="",i=0;i<o.length;i+=2)n+=String.fromCharCode(o[i]+256*o[i+1]);return n}function F(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function k(t,r,o,n,i,s){if(!e.isBuffer(t))throw new TypeError("buffer must be a Buffer instance");if(r>i||r<s)throw new RangeError("value is out of bounds");if(o+n>t.length)throw new RangeError("index out of range")}function R(e,t,r,o){t<0&&(t=65535+t+1);for(var n=0,i=Math.min(e.length-r,2);n<i;n++)e[r+n]=(t&255<<8*(o?n:1-n))>>>8*(o?n:1-n)}function D(e,t,r,o){t<0&&(t=4294967295+t+1);for(var n=0,i=Math.min(e.length-r,4);n<i;n++)e[r+n]=t>>>8*(o?n:3-n)&255}function N(e,t,r,o,n,i){if(t>n||t<i)throw new RangeError("value is out of bounds");if(r+o>e.length)throw new RangeError("index out of range");if(r<0)throw new RangeError("index out of range")}function U(e,t,r,o,n){return n||N(e,t,r,4,3.4028234663852886e38,-3.4028234663852886e38),K.write(e,t,r,o,23,4),r+4}function L(e,t,r,o,n){return n||N(e,t,r,8,1.7976931348623157e308,-1.7976931348623157e308),K.write(e,t,r,o,52,8),r+8}function P(e){if(e=W(e).replace(te,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function W(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function z(e){return e<16?"0"+e.toString(16):e.toString(16)}function V(e,t){t=t||1/0;for(var r,o=e.length,n=null,i=[],s=0;s<o;s++){if(r=e.charCodeAt(s),r>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(s+1===o){(t-=3)>-1&&i.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),n=r;continue}r=(n-55296<<10|r-56320)+65536}else n&&(t-=3)>-1&&i.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function H(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t}function G(e,t){for(var r,o,n,i=[],s=0;s<e.length&&!((t-=2)<0);s++)r=e.charCodeAt(s),o=r>>8,n=r%256,i.push(n),i.push(o);return i}function Y(e){return X.toByteArray(P(e))}function Z(e,t,r,o){for(var n=0;n<o&&!(n+r>=t.length||n>=e.length);n++)t[n+r]=e[n];return n}var X=r(96),K=r(97),q=r(98);t.Buffer=e,t.SlowBuffer=_,t.INSPECT_MAX_BYTES=50,e.poolSize=8192;var J={};e.TYPED_ARRAY_SUPPORT=void 0!==o.TYPED_ARRAY_SUPPORT?o.TYPED_ARRAY_SUPPORT:n(),e.TYPED_ARRAY_SUPPORT?(e.prototype.__proto__=Uint8Array.prototype,e.__proto__=Uint8Array):(e.prototype.length=void 0,e.prototype.parent=void 0),e.isBuffer=function re(e){return!(null==e||!e._isBuffer)},e.compare=function oe(t,r){if(!e.isBuffer(t)||!e.isBuffer(r))throw new TypeError("Arguments must be Buffers");if(t===r)return 0;for(var o=t.length,n=r.length,i=0,s=Math.min(o,n);i<s&&t[i]===r[i];)++i;return i!==s&&(o=t[i],n=r[i]),o<n?-1:n<o?1:0},e.isEncoding=function ne(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},e.concat=function ie(t,r){if(!q(t))throw new TypeError("list argument must be an Array of Buffers.");if(0===t.length)return new e(0);var o;if(void 0===r)for(r=0,o=0;o<t.length;o++)r+=t[o].length;var n=new e(r),i=0;for(o=0;o<t.length;o++){var s=t[o];s.copy(n,i),i+=s.length}return n},e.byteLength=m,e.prototype.toString=function se(){var e=0|this.length;return 0===e?"":0===arguments.length?I(this,0,e):b.apply(this,arguments)},e.prototype.equals=function ae(t){if(!e.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===e.compare(this,t)},e.prototype.inspect=function ue(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},e.prototype.compare=function pe(t){if(!e.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?0:e.compare(this,t)},e.prototype.indexOf=function ge(t,r){function o(e,t,r){for(var o=-1,n=0;r+n<e.length;n++)if(e[r+n]===t[o===-1?0:n-o]){if(o===-1&&(o=n),n-o+1===t.length)return r+o}else o=-1;return-1}if(r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r>>=0,0===this.length)return-1;if(r>=this.length)return-1;if(r<0&&(r=Math.max(this.length+r,0)),"string"==typeof t)return 0===t.length?-1:String.prototype.indexOf.call(this,t,r);if(e.isBuffer(t))return o(this,t,r);if("number"==typeof t)return e.TYPED_ARRAY_SUPPORT&&"function"===Uint8Array.prototype.indexOf?Uint8Array.prototype.indexOf.call(this,t,r):o(this,[t],r);throw new TypeError("val must be string, number or Buffer")},e.prototype.get=function ce(e){return console.log(".get() is deprecated. Access using array indexes instead."),this.readUInt8(e)},e.prototype.set=function le(e,t){return console.log(".set() is deprecated. Access using array indexes instead."),this.writeUInt8(e,t)},e.prototype.write=function he(e,t,r,o){if(void 0===t)o="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)o=t,r=this.length,t=0;else if(isFinite(t))t=0|t,isFinite(r)?(r=0|r,void 0===o&&(o="utf8")):(o=r,r=void 0);else{var n=o;o=t,t=0|r,r=n}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("attempt to write outside buffer bounds");o||(o="utf8");for(var s=!1;;)switch(o){case"hex":return v(this,e,t,r);case"utf8":case"utf-8":return S(this,e,t,r);case"ascii":return E(this,e,t,r);case"binary":return w(this,e,t,r);case"base64":return A(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return j(this,e,t,r);default:if(s)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),s=!0}},e.prototype.toJSON=function fe(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Q=4096;e.prototype.slice=function de(t,r){var o=this.length;t=~~t,r=void 0===r?o:~~r,t<0?(t+=o,t<0&&(t=0)):t>o&&(t=o),r<0?(r+=o,r<0&&(r=0)):r>o&&(r=o),r<t&&(r=t);var n;if(e.TYPED_ARRAY_SUPPORT)n=e._augment(this.subarray(t,r));else{var i=r-t;n=new e(i,(void 0));for(var s=0;s<i;s++)n[s]=this[s+t]}return n.length&&(n.parent=this.parent||this),n},e.prototype.readUIntLE=function ye(e,t,r){e=0|e,t=0|t,r||F(e,t,this.length);for(var o=this[e],n=1,i=0;++i<t&&(n*=256);)o+=this[e+i]*n;return o},e.prototype.readUIntBE=function _e(e,t,r){e=0|e,t=0|t,r||F(e,t,this.length);for(var o=this[e+--t],n=1;t>0&&(n*=256);)o+=this[e+--t]*n;return o},e.prototype.readUInt8=function me(e,t){return t||F(e,1,this.length),this[e]},e.prototype.readUInt16LE=function be(e,t){return t||F(e,2,this.length),this[e]|this[e+1]<<8},e.prototype.readUInt16BE=function ve(e,t){return t||F(e,2,this.length),this[e]<<8|this[e+1]},e.prototype.readUInt32LE=function Se(e,t){return t||F(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},e.prototype.readUInt32BE=function Ee(e,t){return t||F(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},e.prototype.readIntLE=function we(e,t,r){e=0|e,t=0|t,r||F(e,t,this.length);for(var o=this[e],n=1,i=0;++i<t&&(n*=256);)o+=this[e+i]*n;return n*=128,o>=n&&(o-=Math.pow(2,8*t)),o},e.prototype.readIntBE=function Ae(e,t,r){e=0|e,t=0|t,r||F(e,t,this.length);for(var o=t,n=1,i=this[e+--o];o>0&&(n*=256);)i+=this[e+--o]*n;return n*=128,i>=n&&(i-=Math.pow(2,8*t)),i},e.prototype.readInt8=function je(e,t){return t||F(e,1,this.length),128&this[e]?(255-this[e]+1)*-1:this[e]},e.prototype.readInt16LE=function Te(e,t){t||F(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},e.prototype.readInt16BE=function Ie(e,t){t||F(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},e.prototype.readInt32LE=function Be(e,t){return t||F(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24;
},e.prototype.readInt32BE=function Me(e,t){return t||F(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},e.prototype.readFloatLE=function Oe(e,t){return t||F(e,4,this.length),K.read(this,e,!0,23,4)},e.prototype.readFloatBE=function xe(e,t){return t||F(e,4,this.length),K.read(this,e,!1,23,4)},e.prototype.readDoubleLE=function Ce(e,t){return t||F(e,8,this.length),K.read(this,e,!0,52,8)},e.prototype.readDoubleBE=function Fe(e,t){return t||F(e,8,this.length),K.read(this,e,!1,52,8)},e.prototype.writeUIntLE=function ke(e,t,r,o){e=+e,t=0|t,r=0|r,o||k(this,e,t,r,Math.pow(2,8*r),0);var n=1,i=0;for(this[t]=255&e;++i<r&&(n*=256);)this[t+i]=e/n&255;return t+r},e.prototype.writeUIntBE=function Re(e,t,r,o){e=+e,t=0|t,r=0|r,o||k(this,e,t,r,Math.pow(2,8*r),0);var n=r-1,i=1;for(this[t+n]=255&e;--n>=0&&(i*=256);)this[t+n]=e/i&255;return t+r},e.prototype.writeUInt8=function De(t,r,o){return t=+t,r=0|r,o||k(this,t,r,1,255,0),e.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[r]=255&t,r+1},e.prototype.writeUInt16LE=function Ne(t,r,o){return t=+t,r=0|r,o||k(this,t,r,2,65535,0),e.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):R(this,t,r,!0),r+2},e.prototype.writeUInt16BE=function Ue(t,r,o){return t=+t,r=0|r,o||k(this,t,r,2,65535,0),e.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):R(this,t,r,!1),r+2},e.prototype.writeUInt32LE=function Le(t,r,o){return t=+t,r=0|r,o||k(this,t,r,4,4294967295,0),e.TYPED_ARRAY_SUPPORT?(this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t):D(this,t,r,!0),r+4},e.prototype.writeUInt32BE=function Pe(t,r,o){return t=+t,r=0|r,o||k(this,t,r,4,4294967295,0),e.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):D(this,t,r,!1),r+4},e.prototype.writeIntLE=function We(e,t,r,o){if(e=+e,t=0|t,!o){var n=Math.pow(2,8*r-1);k(this,e,t,r,n-1,-n)}var i=0,s=1,a=e<0?1:0;for(this[t]=255&e;++i<r&&(s*=256);)this[t+i]=(e/s>>0)-a&255;return t+r},e.prototype.writeIntBE=function ze(e,t,r,o){if(e=+e,t=0|t,!o){var n=Math.pow(2,8*r-1);k(this,e,t,r,n-1,-n)}var i=r-1,s=1,a=e<0?1:0;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=(e/s>>0)-a&255;return t+r},e.prototype.writeInt8=function Ve(t,r,o){return t=+t,r=0|r,o||k(this,t,r,1,127,-128),e.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[r]=255&t,r+1},e.prototype.writeInt16LE=function $e(t,r,o){return t=+t,r=0|r,o||k(this,t,r,2,32767,-32768),e.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):R(this,t,r,!0),r+2},e.prototype.writeInt16BE=function He(t,r,o){return t=+t,r=0|r,o||k(this,t,r,2,32767,-32768),e.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):R(this,t,r,!1),r+2},e.prototype.writeInt32LE=function Ge(t,r,o){return t=+t,r=0|r,o||k(this,t,r,4,2147483647,-2147483648),e.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24):D(this,t,r,!0),r+4},e.prototype.writeInt32BE=function Ye(t,r,o){return t=+t,r=0|r,o||k(this,t,r,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),e.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):D(this,t,r,!1),r+4},e.prototype.writeFloatLE=function Ze(e,t,r){return U(this,e,t,!0,r)},e.prototype.writeFloatBE=function Xe(e,t,r){return U(this,e,t,!1,r)},e.prototype.writeDoubleLE=function Ke(e,t,r){return L(this,e,t,!0,r)},e.prototype.writeDoubleBE=function qe(e,t,r){return L(this,e,t,!1,r)},e.prototype.copy=function Je(t,r,o,n){if(o||(o=0),n||0===n||(n=this.length),r>=t.length&&(r=t.length),r||(r=0),n>0&&n<o&&(n=o),n===o)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(o<0||o>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-r<n-o&&(n=t.length-r+o);var i=n-o,s;if(this===t&&o<r&&r<n)for(s=i-1;s>=0;s--)t[s+r]=this[s+o];else if(i<1e3||!e.TYPED_ARRAY_SUPPORT)for(s=0;s<i;s++)t[s+r]=this[s+o];else t._set(this.subarray(o,o+i),r);return i},e.prototype.fill=function Qe(e,t,r){if(e||(e=0),t||(t=0),r||(r=this.length),r<t)throw new RangeError("end < start");if(r!==t&&0!==this.length){if(t<0||t>=this.length)throw new RangeError("start out of bounds");if(r<0||r>this.length)throw new RangeError("end out of bounds");var o;if("number"==typeof e)for(o=t;o<r;o++)this[o]=e;else{var n=V(e.toString()),i=n.length;for(o=t;o<r;o++)this[o]=n[o%i]}return this}},e.prototype.toArrayBuffer=function et(){if("undefined"!=typeof Uint8Array){if(e.TYPED_ARRAY_SUPPORT)return new e(this).buffer;for(var t=new Uint8Array(this.length),r=0,o=t.length;r<o;r+=1)t[r]=this[r];return t.buffer}throw new TypeError("Buffer.toArrayBuffer not supported in this browser")};var ee=e.prototype;e._augment=function tt(t){return t.constructor=e,t._isBuffer=!0,t._set=t.set,t.get=ee.get,t.set=ee.set,t.write=ee.write,t.toString=ee.toString,t.toLocaleString=ee.toString,t.toJSON=ee.toJSON,t.equals=ee.equals,t.compare=ee.compare,t.indexOf=ee.indexOf,t.copy=ee.copy,t.slice=ee.slice,t.readUIntLE=ee.readUIntLE,t.readUIntBE=ee.readUIntBE,t.readUInt8=ee.readUInt8,t.readUInt16LE=ee.readUInt16LE,t.readUInt16BE=ee.readUInt16BE,t.readUInt32LE=ee.readUInt32LE,t.readUInt32BE=ee.readUInt32BE,t.readIntLE=ee.readIntLE,t.readIntBE=ee.readIntBE,t.readInt8=ee.readInt8,t.readInt16LE=ee.readInt16LE,t.readInt16BE=ee.readInt16BE,t.readInt32LE=ee.readInt32LE,t.readInt32BE=ee.readInt32BE,t.readFloatLE=ee.readFloatLE,t.readFloatBE=ee.readFloatBE,t.readDoubleLE=ee.readDoubleLE,t.readDoubleBE=ee.readDoubleBE,t.writeUInt8=ee.writeUInt8,t.writeUIntLE=ee.writeUIntLE,t.writeUIntBE=ee.writeUIntBE,t.writeUInt16LE=ee.writeUInt16LE,t.writeUInt16BE=ee.writeUInt16BE,t.writeUInt32LE=ee.writeUInt32LE,t.writeUInt32BE=ee.writeUInt32BE,t.writeIntLE=ee.writeIntLE,t.writeIntBE=ee.writeIntBE,t.writeInt8=ee.writeInt8,t.writeInt16LE=ee.writeInt16LE,t.writeInt16BE=ee.writeInt16BE,t.writeInt32LE=ee.writeInt32LE,t.writeInt32BE=ee.writeInt32BE,t.writeFloatLE=ee.writeFloatLE,t.writeFloatBE=ee.writeFloatBE,t.writeDoubleLE=ee.writeDoubleLE,t.writeDoubleBE=ee.writeDoubleBE,t.fill=ee.fill,t.inspect=ee.inspect,t.toArrayBuffer=ee.toArrayBuffer,t};var te=/[^+\/0-9A-Za-z-_]/g}).call(t,r(95).Buffer,function(){return this}())},function(e,t,r){var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";!function(e){"use strict";function t(e){var t=e.charCodeAt(0);return t===s||t===c?62:t===a||t===l?63:t<u?-1:t<u+10?t-u+26+26:t<g+26?t-g:t<p+26?t-p+26:void 0}function r(e){function r(e){p[c++]=e}var o,n,s,a,u,p;if(e.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var g=e.length;u="="===e.charAt(g-2)?2:"="===e.charAt(g-1)?1:0,p=new i(3*e.length/4-u),s=u>0?e.length-4:e.length;var c=0;for(o=0,n=0;o<s;o+=4,n+=3)a=t(e.charAt(o))<<18|t(e.charAt(o+1))<<12|t(e.charAt(o+2))<<6|t(e.charAt(o+3)),r((16711680&a)>>16),r((65280&a)>>8),r(255&a);return 2===u?(a=t(e.charAt(o))<<2|t(e.charAt(o+1))>>4,r(255&a)):1===u&&(a=t(e.charAt(o))<<10|t(e.charAt(o+1))<<4|t(e.charAt(o+2))>>2,r(a>>8&255),r(255&a)),p}function n(e){function t(e){return o.charAt(e)}function r(e){return t(e>>18&63)+t(e>>12&63)+t(e>>6&63)+t(63&e)}var n,i=e.length%3,s="",a,u;for(n=0,u=e.length-i;n<u;n+=3)a=(e[n]<<16)+(e[n+1]<<8)+e[n+2],s+=r(a);switch(i){case 1:a=e[e.length-1],s+=t(a>>2),s+=t(a<<4&63),s+="==";break;case 2:a=(e[e.length-2]<<8)+e[e.length-1],s+=t(a>>10),s+=t(a>>4&63),s+=t(a<<2&63),s+="="}return s}var i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="+".charCodeAt(0),a="/".charCodeAt(0),u="0".charCodeAt(0),p="a".charCodeAt(0),g="A".charCodeAt(0),c="-".charCodeAt(0),l="_".charCodeAt(0);e.toByteArray=r,e.fromByteArray=n}(t)},function(e,t){t.read=function(e,t,r,o,n){var i,s,a=8*n-o-1,u=(1<<a)-1,p=u>>1,g=-7,c=r?n-1:0,l=r?-1:1,h=e[t+c];for(c+=l,i=h&(1<<-g)-1,h>>=-g,g+=a;g>0;i=256*i+e[t+c],c+=l,g-=8);for(s=i&(1<<-g)-1,i>>=-g,g+=o;g>0;s=256*s+e[t+c],c+=l,g-=8);if(0===i)i=1-p;else{if(i===u)return s?NaN:(h?-1:1)*(1/0);s+=Math.pow(2,o),i-=p}return(h?-1:1)*s*Math.pow(2,i-o)},t.write=function(e,t,r,o,n,i){var s,a,u,p=8*i-n-1,g=(1<<p)-1,c=g>>1,l=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,h=o?0:i-1,f=o?1:-1,d=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=g):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+c>=1?l/u:l*Math.pow(2,1-c),t*u>=2&&(s++,u/=2),s+c>=g?(a=0,s=g):s+c>=1?(a=(t*u-1)*Math.pow(2,n),s+=c):(a=t*Math.pow(2,c-1)*Math.pow(2,n),s=0));n>=8;e[r+h]=255&a,h+=f,a/=256,n-=8);for(s=s<<n|a,p+=n;p>0;e[r+h]=255&s,h+=f,s/=256,p-=8);e[r+h-f]|=128*d}},function(e,t){e.exports=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)}},function(e,t,r){var o,o;!function(t){e.exports=t()}(function(){return function e(t,r,n){function i(a,u){if(!r[a]){if(!t[a]){var p="function"==typeof o&&o;if(!u&&p)return o(a,!0);if(s)return s(a,!0);var g=new Error("Cannot find module '"+a+"'");throw g.code="MODULE_NOT_FOUND",g}var c=r[a]={exports:{}};t[a][0].call(c.exports,function(e){var r=t[a][1][e];return i(r||e)},c,c.exports,e,t,r,n)}return r[a].exports}for(var s="function"==typeof o&&o,a=0;a<n.length;a++)i(n[a]);return i}({1:[function(e,t,r){"use strict";function o(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)o(r,n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var i={arraySet:function(e,t,r,o,n){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+o),n);else for(var i=0;i<o;i++)e[n+i]=t[r+i]},flattenChunks:function(e){var t,r,o,n,i,s;for(o=0,t=0,r=e.length;t<r;t++)o+=e[t].length;for(s=new Uint8Array(o),n=0,t=0,r=e.length;t<r;t++)i=e[t],s.set(i,n),n+=i.length;return s}},s={arraySet:function(e,t,r,o,n){for(var i=0;i<o;i++)e[n+i]=t[r+i]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,s))},r.setTyped(n)},{}],2:[function(e,t,r){"use strict";function o(e,t){if(t<65537&&(e.subarray&&s||!e.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",o=0;o<t;o++)r+=String.fromCharCode(e[o]);return r}var n=e("./common"),i=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(e){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){s=!1}for(var a=new n.Buf8(256),u=0;u<256;u++)a[u]=u>=252?6:u>=248?5:u>=240?4:u>=224?3:u>=192?2:1;a[254]=a[254]=1,r.string2buf=function(e){var t,r,o,i,s,a=e.length,u=0;for(i=0;i<a;i++)55296==(64512&(r=e.charCodeAt(i)))&&i+1<a&&56320==(64512&(o=e.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(o-56320),i++),u+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(u),s=0,i=0;s<u;i++)55296==(64512&(r=e.charCodeAt(i)))&&i+1<a&&56320==(64512&(o=e.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(o-56320),i++),r<128?t[s++]=r:r<2048?(t[s++]=192|r>>>6,t[s++]=128|63&r):r<65536?(t[s++]=224|r>>>12,t[s++]=128|r>>>6&63,t[s++]=128|63&r):(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63,t[s++]=128|r>>>6&63,t[s++]=128|63&r);return t},r.buf2binstring=function(e){return o(e,e.length)},r.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,o=t.length;r<o;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,i,s,u=t||e.length,p=new Array(2*u);for(n=0,r=0;r<u;)if((i=e[r++])<128)p[n++]=i;else if((s=a[i])>4)p[n++]=65533,r+=s-1;else{for(i&=2===s?31:3===s?15:7;s>1&&r<u;)i=i<<6|63&e[r++],s--;s>1?p[n++]=65533:i<65536?p[n++]=i:(i-=65536,p[n++]=55296|i>>10&1023,p[n++]=56320|1023&i)}return o(p,n)},r.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;r>=0&&128==(192&e[r]);)r--;return r<0?t:0===r?t:r+a[e[r]]>t?r:t}},{"./common":1}],3:[function(e,t,r){"use strict";t.exports=function(e,t,r,o){for(var n=65535&e|0,i=e>>>16&65535|0,s=0;0!==r;){r-=s=r>2e3?2e3:r;do i=i+(n=n+t[o++]|0)|0;while(--s);n%=65521,i%=65521}return n|i<<16|0}},{}],4:[function(e,t,r){"use strict";var o=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var o=0;o<8;o++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,n){var i=o,s=n+r;e^=-1;for(var a=n;a<s;a++)e=e>>>8^i[255&(e^t[a])];return-1^e}},{}],5:[function(e,t,r){"use strict";function o(e,t){return e.msg=B[t],t}function n(e){return(e<<1)-(e>4?9:0)}function i(e){for(var t=e.length;--t>=0;)e[t]=0}function s(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(A.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function a(e,t){j._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,s(e.strm)}function u(e,t){e.pending_buf[e.pending++]=t}function p(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function g(e,t,r,o){var n=e.avail_in;return n>o&&(n=o),0===n?0:(e.avail_in-=n,A.arraySet(t,e.input,e.next_in,n,r),1===e.state.wrap?e.adler=T(e.adler,t,n,r):2===e.state.wrap&&(e.adler=I(e.adler,t,n,r)),e.next_in+=n,e.total_in+=n,n)}function c(e,t){var r,o,n=e.max_chain_length,i=e.strstart,s=e.prev_length,a=e.nice_match,u=e.strstart>e.w_size-oe?e.strstart-(e.w_size-oe):0,p=e.window,g=e.w_mask,c=e.prev,l=e.strstart+re,h=p[i+s-1],f=p[i+s];e.prev_length>=e.good_match&&(n>>=2),a>e.lookahead&&(a=e.lookahead);do if(r=t,p[r+s]===f&&p[r+s-1]===h&&p[r]===p[i]&&p[++r]===p[i+1]){i+=2,r++;do;while(p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&p[++i]===p[++r]&&i<l);if(o=re-(l-i),i=l-re,o>s){if(e.match_start=t,s=o,o>=a)break;h=p[i+s-1],f=p[i+s]}}while((t=c[t&g])>u&&0!=--n);return s<=e.lookahead?s:e.lookahead}function l(e){var t,r,o,n,i,s=e.w_size;do{if(n=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-oe)){A.arraySet(e.window,e.window,s,s,0),e.match_start-=s,e.strstart-=s,e.block_start-=s,t=r=e.hash_size;do o=e.head[--t],e.head[t]=o>=s?o-s:0;while(--r);t=r=s;do o=e.prev[--t],e.prev[t]=o>=s?o-s:0;while(--r);n+=s}if(0===e.strm.avail_in)break;if(r=g(e.strm,e.window,e.strstart+e.lookahead,n),e.lookahead+=r,e.lookahead+e.insert>=te)for(i=e.strstart-e.insert,e.ins_h=e.window[i],e.ins_h=(e.ins_h<<e.hash_shift^e.window[i+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[i+te-1])&e.hash_mask,e.prev[i&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=i,i++,e.insert--,!(e.lookahead+e.insert<te)););}while(e.lookahead<oe&&0!==e.strm.avail_in)}function h(e,t){for(var r,o;;){if(e.lookahead<oe){if(l(e),e.lookahead<oe&&t===M)return le;if(0===e.lookahead)break}if(r=0,e.lookahead>=te&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-oe&&(e.match_length=c(e,r)),e.match_length>=te)if(o=j._tr_tally(e,e.strstart-e.match_start,e.match_length-te),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=te){e.match_length--;do e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else o=j._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(o&&(a(e,!1),0===e.strm.avail_out))return le}return e.insert=e.strstart<te-1?e.strstart:te-1,t===C?(a(e,!0),0===e.strm.avail_out?fe:de):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?le:he}function f(e,t){for(var r,o,n;;){if(e.lookahead<oe){if(l(e),e.lookahead<oe&&t===M)return le;if(0===e.lookahead)break}if(r=0,e.lookahead>=te&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=te-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-oe&&(e.match_length=c(e,r),e.match_length<=5&&(e.strategy===P||e.match_length===te&&e.strstart-e.match_start>4096)&&(e.match_length=te-1)),e.prev_length>=te&&e.match_length<=e.prev_length){n=e.strstart+e.lookahead-te,o=j._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-te),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=n&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(0!=--e.prev_length);if(e.match_available=0,e.match_length=te-1,e.strstart++,o&&(a(e,!1),0===e.strm.avail_out))return le}else if(e.match_available){if((o=j._tr_tally(e,0,e.window[e.strstart-1]))&&a(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return le}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(o=j._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<te-1?e.strstart:te-1,t===C?(a(e,!0),0===e.strm.avail_out?fe:de):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?le:he}function d(e,t){for(var r,o,n,i,s=e.window;;){if(e.lookahead<=re){if(l(e),e.lookahead<=re&&t===M)return le;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=te&&e.strstart>0&&(n=e.strstart-1,(o=s[n])===s[++n]&&o===s[++n]&&o===s[++n])){i=e.strstart+re;do;while(o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&o===s[++n]&&n<i);e.match_length=re-(i-n),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=te?(r=j._tr_tally(e,1,e.match_length-te),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=j._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(a(e,!1),0===e.strm.avail_out))return le}return e.insert=0,t===C?(a(e,!0),0===e.strm.avail_out?fe:de):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?le:he}function y(e,t){for(var r;;){if(0===e.lookahead&&(l(e),0===e.lookahead)){if(t===M)return le;break}if(e.match_length=0,r=j._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(a(e,!1),0===e.strm.avail_out))return le}return e.insert=0,t===C?(a(e,!0),0===e.strm.avail_out?fe:de):e.last_lit&&(a(e,!1),0===e.strm.avail_out)?le:he}function _(e,t,r,o,n){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=o,this.func=n}function m(e){e.window_size=2*e.w_size,i(e.head),e.max_lazy_match=w[e.level].max_lazy,e.good_match=w[e.level].good_length,e.nice_match=w[e.level].nice_length,e.max_chain_length=w[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=te-1,e.match_available=0,e.ins_h=0}function b(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Y,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new A.Buf16(2*$),this.dyn_dtree=new A.Buf16(2*(2*J+1)),this.bl_tree=new A.Buf16(2*(2*Q+1)),i(this.dyn_ltree),i(this.dyn_dtree),i(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new A.Buf16(ee+1),this.heap=new A.Buf16(2*q+1),i(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new A.Buf16(2*q+1),i(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function v(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=G,t=e.state,t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?ie:ge,e.adler=2===t.wrap?0:1,t.last_flush=M,j._tr_init(t),k):o(e,D)}function S(e){var t=v(e);return t===k&&m(e.state),t}function E(e,t,r,n,i,s){if(!e)return D;var a=1;if(t===L&&(t=6),n<0?(a=0,n=-n):n>15&&(a=2,n-=16),i<1||i>Z||r!==Y||n<8||n>15||t<0||t>9||s<0||s>V)return o(e,D);8===n&&(n=9);var u=new b;return e.state=u,u.strm=e,u.wrap=a,u.gzhead=null,u.w_bits=n,u.w_size=1<<u.w_bits,u.w_mask=u.w_size-1,u.hash_bits=i+7,u.hash_size=1<<u.hash_bits,u.hash_mask=u.hash_size-1,u.hash_shift=~~((u.hash_bits+te-1)/te),u.window=new A.Buf8(2*u.w_size),u.head=new A.Buf16(u.hash_size),u.prev=new A.Buf16(u.w_size),u.lit_bufsize=1<<i+6,u.pending_buf_size=4*u.lit_bufsize,u.pending_buf=new A.Buf8(u.pending_buf_size),u.d_buf=1*u.lit_bufsize,u.l_buf=3*u.lit_bufsize,u.level=t,u.strategy=s,u.method=r,S(e)}var w,A=e("../utils/common"),j=e("./trees"),T=e("./adler32"),I=e("./crc32"),B=e("./messages"),M=0,O=1,x=3,C=4,F=5,k=0,R=1,D=-2,N=-3,U=-5,L=-1,P=1,W=2,z=3,V=4,H=0,G=2,Y=8,Z=9,X=15,K=8,q=286,J=30,Q=19,$=2*q+1,ee=15,te=3,re=258,oe=re+te+1,ne=32,ie=42,se=69,ae=73,ue=91,pe=103,ge=113,ce=666,le=1,he=2,fe=3,de=4,ye=3;w=[new _(0,0,0,0,function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(l(e),0===e.lookahead&&t===M)return le;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var o=e.block_start+r;if((0===e.strstart||e.strstart>=o)&&(e.lookahead=e.strstart-o,e.strstart=o,a(e,!1),0===e.strm.avail_out))return le;if(e.strstart-e.block_start>=e.w_size-oe&&(a(e,!1),0===e.strm.avail_out))return le}return e.insert=0,t===C?(a(e,!0),0===e.strm.avail_out?fe:de):(e.strstart>e.block_start&&(a(e,!1),e.strm.avail_out),le)}),new _(4,4,8,4,h),new _(4,5,16,8,h),new _(4,6,32,32,h),new _(4,4,16,16,f),new _(8,16,32,32,f),new _(8,16,128,128,f),new _(8,32,128,256,f),new _(32,128,258,1024,f),new _(32,258,258,4096,f)],r.deflateInit=function(e,t){return E(e,t,Y,X,K,H)},r.deflateInit2=E,r.deflateReset=S,r.deflateResetKeep=v,r.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?D:(e.state.gzhead=t,k):D},r.deflate=function(e,t){var r,a,g,c;if(!e||!e.state||t>F||t<0)return e?o(e,D):D;if(a=e.state,!e.output||!e.input&&0!==e.avail_in||a.status===ce&&t!==C)return o(e,0===e.avail_out?U:D);if(a.strm=e,r=a.last_flush,a.last_flush=t,a.status===ie)if(2===a.wrap)e.adler=0,u(a,31),u(a,139),u(a,8),a.gzhead?(u(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),u(a,255&a.gzhead.time),u(a,a.gzhead.time>>8&255),u(a,a.gzhead.time>>16&255),u(a,a.gzhead.time>>24&255),u(a,9===a.level?2:a.strategy>=W||a.level<2?4:0),u(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(u(a,255&a.gzhead.extra.length),u(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(e.adler=I(e.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=se):(u(a,0),u(a,0),u(a,0),u(a,0),u(a,0),u(a,9===a.level?2:a.strategy>=W||a.level<2?4:0),u(a,ye),a.status=ge);else{var l=Y+(a.w_bits-8<<4)<<8;l|=(a.strategy>=W||a.level<2?0:a.level<6?1:6===a.level?2:3)<<6,0!==a.strstart&&(l|=ne),l+=31-l%31,a.status=ge,p(a,l),0!==a.strstart&&(p(a,e.adler>>>16),p(a,65535&e.adler)),e.adler=1}if(a.status===se)if(a.gzhead.extra){for(g=a.pending;a.gzindex<(65535&a.gzhead.extra.length)&&(a.pending!==a.pending_buf_size||(a.gzhead.hcrc&&a.pending>g&&(e.adler=I(e.adler,a.pending_buf,a.pending-g,g)),s(e),g=a.pending,a.pending!==a.pending_buf_size));)u(a,255&a.gzhead.extra[a.gzindex]),a.gzindex++;a.gzhead.hcrc&&a.pending>g&&(e.adler=I(e.adler,a.pending_buf,a.pending-g,g)),a.gzindex===a.gzhead.extra.length&&(a.gzindex=0,a.status=ae)}else a.status=ae;if(a.status===ae)if(a.gzhead.name){g=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>g&&(e.adler=I(e.adler,a.pending_buf,a.pending-g,g)),s(e),g=a.pending,a.pending===a.pending_buf_size)){c=1;break}c=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,u(a,c)}while(0!==c);a.gzhead.hcrc&&a.pending>g&&(e.adler=I(e.adler,a.pending_buf,a.pending-g,g)),0===c&&(a.gzindex=0,a.status=ue)}else a.status=ue;if(a.status===ue)if(a.gzhead.comment){g=a.pending;do{if(a.pending===a.pending_buf_size&&(a.gzhead.hcrc&&a.pending>g&&(e.adler=I(e.adler,a.pending_buf,a.pending-g,g)),s(e),g=a.pending,a.pending===a.pending_buf_size)){c=1;break}c=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,u(a,c)}while(0!==c);a.gzhead.hcrc&&a.pending>g&&(e.adler=I(e.adler,a.pending_buf,a.pending-g,g)),0===c&&(a.status=pe)}else a.status=pe;if(a.status===pe&&(a.gzhead.hcrc?(a.pending+2>a.pending_buf_size&&s(e),a.pending+2<=a.pending_buf_size&&(u(a,255&e.adler),u(a,e.adler>>8&255),e.adler=0,a.status=ge)):a.status=ge),0!==a.pending){if(s(e),0===e.avail_out)return a.last_flush=-1,k}else if(0===e.avail_in&&n(t)<=n(r)&&t!==C)return o(e,U);if(a.status===ce&&0!==e.avail_in)return o(e,U);if(0!==e.avail_in||0!==a.lookahead||t!==M&&a.status!==ce){var h=a.strategy===W?y(a,t):a.strategy===z?d(a,t):w[a.level].func(a,t);if(h!==fe&&h!==de||(a.status=ce),h===le||h===fe)return 0===e.avail_out&&(a.last_flush=-1),k;if(h===he&&(t===O?j._tr_align(a):t!==F&&(j._tr_stored_block(a,0,0,!1),t===x&&(i(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),s(e),0===e.avail_out))return a.last_flush=-1,k}return t!==C?k:a.wrap<=0?R:(2===a.wrap?(u(a,255&e.adler),u(a,e.adler>>8&255),u(a,e.adler>>16&255),u(a,e.adler>>24&255),u(a,255&e.total_in),u(a,e.total_in>>8&255),u(a,e.total_in>>16&255),u(a,e.total_in>>24&255)):(p(a,e.adler>>>16),p(a,65535&e.adler)),s(e),a.wrap>0&&(a.wrap=-a.wrap),0!==a.pending?k:R)},r.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==ie&&t!==se&&t!==ae&&t!==ue&&t!==pe&&t!==ge&&t!==ce?o(e,D):(e.state=null,t===ge?o(e,N):k):D},r.deflateSetDictionary=function(e,t){var r,o,n,s,a,u,p,g,c=t.length;if(!e||!e.state)return D;if(r=e.state,2===(s=r.wrap)||1===s&&r.status!==ie||r.lookahead)return D;for(1===s&&(e.adler=T(e.adler,t,c,0)),r.wrap=0,c>=r.w_size&&(0===s&&(i(r.head),r.strstart=0,r.block_start=0,r.insert=0),g=new A.Buf8(r.w_size),A.arraySet(g,t,c-r.w_size,r.w_size,0),t=g,c=r.w_size),a=e.avail_in,u=e.next_in,p=e.input,e.avail_in=c,e.next_in=0,e.input=t,l(r);r.lookahead>=te;){o=r.strstart,n=r.lookahead-(te-1);do r.ins_h=(r.ins_h<<r.hash_shift^r.window[o+te-1])&r.hash_mask,r.prev[o&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=o,o++;while(--n);r.strstart=o,r.lookahead=te-1,l(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=te-1,r.match_available=0,e.next_in=u,e.input=p,e.avail_in=a,r.wrap=s,k},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":1,"./adler32":3,"./crc32":4,"./messages":6,"./trees":7}],6:[function(e,t,r){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],7:[function(e,t,r){"use strict";function o(e){for(var t=e.length;--t>=0;)e[t]=0}function n(e,t,r,o,n){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=o,this.max_length=n,this.has_stree=e&&e.length}function i(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function s(e){return e<256?te[e]:te[256+(e>>>7)]}function a(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function u(e,t,r){e.bi_valid>V-r?(e.bi_buf|=t<<e.bi_valid&65535,a(e,e.bi_buf),e.bi_buf=t>>V-e.bi_valid,e.bi_valid+=r-V):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function p(e,t,r){u(e,r[2*t],r[2*t+1])}function g(e,t){var r=0;do r|=1&e,e>>>=1,r<<=1;while(--t>0);return r>>>1}function c(e){16===e.bi_valid?(a(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}function l(e,t){var r,o,n,i,s,a,u=t.dyn_tree,p=t.max_code,g=t.stat_desc.static_tree,c=t.stat_desc.has_stree,l=t.stat_desc.extra_bits,h=t.stat_desc.extra_base,f=t.stat_desc.max_length,d=0;for(i=0;i<=z;i++)e.bl_count[i]=0;for(u[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<W;r++)(i=u[2*u[2*(o=e.heap[r])+1]+1]+1)>f&&(i=f,d++),u[2*o+1]=i,o>p||(e.bl_count[i]++,s=0,o>=h&&(s=l[o-h]),a=u[2*o],e.opt_len+=a*(i+s),c&&(e.static_len+=a*(g[2*o+1]+s)));if(0!==d){do{for(i=f-1;0===e.bl_count[i];)i--;e.bl_count[i]--,e.bl_count[i+1]+=2,e.bl_count[f]--,d-=2}while(d>0);for(i=f;0!==i;i--)for(o=e.bl_count[i];0!==o;)(n=e.heap[--r])>p||(u[2*n+1]!==i&&(e.opt_len+=(i-u[2*n+1])*u[2*n],u[2*n+1]=i),o--)}}function h(e,t,r){var o,n,i=new Array(z+1),s=0;for(o=1;o<=z;o++)i[o]=s=s+r[o-1]<<1;for(n=0;n<=t;n++){var a=e[2*n+1];0!==a&&(e[2*n]=g(i[a]++,a))}}function f(){var e,t,r,o,i,s=new Array(z+1);for(r=0,o=0;o<D-1;o++)for(oe[o]=r,e=0;e<1<<K[o];e++)re[r++]=o;for(re[r-1]=o,i=0,o=0;o<16;o++)for(ne[o]=i,e=0;e<1<<q[o];e++)te[i++]=o;for(i>>=7;o<L;o++)for(ne[o]=i<<7,e=0;e<1<<q[o]-7;e++)te[256+i++]=o;for(t=0;t<=z;t++)s[t]=0;for(e=0;e<=143;)$[2*e+1]=8,e++,s[8]++;for(;e<=255;)$[2*e+1]=9,e++,s[9]++;for(;e<=279;)$[2*e+1]=7,e++,s[7]++;for(;e<=287;)$[2*e+1]=8,e++,s[8]++;for(h($,U+1,s),e=0;e<L;e++)ee[2*e+1]=5,ee[2*e]=g(e,5);ie=new n($,K,N+1,U,z),se=new n(ee,q,0,L,z),ae=new n(new Array(0),J,0,P,H)}function d(e){var t;for(t=0;t<U;t++)e.dyn_ltree[2*t]=0;for(t=0;t<L;t++)e.dyn_dtree[2*t]=0;for(t=0;t<P;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*G]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function y(e){e.bi_valid>8?a(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function _(e,t,r,o){y(e),o&&(a(e,r),a(e,~r)),B.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}function m(e,t,r,o){var n=2*t,i=2*r;return e[n]<e[i]||e[n]===e[i]&&o[t]<=o[r]}function b(e,t,r){for(var o=e.heap[r],n=r<<1;n<=e.heap_len&&(n<e.heap_len&&m(t,e.heap[n+1],e.heap[n],e.depth)&&n++,!m(t,o,e.heap[n],e.depth));)e.heap[r]=e.heap[n],r=n,n<<=1;e.heap[r]=o}function v(e,t,r){var o,n,i,a,g=0;if(0!==e.last_lit)do o=e.pending_buf[e.d_buf+2*g]<<8|e.pending_buf[e.d_buf+2*g+1],n=e.pending_buf[e.l_buf+g],g++,0===o?p(e,n,t):(p(e,(i=re[n])+N+1,t),0!==(a=K[i])&&u(e,n-=oe[i],a),p(e,i=s(--o),r),0!==(a=q[i])&&u(e,o-=ne[i],a));while(g<e.last_lit);p(e,G,t)}function S(e,t){var r,o,n,i=t.dyn_tree,s=t.stat_desc.static_tree,a=t.stat_desc.has_stree,u=t.stat_desc.elems,p=-1;for(e.heap_len=0,e.heap_max=W,r=0;r<u;r++)0!==i[2*r]?(e.heap[++e.heap_len]=p=r,e.depth[r]=0):i[2*r+1]=0;for(;e.heap_len<2;)i[2*(n=e.heap[++e.heap_len]=p<2?++p:0)]=1,e.depth[n]=0,e.opt_len--,a&&(e.static_len-=s[2*n+1]);for(t.max_code=p,r=e.heap_len>>1;r>=1;r--)b(e,i,r);n=u;do r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],b(e,i,1),o=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=o,i[2*n]=i[2*r]+i[2*o],e.depth[n]=(e.depth[r]>=e.depth[o]?e.depth[r]:e.depth[o])+1,i[2*r+1]=i[2*o+1]=n,e.heap[1]=n++,b(e,i,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],l(e,t),h(i,p,e.bl_count)}function E(e,t,r){var o,n,i=-1,s=t[1],a=0,u=7,p=4;for(0===s&&(u=138,p=3),t[2*(r+1)+1]=65535,o=0;o<=r;o++)n=s,s=t[2*(o+1)+1],++a<u&&n===s||(a<p?e.bl_tree[2*n]+=a:0!==n?(n!==i&&e.bl_tree[2*n]++,e.bl_tree[2*Y]++):a<=10?e.bl_tree[2*Z]++:e.bl_tree[2*X]++,a=0,i=n,0===s?(u=138,p=3):n===s?(u=6,p=3):(u=7,p=4))}function w(e,t,r){var o,n,i=-1,s=t[1],a=0,g=7,c=4;for(0===s&&(g=138,c=3),o=0;o<=r;o++)if(n=s,s=t[2*(o+1)+1],!(++a<g&&n===s)){if(a<c){do p(e,n,e.bl_tree);while(0!=--a)}else 0!==n?(n!==i&&(p(e,n,e.bl_tree),a--),p(e,Y,e.bl_tree),u(e,a-3,2)):a<=10?(p(e,Z,e.bl_tree),u(e,a-3,3)):(p(e,X,e.bl_tree),u(e,a-11,7));a=0,i=n,0===s?(g=138,c=3):n===s?(g=6,c=3):(g=7,c=4)}}function A(e){var t;for(E(e,e.dyn_ltree,e.l_desc.max_code),E(e,e.dyn_dtree,e.d_desc.max_code),S(e,e.bl_desc),t=P-1;t>=3&&0===e.bl_tree[2*Q[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}function j(e,t,r,o){var n;for(u(e,t-257,5),u(e,r-1,5),u(e,o-4,4),n=0;n<o;n++)u(e,e.bl_tree[2*Q[n]+1],3);w(e,e.dyn_ltree,t-1),w(e,e.dyn_dtree,r-1)}function T(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return O;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return x;
for(t=32;t<N;t++)if(0!==e.dyn_ltree[2*t])return x;return O}function I(e,t,r,o){u(e,(F<<1)+(o?1:0),3),_(e,t,r,!0)}var B=e("../utils/common"),M=4,O=0,x=1,C=2,F=0,k=1,R=2,D=29,N=256,U=N+1+D,L=30,P=19,W=2*U+1,z=15,V=16,H=7,G=256,Y=16,Z=17,X=18,K=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],q=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],J=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Q=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],$=new Array(2*(U+2));o($);var ee=new Array(2*L);o(ee);var te=new Array(512);o(te);var re=new Array(256);o(re);var oe=new Array(D);o(oe);var ne=new Array(L);o(ne);var ie,se,ae,ue=!1;r._tr_init=function(e){ue||(f(),ue=!0),e.l_desc=new i(e.dyn_ltree,ie),e.d_desc=new i(e.dyn_dtree,se),e.bl_desc=new i(e.bl_tree,ae),e.bi_buf=0,e.bi_valid=0,d(e)},r._tr_stored_block=I,r._tr_flush_block=function(e,t,r,o){var n,i,s=0;e.level>0?(e.strm.data_type===C&&(e.strm.data_type=T(e)),S(e,e.l_desc),S(e,e.d_desc),s=A(e),n=e.opt_len+3+7>>>3,(i=e.static_len+3+7>>>3)<=n&&(n=i)):n=i=r+5,r+4<=n&&-1!==t?I(e,t,r,o):e.strategy===M||i===n?(u(e,(k<<1)+(o?1:0),3),v(e,$,ee)):(u(e,(R<<1)+(o?1:0),3),j(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),v(e,e.dyn_ltree,e.dyn_dtree)),d(e),o&&y(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(re[r]+N+1)]++,e.dyn_dtree[2*s(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){u(e,k<<1,3),p(e,G,$),c(e)}},{"../utils/common":1}],8:[function(e,t,r){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],"/lib/deflate.js":[function(e,t,r){"use strict";function o(e){if(!(this instanceof o))return new o(e);this.options=s.assign({level:l,method:f,chunkSize:16384,windowBits:15,memLevel:8,strategy:h,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new p,this.strm.avail_out=0;var r=i.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==c)throw new Error(u[r]);if(t.header&&i.deflateSetHeader(this.strm,t.header),t.dictionary){var n;if(n="string"==typeof t.dictionary?a.string2buf(t.dictionary):"[object ArrayBuffer]"===g.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(r=i.deflateSetDictionary(this.strm,n))!==c)throw new Error(u[r]);this._dict_set=!0}}function n(e,t){var r=new o(t);if(r.push(e,!0),r.err)throw r.msg||u[r.err];return r.result}var i=e("./zlib/deflate"),s=e("./utils/common"),a=e("./utils/strings"),u=e("./zlib/messages"),p=e("./zlib/zstream"),g=Object.prototype.toString,c=0,l=-1,h=0,f=8;o.prototype.push=function(e,t){var r,o,n=this.strm,u=this.options.chunkSize;if(this.ended)return!1;o=t===~~t?t:!0===t?4:0,"string"==typeof e?n.input=a.string2buf(e):"[object ArrayBuffer]"===g.call(e)?n.input=new Uint8Array(e):n.input=e,n.next_in=0,n.avail_in=n.input.length;do{if(0===n.avail_out&&(n.output=new s.Buf8(u),n.next_out=0,n.avail_out=u),1!==(r=i.deflate(n,o))&&r!==c)return this.onEnd(r),this.ended=!0,!1;0!==n.avail_out&&(0!==n.avail_in||4!==o&&2!==o)||("string"===this.options.to?this.onData(a.buf2binstring(s.shrinkBuf(n.output,n.next_out))):this.onData(s.shrinkBuf(n.output,n.next_out)))}while((n.avail_in>0||0===n.avail_out)&&1!==r);return 4===o?(r=i.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===c):2!==o||(this.onEnd(c),n.avail_out=0,!0)},o.prototype.onData=function(e){this.chunks.push(e)},o.prototype.onEnd=function(e){e===c&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=s.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=o,r.deflate=n,r.deflateRaw=function(e,t){return t=t||{},t.raw=!0,n(e,t)},r.gzip=function(e,t){return t=t||{},t.gzip=!0,n(e,t)}},{"./utils/common":1,"./utils/strings":2,"./zlib/deflate":5,"./zlib/messages":6,"./zlib/zstream":8}]},{},[])("/lib/deflate.js")})},function(e,t,r){function o(e){return e&&e.__esModule?e:{"default":e}}var n=r(92),s=o(n),a={setHeight:function u(){var e=$(".matter");e.height($(".swiper-container").height()-100)},initUI:function p(){var e=window.currentVideoItem,t=0,r=!0;for(var o in e.images){r&&($("#previewImage").css("background-image","url(data:image/png;base64,"+e.images[o]+")"),r=!1);var n=this.getImageSizeFromBase64Data(e.images[o]);t+=n.width*n.height*4;var i='<li id="'+o+'" ><a class="imageKeyA" href="javascript:;" data-role="showImage" data-imageid="'+o+'">'+o+" --- "+(0,s["default"])(n)+"</a></li>";$("#imageKeyListEnd").before(i)}$("#imageKeyListFirst").text("内存占用："+Math.round(t/1048576*100)/100+" M");var a={};a.version=e.version,a.FPS=e.FPS,a.frames=e.frames,a.videoSize=e.videoSize,$("#JSONDataDisplayer").html('<pre><code id="JSONDisplayView" class="json">'+(0,s["default"])(a,null,2)+"</code></pre>")},init:function g(){this.setHeight()},getImageSizeFromBase64Data:function c(e){var t=this.convertDataURIToBinary(e),r=256*t[18]+t[19],o=256*t[22]+t[23];return{width:r,height:o}},convertDataURIToBinary:function l(e){var t=window.atob(e),r=t.length,o=new Uint8Array(new ArrayBuffer(r));for(i=0;i<r;i++)o[i]=t.charCodeAt(i);return o}};a.init(),$(document).on("click",'[data-role="showImage"]',function(){var e=window.currentVideoItem;$("#previewImage").css("background-image","url(data:image/png;base64,"+e.images[$(this).attr("data-imageid")]+")"),$(".matter-right").find(".is-active").removeClass("is-active"),$(this).addClass("is-active")}),e.exports=a}]);