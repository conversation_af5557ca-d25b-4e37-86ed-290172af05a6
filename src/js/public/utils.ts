declare const document: any

const util = {
  sleep (delay: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, delay)
    })
  },
  loop (callback: Function) {
    requestAnimationFrame(animate)
    async function animate (time: number) {
      await callback(time)
      requestAnimationFrame(animate)
    }
  },
  publicPath (url: string): string {
    const meta: HTMLElement = document.querySelector('meta[name="publicPath"]') as HTMLElement
    const content: string = meta.getAttribute('content') || `//${location.host}`
    return `${content}/${url}`
  }
}

export default util
