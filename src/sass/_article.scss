@mixin article {
  section {
    &:nth-child(1) {
      @include title-block;
      h3 {
        max-width: 560px;
        margin: 0 auto;
        padding: 0;
      }
    }
    &:nth-child(2) {
      @include content-block;
      .col {
        float: none;
        padding: 0 12px;
      }
      li {
        list-style-type: none;
        padding: 0;
        border-top: 1px solid transparentize($color: #e0e0e0, $amount: .5);
        transition: border-color .3s;
        &:hover {
          &,
          & + li {
            border-top-color: transparentize($color: #A457D4, $amount: .5);
          }
        }
        a {
          display: block;
          padding: 15px 4px;
          h3 {
            font-weight: 400;
          }
          &, h3 {
            color: #666;
          }
          &:hover {
            &, h3 {
              color: #A457D4;
              text-decoration: none;
            }
          }
        }
      }
    }
    &:nth-child(2) {
      margin-top: -50px;
    }
  }
  @media (min-height: 1010px) and (min-width: 1200px) {
    & + footer {
      left: 0;
      bottom: 0;
      width: 100%;
      position: fixed;
    }
  }
}
