@mixin mobile {
  min-height: 480px;
  .center {
    max-width: 425px;
    position: relative;
    padding: 40px 0 0 0;
    // padding-top: 55px;
    // padding: 0 40px;
    @media (min-width: 375px) {
      padding: 40px 10px 0 10px;
    }
    @media (min-width: 414px) {
      padding: 50px 24px 0 24px;
    }
    h3 {
      top: 180px;
      left: 0;
      width: 100%;
      text-align: center;
      position: absolute;
      color: #fff;
      opacity: 0;
      pointer-events: none;
      @media (min-width: 375px) {
        top: 200px;
      }
      @media (min-width: 414px) {
        top: 220px;
      }
    }
    .canvas {
      height: 0;
      width: 100%;
      padding-top: 100%;
      position: relative;
      
    }
    canvas {
      top: 50%;
      left: 50%;
      transform: translate3d(-50%, -50%, 0);
      width: 100%;
      height: 100%;
      position: absolute;
      display: block;
      pointer-events: none;
      background-color: #000;
      transition: width .3s, height .3s;
    }
  }
  @media (min-height: 600px) {
    & + footer {
      left: 0;
      bottom: 0;
      width: 100%;
      position: fixed;
    }
  }
}
