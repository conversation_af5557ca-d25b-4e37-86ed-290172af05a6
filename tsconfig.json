{"compilerOptions": {"sourceMap": true, "target": "es6", "strict": true, "module": "esnext", "moduleResolution": "node", "lib": ["es6", "dom"], "types": [], "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "jsxFactory": "h", "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.vue"], "exclude": ["node_modules"]}