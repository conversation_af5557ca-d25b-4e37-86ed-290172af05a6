import isiOS from 'yypkg/is-ios'
import util from './public/utils'
import header from './module/header'
import index from './module/index'
import intro from './module/intro'
import mobile from './module/mobile'
import article from './module/article'
import designer from './module/designer'
import integrated from './module/integrated'
import preview from './module/svga-preview'

declare const window: any
declare const document: any

window.Promise = window.Promise || Promise
window.util = util
// window.yypkg = yypkg

if (isiOS) {
  document.documentElement.classList.add('ios')
}

header()
index()
intro()
mobile()
article()
designer()
integrated()
preview()
