@mixin intro {
  section {
    &:nth-child(1) {
      @include title-block;
      h3 {
        max-width: 560px;
        margin: 0 auto;
      }
    }
    &:nth-child(2),
    &:nth-child(3) {
      @include content-block;
      .col {
        float: none;
        max-width: 500px;
        padding: 0 12px;
        @media (min-width: 580px) {
          padding: 0 40px;
          max-width: 900px;
        }
        // @media (min-width: 768px) {
        //   padding: 0 40px;
        //   max-width: 900px;
        // }
      }
    }
    &:nth-child(2) {
      margin-top: -65px;
      @media (min-width: 768px) {
        margin-top: -50px;
      }
    }
    &:nth-child(3) {
      margin-top: -50px;
    }
    &:nth-child(2) {
      a {
        white-space: nowrap;
        margin-left: 6px;
        text-decoration: none;
        font-weight: 400;
        border-bottom: 1px solid currentColor;
      }
    }
  }
  @media (min-height: 1010px) and (min-width: 1200px) {
    & + footer {
      left: 0;
      bottom: 0;
      width: 100%;
      position: fixed;
    }
  }
}
