import util from '../public/utils'

declare const window: any
declare const document: any

export default function index () {

  const index = document.querySelector('#index')

  if (!index) {
    return
  }

  /**
   * hack
   * 并行下载 导致 嫦娥 占用 天使 带宽
   * 往往 嫦娥 比 天使先出现
   * 调整为串行 优先 显示 首屏动画
   */

  // 天使动画
  const parser = new window.SVGA.Parser()
  const web = new window.SVGA.Player('#index-web')
  const pad = new window.SVGA.Player('#index-pad')
  const phone = new window.SVGA.Player('#index-phone')
  parser.load(util.publicPath('assets/svga/index-response.svga'), async (item: any) => {
    web.setVideoItem(item)
    web.startAnimation()
    pad.setVideoItem(item)
    pad.startAnimation()
    phone.setVideoItem(item)
    phone.startAnimation()

    {
      // 嫦娥动画
      const example: HTMLElement = document.querySelector('#index-example') as HTMLElement
      const parent: HTMLElement = example.parentNode as HTMLElement
      util.loop(() => {
        const size: number = Math.min(parent.clientWidth / example.clientWidth, 1)
        example.style['transform'] = `translate3d(-50%, -50%, 0) scale(${size}, ${size})`
      })
      const parser = new window.SVGA.Parser()
      const player = new window.SVGA.Player('#index-example')
      parser.load(util.publicPath('assets/svga/index-example.svga'), async (item: any) => {
        player.setVideoItem(item)
        player.startAnimation()
        await util.sleep(0)
        example.style['opacity'] = `1`
      })
    }

  })

}
