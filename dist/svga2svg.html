<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>" class="zh"><head><link rel="stylesheet" href="https://svga.dev/assets/v1/main-2018.css?h=7286240e0b0e12dea64b">
<style>
    .preview-main.ext-bg {
        background-image: url("https://svga.dev//svga.dev/assets/v1/frame.png");
    }

    .preview-main.ext-bg.is-active {
        background-image: url("https://svga.dev//svga.dev/assets/v1/frame-active.png");
    }

    .popbox-close {
        background-image: url("https://svga.dev//svga.dev/assets/v1/img-close.png");
    }
</style>
<link rel="stylesheet" href="https://svga.dev/assets/swiper-3.4.2.min.css?h=7286240e0b0e12dea64b">
<link rel="stylesheet" href="https://svga.dev//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/styles/default.min.css?h=7286240e0b0e12dea64b">

    
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  <!-- SEO 基础标签 -->
  <title>SVGA - 跨平台高性能动画解决方案 | iOS/Android/Flutter/Web/HarmonyOS动画格式</title>
  <meta name="description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。支持After Effects和Animate导出，动画文件体积小，播放资源占用优，设计师和开发者友好的跨平台动画解决方案。">
  <meta name="keywords" content="SVGA, 直播礼物动画, 高性能动画, 跨平台动画方案, 矢量动画, 透明MP4, After Effects, Animate, iOS动画, Android动画, Flutter动画, Web动画, 动画格式, 开源动画库">
  <meta name="author" content="SVGA Team">
  <meta name="robots" content="index, follow, max-snippet:160, max-image-preview:large, max-video-preview:-1">
  <meta name="publicPath" content="//svga.dev">
  
  <!-- 规范化URL -->
  <link rel="canonical" href="https://svga.dev/">
  
  <!-- 网站图标 -->
  <link rel="icon" type="image/x-icon" href="//svga.dev/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="//svga.dev/favicon.ico">
  
  <!-- Open Graph 标签 (Facebook) -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta property="og:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta property="og:url" content="https://svga.dev/">
  <meta property="og:site_name" content="SVGA">
  <meta property="og:image" content="https://svga.dev/img/index-example.jpg?h=9f9c2b15134402c14ebe">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="SVGA动画效果展示">
  <meta property="og:locale" content="zh_CN">
  
  <!-- Twitter Card 标签 -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta name="twitter:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta name="twitter:image" content="https://svga.dev/img/index-example.jpg?h=9f9c2b15134402c14ebe">
  <meta name="twitter:image:alt" content="SVGA动画效果展示">
  
  <!-- 语言和地区 -->
  <meta name="language" content="zh-CN">
  <meta name="geo.region" content="CN">
  
  <!-- 主题颜色 -->
  <meta name="theme-color" content="#4A90E2">
  <meta name="msapplication-TileColor" content="#4A90E2">
  
  <!-- 结构化数据 (JSON-LD) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "SVGA",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。",
    "url": "https://svga.dev/",
    "author": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://github.com/svga"
    },
    "operatingSystem": ["iOS", "Android", "Web", "Flutter"],
    "applicationCategory": "DeveloperApplication",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "downloadUrl": "https://github.com/svga",
    "screenshot": "https://svga.dev/img/index-example.jpg",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "500"
    },
    "programmingLanguage": ["JavaScript", "Swift", "Kotlin", "Dart"],
    "codeRepository": "https://github.com/svga"
  }
  </script>
  
  <!-- 面包屑导航结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "首页",
        "item": "https://svga.dev/"
      }
    ]
  }
  </script>
  
  <!-- DNS预取 -->
  <link rel="dns-prefetch" href="//github.com">
  <link rel="dns-prefetch" href="//pagead2.googlesyndication.com">
  
  <!-- 样式表 -->
  <!-- <style>canvas, header input { display: none; }</style> -->
  <link rel="stylesheet" href="//svga.dev/assets/font/amplesoft-bold.css?h=9f9c2b15134402c14ebe">
  <link rel="stylesheet" href="//svga.dev/css/main.css?h=9f9c2b15134402c14ebe">
  
  <!-- Google AdSense -->
  <script async="" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1514042314776212" crossorigin="anonymous"></script>
<style>
    header {
        z-index: 99;
    }

    .swiper-slide {
        background-color: #fff;
    }

    .preview-controls {
        width: 800px;
        overflow: visible;
    }

    .preview-controls__upload a,
    .preview-controls__download a {
        width: auto;
        min-width: 130px;
        padding: 0 5px;
    }

    .matter-info {
        overflow: scroll;
    }
</style></head>



<body ontouchstart="">
    <header>
  <div class="center">
    <h2><a href="./index.html">SVGA</a></h2>
    <input type="checkbox" name="checkbox" id="checkbox">
    <div class="icon">
      <i></i>
      <i></i>
      <i></i>
      <i></i>
      <i></i>
    </div>
    <nav>
      <div class="tab">
        <div class="item">
          <div class="label">
            <a href="./intro.html"><em data-i18n="header-nav-0">介绍</em></a>
          </div>
        </div>
        <div class="item">
          <div class="label">
            <a href="./integrated.html"><em data-i18n="header-nav-1">集成指南</em></a>
          </div>
        </div>
        <div class="item">
          <div class="list">
            <div class="item">
              <div class="label">
                <a target="_blank" href="https://github.com/svga"><em data-i18n="header-nav-3">开源代码</em></a>
              </div>
            </div>
            <div class="item">
              <div class="label">
                <a href="./designer.html"><em data-i18n="header-nav-5">设计师工具</em></a>
              </div>
            </div>
            <div class="item pc-show mb-hide">
              <div class="label">
                <a href="./svga-preview.html"><em data-i18n="header-nav-6">在线播放器</em></a>
              </div>
            </div>
            <!-- <div class="item svgatools">
              <div class="label">
                <a href="https://www.nangonghan.com/svga/"><em data-i18n="header-nav-8">SVGA Tools</em></a>
              </div>
            </div> -->
            <div class="item">
              <div class="label">
                <a href="./article.html"><em data-i18n="header-nav-7">资源文章</em></a>
              </div>
            </div>
          </div>
          <div class="label"><em data-i18n="header-nav-2">社区生态</em></div>
        </div>
        <div class="item">
          <div class="list">
            <div class="item">
              <div class="label">
                <a href="//svga.dev/en/index.html" data-lang="en"><em>English</em></a>
              </div>
            </div>
            <div class="item">
              <div class="label">
                <a href="//svga.dev/index.html" data-lang="zh"><em>中文</em></a>
              </div>
            </div>
          </div>
          <div class="label"><em data-i18n="header-language">多语言</em></div>
        </div>
      </div>
    </nav>
  </div>
</header>


    <article id="preview">
        <div class="center">
            <!-- @extends prev version -->
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <div class="preview">
                            <div id="preview-main" class="preview-main ext-bg" ondrop="dropHandler(event);" ondragover="dragOverHandler(event);">
                                <p id="preview-text-0" data-i18n="preview-text-0">体验预览 SVGA 动画，请将文件拖拽到此区域</p>
                                <div id="previewDemo" style="background-color: black; width: 0px"></div>
                            </div>
                            <div id="colorPicker" class="color-picker-button-group" style="display:none">
                                <a id="color-picker-button-1" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-2" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-3" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-4" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-5" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-6" href="javascript:;" data-role="change-color"></a>
                            </div>
                            <div id="preview-controls" class="preview-controls" style="display: none">
                                <div class="preview-controls__btn preview-controls__upload">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-1" style="width: 160px" onclick="downloadSVG()">选择文件</a>
                                </div>
                                <div class="preview-controls__btn preview-controls__download">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-3" style="width: 120px" onclick="changeBackgroundColor()" id="settingBackgroundColor">下载案例</a>
                                </div>
                                <div class="preview-controls__btn preview-controls__download">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-3" style="width: 120px" onclick="changeLoopCount()" id="settingLoopCount">下载案例</a>
                                </div>
                                <div class="preview-controls__btn preview-controls__download">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-3" style="width: 120px" onclick="changeFillMode()" id="settingFillMode">下载案例</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide">
                        <div class="matter">
                            <div class="matter-left">
                                <div class="matter-preview" id="previewImage"></div>
                                <div class="matter-info">
                                    <div id="JSONDataDisplayer"></div>
                                </div>
                            </div>
                            <div class="matter-right">
                                <ul>
                                    <li id="imageKeyListFirst">&nbsp;</li>
                                    <li id="imageKeyListEnd">&nbsp;</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 如果需要导航按钮 -->
                <div id="swiperButtonPrev" class="swiper-button-prev" style="display:none"></div>
            </div>
        </div>
    </article>
    <footer>
  <div class="center">
    
    <!-- 友情链接区域 -->
    <section aria-label="友情链接" class="partner-links" style="text-align: center; margin:5px 0 0;">
      <nav aria-label="合作伙伴链接">
        <ul style="display: inline-block; margin: 0; padding: 0; list-style: none;">
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://codefont.dev" title="CodeFont - Font Switching Made Simple for VS Code" target="_blank" rel="noopener" aria-label="Code Font">CodeFont</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://sline.dev" title="Sline - SHOPLINE Custom-Built E-commerce Template Engine" target="_blank" rel="noopener" aria-label="Sline">Sline</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://colorcode.cc" title="ColorCode - 专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。支持 OKLCH、LAB、CMYK 等专业格式，ΔE≤0.5 精度保障" target="_blank" rel="noopener" aria-label="ColorCode">ColorCode</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://zartify.com" title="Zartify - Premium HD Classic Paintings for Prints, Displays &amp; Inspiration" target="_blank" rel="noopener" aria-label="Zartify">Zartify</a>
          </li>
        </ul>
      </nav>
    </section>
    
    <p>Copyright © 2016-2025 <a href="https://svga.dev" rel="noopener">SVGA Team</a></p>
  </div>
  
  <!-- 组织机构结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "SVGA Team",
    "url": "https://svga.dev",
    "logo": "https://svga.dev/favicon.ico",
    "description": "SVGA是一个开源的跨平台动画解决方案团队",
    "foundingDate": "2016",
    "sameAs": [
      "https://github.com/svga"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "technical support",
      "url": "https://github.com/svga/docs/issues"
    }
  }
  </script>
  
  <!-- 网站结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "SVGA",
    "url": "https://svga.dev",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式",
    "inLanguage": "zh-CN",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://github.com/svga/docs/issues?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://svga.dev"
    }
  }
  </script>
</footer>
    <script src="//svga.dev/assets/jszip.min.js?h=9f9c2b15134402c14ebe"></script>
<script src="//svga.dev/assets/svga.min.js?h=9f9c2b15134402c14ebe"></script>
<script src="//svga.dev/js/main.js?h=9f9c2b15134402c14ebe"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-8307426-22"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-8307426-22');
</script>
<script src="https://analytics.ahrefs.com/analytics.js?h=9f9c2b15134402c14ebe" data-key="LPY8uaT1uYB8yUgv+xE/Uw" async=""></script>

    <script src="//svga.dev/assets/jquery.min.js?h=9f9c2b15134402c14ebe"></script>
    <script src="//svga.dev/assets/jimp.min.js?h=9f9c2b15134402c14ebe"></script>
    <script src="//svga.dev/assets/main.browser.js?h=9f9c2b15134402c14ebe"></script>

    <script>

        let currentFile;
        let currentOut;
        let transformSetting = {
            backgroundColor: "transparent",
            loopCount: 0,
            fillMode: "forward",
        }

        function dropHandler(ev) {
            ev.preventDefault();
            if (ev.dataTransfer.items) {
                for (var i = 0; i < ev.dataTransfer.items.length; i++) {
                    if (ev.dataTransfer.items[i].kind === 'file') {
                        currentFile = ev.dataTransfer.items[i].getAsFile();
                        break;
                    }
                }
            } else {
                for (var i = 0; i < ev.dataTransfer.files.length; i++) {
                    currentFile = ev.dataTransfer.files[i];
                    break;
                }
            }
            if (currentFile !== undefined) {
                tiktok.transformFile(currentFile, transformSetting).then((out) => {
                    currentOut = out;
                    startTransform()
                })
            }
        }

        function startTransform() {
            tiktok.transformFile(currentFile, transformSetting).then((out) => {
                currentOut = out;
                displayOut()
            })
        }

        function displayOut() {
            $('#preview-main').height("unset")
            $('#preview-text-0').hide()
            $('#previewDemo').html(currentOut).width("100%")
            $('#preview-controls').show()
        }

        function dragOverHandler(ev) {
            ev.preventDefault();
        }

        function downloadSVG() {
            var saveData = (function () {
                var a = document.createElement("a");
                document.body.appendChild(a);
                a.style = "display: none";
                return function (fileName) {
                    var blob = new Blob([currentOut], { type: "octet/stream" }),
                        url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = fileName;
                    a.click();
                    window.URL.revokeObjectURL(url);
                };
            }());
            saveData(new Date().getTime() + ".svg");
        }

        function changeBackgroundColor() {
            let newValue = prompt("请输入背景色", transformSetting.backgroundColor)
            if (newValue) {
                transformSetting.backgroundColor = newValue;
                $('#settingBackgroundColor').text(newValue)
                startTransform()
            }
        }

        function changeLoopCount() {
            let newValue = prompt("请输入动画循环次数（ 0 代表无限循环）", transformSetting.loopCount)
            if (newValue) {
                transformSetting.loopCount = newValue;
                $('#settingLoopCount').text(newValue === 0 ? "无限循环" : "循环" + newValue.toString() + "次")
                startTransform()
            }
        }

        function changeFillMode() {
            let newValue = prompt("请输入动画结束时的停留画面；终于首帧 = backward；终于末帧 = forward；清空 = clear", transformSetting.fillMode)
            if (newValue) {
                if (newValue !== "backward" && newValue !== "forward；清空" && newValue !== "clear") {
                    alert("非法值")
                    return;
                }
                transformSetting.fillMode = newValue;
                if (newValue === "backward") {
                    $('#settingFillMode').text("终于首帧")
                }
                else if (newValue === "forward") {
                    $('#settingFillMode').text("终于末帧")
                }
                else if (newValue === "clear") {
                    $('#settingFillMode').text("清空")
                }
                startTransform()
            }
        }

    </script>




</body></html>