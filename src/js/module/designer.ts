declare const document: any
declare const process: any

export default function designer () {

  const designer = document.querySelector('#designer')

  if (!designer) {
    return
  }

//   const url = `//svga.dev/docute/#/Plug-in?id=animate`
//   const reg = /svga\.io/

//   const sameOrigin = reg.test(location.host)

//   if (sameOrigin || process.env === 'dev') {

//     const iframe: HTMLIFrameElement = document.querySelector('iframe') as HTMLIFrameElement
//     iframe.onload = () => {
//       if (sameOrigin) {
//         const window: Window = iframe.contentWindow as Window
//         const list: NodeList = window.document.querySelectorAll('header, .sidebar, .sidebar-toggle, .markdown-body>*:not(table)')
//         {
//           [].forEach.call(list, function (el: Element) {
//             el.remove()
//           })
//         }
//         {
//           const el: HTMLElement =  window.document.querySelector('.main') as HTMLElement
//           el.style['width'] = `auto`
//           el.style['padding'] = `0`
//         }
//         {
//           (window.document.querySelector('.content-wrap') as HTMLElement).style['height'] = `auto`
//         }
//         {
//           (window.document.querySelector('.markdown-body') as HTMLElement).style['margin'] = `0`
//         }
//       }
//       iframe.style['visibility'] = 'visible'
//     }
//     iframe.src = url
//   }

}
