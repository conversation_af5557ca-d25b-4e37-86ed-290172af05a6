{"index-title-0": {"en": "SVGA Animation Format", "zh": "SVGA动画格式"}, "index-title-1": {"en": "provides high performance animation play experience", "zh": "高性能动画播放体验"}, "index-title-2": {"en": "SVGA is compatible with iOS / Android / Flutter / Web / HarmonyOS.", "zh": "SVGA 是一种同时兼容 iOS / Android / Flutter / Web / HarmonyOS 多个平台的动画格式。"}, "index-title-button": {"en": "Start Animation", "zh": "立即体验"}, "index-reason-0": {"en": "Why SVGA？", "zh": "为什么选 SVGA？"}, "index-reason-1": {"en": "Friendly to the Developer", "zh": "对开发者友好"}, "index-reason-2": {"en": "The convenient SDK enables SVGA to run on different platforms. The integration step is easy and easy.", "zh": "便捷的 SDK 使得 SVGA 可运行在不同平台上，集成步骤轻松简单。"}, "index-reason-3": {"en": "Friendly to the Designer", "zh": "对设计师友好"}, "index-reason-4": {"en": "Designers can use After Effects or Animate(Flash) for animation design, and SVGA can support most of them.  Animation files can be converted to SVGA file by using the tools.", "zh": "你可以使用 After Effects 或是 Animate(Flash) 进行动画设计，SVGA 可以支持其中的大部分效果，设计师使用导出工具即可生成动画文件。"}, "index-reason-5": {"en": "Cost-Effective", "zh": "性价比更高"}, "index-reason-6": {"en": "The smaller animation files, the higher performance animation play will provide, the more realistic animation effects will be.", "zh": "动画文件体积更小，播放资源占用更优，动画还原效果更好。"}, "index-about-0": {"en": "More", "zh": "关于 SVGA"}, "index-about-1": {"en": "Integration Guide", "zh": "集成指南"}, "index-about-2": {"en": "You can easily introduce SVGA player and play SVGA source file in iOS / Android / Flutter / Web / HarmonyOS by Docs.", "zh": "轻松在 iOS / Android / Flutter / Web / HarmonyOS 中引入 SVGA 播放器并播放资源文件。"}, "index-about-3": {"en": "Tools", "zh": "设计师工具"}, "index-about-4": {"en": "You can easily convert the aep or fla animation source file into SVGA resource files by Tools.", "zh": "使用设计师工具，可以轻松的将 Aep 或 Fla 格式的动画文件导出成 SVGA 格式资源文件。"}, "index-about-5": {"en": "Open Source", "zh": "开源代码"}, "index-about-6": {"en": "The converter and players are open source and you're welcome to submit your work", "zh": "我们的转换器和播放器都是开源的，欢迎提交 PR 贡献代码。"}, "index-about-7": {"en": "Support", "zh": "技术支持"}, "index-about-8": {"en": "If any problem, it's welcome to open", "zh": "在使用过程中，如有任何问题欢迎提交"}, "index-about-9": {"en": "issue", "zh": "issue"}, "index-about-10": {"en": ". Or join our QQ group at", "zh": "或者加入我们的 QQ 群参与讨论："}, "index-about-11": {"en": ".", "zh": "。"}}