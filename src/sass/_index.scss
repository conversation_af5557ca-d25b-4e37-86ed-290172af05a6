@mixin index {
  .container {
    &::before {
      top: 0;
      left: 12px;
      right: 12px;
      height: 1px;
      content: "";
      position: absolute;
      background-color: #e0e0e0;
    }
  }
  section {
    h2 {
      font-size: 30px;
      font-weight: 400;
      line-height: 1.2;
      @media (min-width: 426px) {
        font-size: 30px;
        font-weight: 400;
      }
    }
    &:first-child {
      .container {
        &::before {
          display: none;
        }
      }
    }
  }
  section {
    &:nth-child(1) {
      .container {
        padding: 30px 0 64px;
        @media (min-width: 960px) {
          padding: 96px 0;
        }
      }
      .left {
        @media (min-width: 960px) {
          width: 400px;
        }
      }
      .right {
        height: 180px;
        overflow: hidden;
        @media (min-width: 320px) {
          height: 180px;
        }
        @media (min-width: 375px) {
          height: 220px;
        }
        @media (min-width: 425px) {
          height: 250px;
        }
        @media (min-width: 960px) {
          float: none;
          height: 330px;
          margin-bottom: 10px;
        }
        @media (min-width: 1140px) {
          height: 430px;
          margin-bottom: -90px;
        }
      }
      h2 {
        margin-top: -12px;
        margin-bottom: 5px;
      }
      h3 {
        font-size: 26px;
        line-height: 1.2;
        font-weight: normal;
        margin-bottom: 10px;
        @media (min-width: 426px) {
          font-size: 30px;
        }
      }
      p {
        margin-bottom: 20px;
        &:last-child {
          margin-bottom: 0;
        }
      }
      a {
        display: inline-block;
        @media (min-width: 425px) {
          margin-right: 10px;
        }
        @media (min-width: 960px) {
          margin-top: 24px;
        }
        &:last-child {
          margin-right: 0;
        }
      }
      svg {
        width: 1.6em;
        height: 1.6em;
        display: inline-block;
        margin-right: .3em;
        position: relative;
        top: .4em;
      }
      figure {
        .response {
          transform: scale(.35);
        }
        @media (min-width: 320px) {
          .response {
            transform: scale(.35);
          }
        }
        @media (min-width: 375px) {
          .response {
            transform: scale(.43);
          }
        }
        @media (min-width: 425px) {
          .response {
            transform: scale(.49);
          }
        }
        @media (min-width: 960px) {
          margin: 0 auto;
          width: 510px;
          .response {
            transform: scale(.65);
          }
        }
        @media (min-width: 1140px) {
          width: 650px;
          .response {
            transform: scale(.85);
          }
        }
      }
    }
    &:nth-child(2) {
      .col {
        &:first-child {
          display: none;
          @media (min-width: 960px) {
            display: block;
          }
        }
      }
      .left {
        @media (min-width: 960px) {
          width: 40%;
        }
      }
      .right {
        @media (min-width: 960px) {
          width: 60%;
        }
      }
      h2 {
        margin-top: -12px;
        margin-bottom: 50px;
        @media (min-width: 960px) {
          margin-top: 0;
        }
      }
      h3 {
        margin-bottom: 5px;
      }
      p {
        margin-bottom: 56px;
        &:last-child {
          margin-bottom: 0;
        }
      }
      figure {
        top: 0;
        width: 100%;
        max-width: 367px;
        position: relative;
        margin-bottom: 70px;
        @media (min-width: 960px) {
          top: -120px;
          margin-bottom: -120px;
        }
      }
      .canvas {
        top: 16%;
        left: 0;
        width: 100%;
        padding-bottom: 100%;
        position: absolute;
      }
      canvas {
        top: 50%;
        left: 50%;
        width: 360px;
        height: 360px;
        opacity: 0;
        transition: opacity .3s;
        position: absolute;
        pointer-events: none;
      }
    }
    &:nth-child(3) {
      h2 {
        margin-top: -12px;
        margin-bottom: 30px;
        @media (min-width: 960px) {
          margin-bottom: 75px;
        }
      }
      .col {
        margin-bottom: 40px;
        &:last-child {
          margin-bottom: 0;
        }
        @media (min-width: 768px) {
          float: left;
          width: 50%;
        }
        @media (min-width: 960px) {
          width: 25%;
          margin-bottom: 0;
        }
      }
      .icon {
        margin-bottom: 5px;
      }
      h3 {
        margin-bottom: 10px;
      }
      p {
        max-width: 270px;
        margin: 0 auto;
        a {
          padding: 0 3px;
        }
      }
    }
  }
  .demo {
    min-width: 120px;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    border-radius: 3px;
    border: 0;
    background-color: #a457d4;
    color: #fff;
    text-align: center;
    cursor: pointer;
    user-select: none;
    padding: 0px 8px;
    &:hover,
    &:active {
      background-color: #9832d8;
    }
    // @media (min-width: 375px) {
    //   min-width: 148px;
    //   height: 56px;
    //   line-height: 56px;
    //   font-size: 20px;
    //   border-radius: 6px;
    // }
    display: none;
    @media (min-width: 425px) {
      display: inline-block;
    }
  }
  .github {
    width: 120px;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    border-radius: 24px;
    border: 0;
    background-color: #f6f6f6;
    color: #7f8c8d;
    text-align: center;
    cursor: pointer;
    user-select: none;
    // @media (min-width: 375px) {
    //   width: 148px;
    //   height: 56px;
    //   line-height: 56px;
    //   font-size: 20px;
    //   border-radius: 28px;
    // }
  }
  .response {
    width: 755px;
    height: 505px;
    position: relative;
    transform-origin: left top;
  }
  .web,
  .pad,
  .phone {
    cursor: pointer;
    position: absolute;
    background-size: contain;
    background-repeat: no-repeat;
    &:hover,
    &:active {
      .active {
        opacity: 1;
      }
    }
    .active {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      position: absolute;
      pointer-events: none;
      background-size: contain;
      background-repeat: no-repeat;
    }
    canvas {
      position: absolute;
      pointer-events: none;
    }
  }
  .web {
    top: 50px;
    width: 404px;
    height: 308px;
    background-image: url("../img/index-web-normal.png");
    .active {
      background-image: url("../img/index-web-hover.png");
    }
    canvas {
      top: -30px;
      left: 0;
      width: 400px;
      height: 400px;
    }
  }
  .pad {
    top: 290px;
    left: 430px;
    width: 317px;
    height: 204px;
    background-image: url("../img/index-pad-normal.png");
    .active {
      background-image: url("../img/index-pad-hover.png");
    }
    canvas {
      top: -20px;
      left: 30px;
      width: 250px;
      height: 250px;
    }
  }
  .phone {
    top: 0;
    left: 430px;
    width: 135px;
    height: 261px;
    background-image: url("../img/index-phone-normal.png");
    .active {
      background-image: url("../img/index-phone-hover.png");
    }
    canvas {
      top: 60px;
      left: 0;
      width: 130px;
      height: 130px;
    }
  }
  .icon {
    height: 110px;
    i {
      display: block;
      margin: 0 auto;
      position: relative;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center center;
    }
  }
  .icon0 {
    i {
      top: 25px;
      width: 77px;
      height: 63px;
      background-image: url("../img/index-about0.png");
    }
  }
  .icon1 {
    i {
      top: 10px;
      width: 70px;
      height: 84px;
      background-image: url("../img/index-about1.png");
    }
  }
  .icon2 {
    i {
      top: 22px;
      width: 77px;
      height: 67px;
      background-image: url("../img/index-about2.png");
    }
  }
  .icon3 {
    i {
      top: 22px;
      width: 67px;
      height: 67px;
      background-image: url("../img/index-about3.png");
    }
  }
}
