<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<link rel="stylesheet" href="https://svga.dev/assets/v1/main-2018.css?h=7286240e0b0e12dea64b">
<style>
    .preview-main.ext-bg {
        background-image: url("https://svga.dev/assets/v1/frame.png");
    }

    .preview-main.ext-bg.is-active {
        background-image: url("https://svga.dev/assets/v1/frame-active.png");
    }

    .popbox-close {
        background-image: url("https://svga.dev/assets/v1/img-close.png");
    }
</style>
<link rel="stylesheet" href="https://svga.dev/assets/swiper-3.4.2.min.css?h=7286240e0b0e12dea64b">
<link rel="stylesheet"
    href="https://svga.dev//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/styles/default.min.css?h=7286240e0b0e12dea64b">

    <%- include('./template/head.ejs') %>

<style>
    header {
        z-index: 99;
    }

    .swiper-slide {
        background-color: #fff;
    }

    .preview-controls {
        width: 800px;
        overflow: visible;
    }

    .preview-controls__upload a,
    .preview-controls__download a {
        width: auto;
        min-width: 130px;
        padding: 0 5px;
    }

    .matter-info {
        overflow: scroll;
    }
</style>

<body ontouchstart="">
    <%- include('./template/header.ejs') %>

    <article id="preview">
        <div class="center">
            <!-- @extends prev version -->
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <div class="preview">
                            <div id="preview-main" class="preview-main ext-bg" ondrop="dropHandler(event);"
                                ondragover="dragOverHandler(event);">
                                <p id="preview-text-0" data-i18n="preview-text-0">将 SVGA 转换成 SVG 文件，请将文件拖拽到此区域。</p>
                                <div id="previewDemo" style="background-color: black; width: 0px"></div>
                            </div>
                            <div id="colorPicker" class="color-picker-button-group" style="display:none">
                                <a id="color-picker-button-1" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-2" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-3" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-4" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-5" href="javascript:;" data-role="change-color"></a>
                                <a id="color-picker-button-6" href="javascript:;" data-role="change-color"></a>
                            </div>
                            <div id="preview-controls" class="preview-controls" style="display: none">
                                <div class="preview-controls__btn preview-controls__upload">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-1"
                                        style="width: 160px" onclick="downloadSVG()">下载 SVG 文件</a>
                                </div>
                                <div class="preview-controls__btn preview-controls__download">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-3"
                                        style="width: 120px" onclick="changeBackgroundColor()"
                                        id="settingBackgroundColor">透明</a>
                                </div>
                                <div class="preview-controls__btn preview-controls__download">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-3"
                                        style="width: 120px" onclick="changeLoopCount()" id="settingLoopCount">无限循环</a>
                                </div>
                                <div class="preview-controls__btn preview-controls__download">
                                    <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-3"
                                        style="width: 120px" onclick="changeFillMode()" id="settingFillMode">终于末帧</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide">
                        <div class="matter">
                            <div class="matter-left">
                                <div class="matter-preview" id="previewImage"></div>
                                <div class="matter-info">
                                    <div id="JSONDataDisplayer"></div>
                                </div>
                            </div>
                            <div class="matter-right">
                                <ul>
                                    <li id="imageKeyListFirst">&nbsp;</li>
                                    <li id="imageKeyListEnd">&nbsp;</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 如果需要导航按钮 -->
                <div id="swiperButtonPrev" class="swiper-button-prev" style="display:none"></div>
            </div>
        </div>
    </article>
    <%- include('./template/footer.ejs') %>
    <%- include('./template/script.ejs') %>
    <script src="/assets/jquery.min.js"></script>
    <script src="/assets/jimp.min.js"></script>
    <script src="/assets/main.browser.js"></script>

    <script>

        let currentFile;
        let currentOut;
        let transformSetting = {
            backgroundColor: "transparent",
            loopCount: 0,
            fillMode: "forward",
        }

        function dropHandler(ev) {
            ev.preventDefault();
            if (ev.dataTransfer.items) {
                for (var i = 0; i < ev.dataTransfer.items.length; i++) {
                    if (ev.dataTransfer.items[i].kind === 'file') {
                        currentFile = ev.dataTransfer.items[i].getAsFile();
                        break;
                    }
                }
            } else {
                for (var i = 0; i < ev.dataTransfer.files.length; i++) {
                    currentFile = ev.dataTransfer.files[i];
                    break;
                }
            }
            if (currentFile !== undefined) {
                tiktok.transformFile(currentFile, transformSetting).then((out) => {
                    currentOut = out;
                    startTransform()
                })
            }
        }

        function startTransform() {
            tiktok.transformFile(currentFile, transformSetting).then((out) => {
                currentOut = out;
                displayOut()
            })
        }

        function displayOut() {
            $('#preview-main').height("unset")
            $('#preview-text-0').hide()
            $('#previewDemo').html(currentOut).width("100%")
            $('#preview-controls').show()
        }

        function dragOverHandler(ev) {
            ev.preventDefault();
        }

        function downloadSVG() {
            var saveData = (function () {
                var a = document.createElement("a");
                document.body.appendChild(a);
                a.style = "display: none";
                return function (fileName) {
                    var blob = new Blob([currentOut], { type: "octet/stream" }),
                        url = window.URL.createObjectURL(blob);
                    a.href = url;
                    a.download = fileName;
                    a.click();
                    window.URL.revokeObjectURL(url);
                };
            }());
            saveData(new Date().getTime() + ".svg");
        }

        function changeBackgroundColor() {
            let newValue = prompt("请输入背景色", transformSetting.backgroundColor)
            if (newValue) {
                transformSetting.backgroundColor = newValue;
                $('#settingBackgroundColor').text(newValue)
                startTransform()
            }
        }

        function changeLoopCount() {
            let newValue = prompt("请输入动画循环次数（ 0 代表无限循环）", transformSetting.loopCount)
            if (newValue) {
                transformSetting.loopCount = newValue;
                $('#settingLoopCount').text(newValue === 0 ? "无限循环" : "循环" + newValue.toString() + "次")
                startTransform()
            }
        }

        function changeFillMode() {
            let newValue = prompt("请输入动画结束时的停留画面；终于首帧 = backward；终于末帧 = forward；清空 = clear", transformSetting.fillMode)
            if (newValue) {
                if (newValue !== "backward" && newValue !== "forward；清空" && newValue !== "clear") {
                    alert("非法值")
                    return;
                }
                transformSetting.fillMode = newValue;
                if (newValue === "backward") {
                    $('#settingFillMode').text("终于首帧")
                }
                else if (newValue === "forward") {
                    $('#settingFillMode').text("终于末帧")
                }
                else if (newValue === "clear") {
                    $('#settingFillMode').text("清空")
                }
                startTransform()
            }
        }

    </script>


</body>

</html>