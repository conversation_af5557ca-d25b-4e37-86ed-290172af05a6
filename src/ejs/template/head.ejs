<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  <!-- SEO 基础标签 -->
  <title>SVGA - 跨平台高性能动画解决方案 | iOS/Android/Flutter/Web/HarmonyOS动画格式</title>
  <meta name="description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。支持After Effects和Animate导出，动画文件体积小，播放资源占用优，设计师和开发者友好的跨平台动画解决方案。">
  <meta name="keywords" content="SVGA, 直播礼物动画, 高性能动画, 跨平台动画方案, 矢量动画, 透明MP4, After Effects, Animate, iOS动画, Android动画, Flutter动画, Web动画, 动画格式, 开源动画库">
  <meta name="author" content="SVGA Team">
  <meta name="robots" content="index, follow, max-snippet:160, max-image-preview:large, max-video-preview:-1">
  <meta name="publicPath" content="">
  
  <!-- 规范化URL -->
  <link rel="canonical" href="https://svga.dev/">
  
  <!-- 网站图标 -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/favicon.ico">
  
  <!-- Open Graph 标签 (Facebook) -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta property="og:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta property="og:url" content="https://svga.dev/">
  <meta property="og:site_name" content="SVGA">
  <meta property="og:image" content="https://svga.dev/img/index-example.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="SVGA动画效果展示">
  <meta property="og:locale" content="zh_CN">
  
  <!-- Twitter Card 标签 -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta name="twitter:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta name="twitter:image" content="https://svga.dev/img/index-example.jpg">
  <meta name="twitter:image:alt" content="SVGA动画效果展示">
  
  <!-- 语言和地区 -->
  <meta name="language" content="zh-CN">
  <meta name="geo.region" content="CN">
  
  <!-- 主题颜色 -->
  <meta name="theme-color" content="#4A90E2">
  <meta name="msapplication-TileColor" content="#4A90E2">
  
  <!-- 结构化数据 (JSON-LD) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "SVGA",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。",
    "url": "https://svga.dev/",
    "author": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://github.com/svga"
    },
    "operatingSystem": ["iOS", "Android", "Web", "Flutter"],
    "applicationCategory": "DeveloperApplication",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "downloadUrl": "https://github.com/svga",
    "screenshot": "https://svga.dev/img/index-example.jpg",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "500"
    },
    "programmingLanguage": ["JavaScript", "Swift", "Kotlin", "Dart"],
    "codeRepository": "https://github.com/svga"
  }
  </script>
  
  <!-- 面包屑导航结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "首页",
        "item": "https://svga.dev/"
      }
    ]
  }
  </script>
  
  <!-- DNS预取 -->
  <link rel="dns-prefetch" href="//github.com">
  <link rel="dns-prefetch" href="//pagead2.googlesyndication.com">
  
  <!-- 样式表 -->
  <!-- <style>canvas, header input { display: none; }</style> -->
  <link rel="stylesheet" href="/assets/font/amplesoft-bold.css">
  <link rel="stylesheet" href="/css/main.css">
  
  <!-- Google AdSense -->
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1514042314776212"
     crossorigin="anonymous"></script>
</head>