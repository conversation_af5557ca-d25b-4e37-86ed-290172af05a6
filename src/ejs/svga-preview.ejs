<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
  <link rel="stylesheet" href="/assets/v1/main-2018.css">
  <style>
    .preview-main.ext-bg {
      background-image: url("/assets/v1/frame.png");
    }
    .preview-main.ext-bg.is-active {
      background-image: url("/assets/v1/frame-active.png");
    }
    .popbox-close {
      background-image: url("/assets/v1/img-close.png");
    }
  </style>
  <link rel="stylesheet" href="/assets/swiper-3.4.2.min.css">
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/styles/default.min.css">
<%- include('./template/head.ejs') %>
  <style>
    header {
      z-index: 99;
    }
    .swiper-slide {
      background-color: #fff;
    }
    .preview-controls {
      width: 800px;
      overflow: visible;
    }
    .preview-controls__upload a,
    .preview-controls__download a {
      width: auto;
      min-width: 130px;
      padding: 0 5px;
    }
    .matter-info {
      overflow: scroll;
    }
  </style>
<body ontouchstart="">
<%- include('./template/header.ejs') %>
  <article id="preview">
    <div class="center">
      <!-- @extends prev version -->
      <div class="swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <div class="preview">
              <div class="preview-main ext-bg">
                <p data-i18n="preview-text-0">体验预览 SVGA 动画，请将文件拖拽到此区域</p>
                <div id="previewDemo" style="background-color: black; width: 0px; height: 0px"></div>
              </div>
              <div id="colorPicker" class="color-picker-button-group" style="display:none">
                <a id="color-picker-button-1" href="javascript:;" data-role="change-color"></a>
                <a id="color-picker-button-2" href="javascript:;" data-role="change-color"></a>
                <a id="color-picker-button-3" href="javascript:;" data-role="change-color"></a>
                <a id="color-picker-button-4" href="javascript:;" data-role="change-color"></a>
                <a id="color-picker-button-5" href="javascript:;" data-role="change-color"></a>
                <a id="color-picker-button-6" href="javascript:;" data-role="change-color"></a>
              </div>
              <div class="preview-controls">
                <div class="preview-controls__btn preview-controls__upload">
                  <input type="file" id="fs" style="display: none;" />
                  <a href="javascript:;" data-role="upload-file" data-i18n="preview-text-1">选择文件</a>
                </div>
                <div class="preview-controls__btn preview-controls__download" style="display:none">
                  <a id="previewResource" href="javascript:;" data-role="storage-videoItem" data-i18n="preview-text-2">浏览素材</a>
                </div>
                <div class="preview-controls__btn preview-controls__download">
                  <a id="downloadResource" href="javascript:;" data-role="download-file" data-i18n="preview-text-3">下载案例</a>
                </div>
                <div class="preview-controls__btn preview-controls__download" style="display:none">
                  <a id="changeVersion" href="javascript:;" data-role="download-file" data-i18n="preview-text-4">版本转换</a>
                </div>
                <!-- <div id="qrcode" class="preview-controls__btn preview-controls__download qrcode" style="display:none"><a href="#"><i><?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1551692066739" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1114" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs></defs><path d="M337.469045 552.015921 136.637579 552.015921c-73.916171 0-133.871518 59.945269-133.871518 133.897722L2.766061 886.745109c0 73.933304 59.955347 133.877565 133.871518 133.877565L337.469045 1020.622674c73.94943 0 133.904777-59.945269 133.904777-133.877565L471.373822 685.913643C471.373822 611.96119 411.419483 552.015921 337.469045 552.015921zM404.447134 853.277229c0 55.446199-44.962479 100.412709-100.432866 100.412709L170.126624 953.689939c-55.453254 0-100.415733-44.96651-100.415733-100.412709L69.710891 719.379507c0-55.463332 44.962479-100.408678 100.415733-100.408678l133.887644 0c55.469379 0 100.432866 44.946353 100.432866 100.408678L404.447134 853.277229zM973.449463 953.688931c-18.480051 0-33.471911 14.978758-33.471911 33.464856 0 18.490129 14.99186 33.468887 33.471911 33.468887 18.496176 0 33.50517-14.978758 33.50517-33.468887C1006.955641 968.667689 991.946647 953.688931 973.449463 953.688931zM270.525223 150.363068l-66.943822 0c-36.95607 0-66.943822 29.967595-66.943822 66.943822l0 66.933743c0 36.976227 29.987752 66.9539 66.943822 66.9539l66.943822 0c36.99336 0 66.943822-29.977674 66.943822-66.9539L337.469045 217.30689C337.469045 180.330663 307.518583 150.363068 270.525223 150.363068zM337.469045 16.46837 136.637579 16.46837c-73.916171 0-133.871518 59.941237-133.871518 133.894699l0 200.831466c0 73.943383 59.955347 133.887644 133.871518 133.887644L337.469045 485.082178c73.94943 0 133.904777-59.945269 133.904777-133.887644L471.373822 150.363068C471.373822 76.409607 411.419483 16.46837 337.469045 16.46837zM404.447134 317.729678c0 55.446199-44.962479 100.408678-100.432866 100.408678L170.126624 418.138356c-55.453254 0-100.415733-44.962479-100.415733-100.408678L69.710891 183.830948c0-55.456277 44.962479-100.418756 100.415733-100.418756l133.887644 0c55.469379 0 100.432866 44.962479 100.432866 100.418756L404.447134 317.729678zM873.03373 16.46837 672.222421 16.46837c-73.969587 0-133.887644 59.941237-133.887644 133.894699l0 200.831466c0 73.943383 59.918057 133.887644 133.887644 133.887644L873.03373 485.082178c73.986721 0 133.920903-59.945269 133.920903-133.887644L1006.954633 150.363068C1006.955641 76.409607 947.02045 16.46837 873.03373 16.46837zM939.977552 317.729678c0 55.446199-44.959455 100.408678-100.415733 100.408678L705.643939 418.138356c-55.423018 0-100.36534-44.962479-100.36534-100.408678L605.278599 183.830948c0-55.456277 44.942322-100.418756 100.36534-100.418756l133.917879 0c55.456277 0 100.415733 44.962479 100.415733 100.418756L939.977552 317.729678zM806.089908 150.363068l-66.940798 0c-36.99336 0-66.926688 29.967595-66.926688 66.943822l0 66.933743c0 36.976227 29.934336 66.9539 66.926688 66.9539l66.940798 0c36.960101 0 66.943822-29.977674 66.943822-66.9539L873.03373 217.30689C873.03373 180.330663 843.050009 150.363068 806.089908 150.363068zM605.278599 685.913643c-18.480051 0-33.489044 14.978758-33.489044 33.464856 0 18.490129 15.008994 33.468887 33.489044 33.468887l66.943822 0c18.476019 0 33.421518-14.978758 33.421518-33.468887l0-100.408678 33.50517 0c18.476019 0 33.454777-14.998915 33.454777-33.468887 0-18.486098-14.978758-33.485013-33.454777-33.485013L605.278599 552.015921c-18.480051 0-33.489044 14.998915-33.489044 33.485013 0 18.469972 15.008994 33.468887 33.489044 33.468887l33.471911 0 0 66.943822L605.278599 685.913643zM973.449463 552.015921c-18.480051 0-33.471911 14.998915-33.471911 33.485013l0 234.290274L806.089908 819.791208c-6.09753 0-11.812074 1.641798-16.738475 4.495038-4.927409-2.853241-10.643969-4.495038-16.746538-4.495038-18.480051 0-33.454777 14.998915-33.454777 33.485013l0 100.412709-100.399607 0L638.75051 886.745109c0-18.490129-14.995892-33.468887-33.471911-33.468887-18.480051 0-33.489044 14.978758-33.489044 33.468887l0 100.408678c0 18.490129 15.008994 33.468887 33.489044 33.468887l167.326295 0c18.493153 0 33.485013-14.978758 33.485013-33.468887L806.089908 886.745109l167.359555 0c18.496176 0 33.50517-14.998915 33.50517-33.468887L1006.954633 585.500934C1006.955641 567.014836 991.946647 552.015921 973.449463 552.015921zM806.089908 752.847387c18.480051 0 33.471911-14.978758 33.471911-33.468887 0-18.486098-14.99186-33.464856-33.471911-33.464856-18.476019 0-33.485013 14.978758-33.485013 33.464856C772.604895 737.868628 787.613889 752.847387 806.089908 752.847387zM270.525223 685.913643l-66.943822 0c-36.95607 0-66.943822 29.977674-66.943822 66.933743l0 66.943822c0 36.976227 29.987752 66.9539 66.943822 66.9539l66.943822 0c36.99336 0 66.943822-29.977674 66.943822-66.9539l0-66.943822C337.469045 715.891317 307.518583 685.913643 270.525223 685.913643z" p-id="1115"></path></svg></i><b data-i18n="preview-text-5">扫码预览</b><b data-i18n="preview-text-6">取消预览</b></a><ul><li><em><?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1551695125991" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3743" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M834.7648 736.3584a5.632 5.632 0 1 0 11.264 0 5.632 5.632 0 0 0-11.264 0z m-124.16 128.1024a11.1616 11.1616 0 1 0 22.3744 0 11.1616 11.1616 0 0 0-22.3744 0z m-167.3216 65.8944a16.7936 16.7936 0 1 0 33.6384 0 16.7936 16.7936 0 0 0-33.6384 0zM363.1616 921.6a22.3744 22.3744 0 1 0 44.7488 0 22.3744 22.3744 0 0 0-44.7488 0z m-159.744-82.0224a28.0064 28.0064 0 1 0 55.9616 0 28.0064 28.0064 0 0 0-56.0128 0zM92.672 700.16a33.6384 33.6384 0 1 0 67.2256 0 33.6384 33.6384 0 0 0-67.2256 0zM51.2 528.9984a39.168 39.168 0 1 0 78.336 0 39.168 39.168 0 0 0-78.336 0z m34.1504-170.0864a44.8 44.8 0 1 0 89.6 0 44.8 44.8 0 0 0-89.6 0zM187.904 221.7984a50.432 50.432 0 1 0 100.864 0 50.432 50.432 0 0 0-100.864 0zM338.432 143.36a55.9616 55.9616 0 1 0 111.9232 0 55.9616 55.9616 0 0 0-111.9744 0z m169.0112-4.9152a61.5936 61.5936 0 1 0 123.2384 0 61.5936 61.5936 0 0 0-123.2384 0z m154.7776 69.632a67.1744 67.1744 0 1 0 134.3488 0 67.1744 67.1744 0 0 0-134.3488 0z m110.0288 130.816a72.8064 72.8064 0 1 0 145.5616 0 72.8064 72.8064 0 0 0-145.5616 0z m43.7248 169.472a78.3872 78.3872 0 1 0 156.8256 0 78.3872 78.3872 0 0 0-156.8256 0z" fill="" p-id="3744"></path></svg></em><p><span data-i18n="preview-text-7">二维码正在生</span><br><span data-i18n="preview-text-8">成中，请稍等</span></p></li></ul></div> -->
              </div>
            </div>
          </div>
          <div class="swiper-slide">
            <div class="matter">
              <div class="matter-left">
                <div class="matter-preview" id="previewImage"></div>
                <div class="matter-info">
                  <div id="JSONDataDisplayer"></div>
                </div>
              </div>
              <div class="matter-right">
                <ul>
                  <li id="imageKeyListFirst">&nbsp;</li>
                  <li id="imageKeyListEnd">&nbsp;</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <!-- 如果需要导航按钮 -->
        <div id="swiperButtonPrev" class="swiper-button-prev" style="display:none"></div>
      </div>
      <div class="popbox">
        <div class="popbox-main" style="border-radius: 10px">
          <span class="popbox-close" data-role="popbox-close"></span>
          <blockquote class="alertViewTips" style="position: static; margin-top: 54px">
            <p class="alertViewTipsP" style="color:#123456;font-size:18px;">请选择您要下载的案例版本：</p>
          </blockquote>
          <ul class="alertViewButton" style="margin-top: 44px">
            <li>
              <a href="javascript:;" data-role="download-file1_0">1.0</a>
            </li>
            <li>
              <a href="javascript:;" data-role="download-file1_5">1.5</a>
            </li>
            <li>
              <a href="javascript:;" data-role="download-file2_0">2.0</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- @extends prev version -->
    </div>
  </article>
<%- include('./template/footer.ejs') %>
<%- include('./template/script.ejs') %>
  <script src="/assets/swiper-3.4.2.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.12.0/highlight.min.js"></script>
  <script>hljs.initHighlightingOnLoad()</script>
  <script src="/assets/jquery.min.js"></script>
  <script src="/assets/jszip-utils.min.js"></script>
  <script src="/assets/v1/preview-2018.js"></script>
  <script src="/assets/qrcode.min.js"></script>
  <script src="/assets/howler.min.js"></script>
  <script>
    var mySwiper = new Swiper('.swiper-container', {
      direction: 'horizontal',
      loop: false,
      effect: 'flip',
      swipeHandler: 'null',
      height: $(window).height() - 165 - 60,

      // 如果需要前进后退按钮
      prevButton: '.swiper-button-prev',

      onSlidePrevStart: function (swiper) {
        $('#swiperButtonPrev').css('display', "none");
        $('#previewDemo').css('display', "block");

        for (var imageKey in window.currentVideoItem.images) {
          $('#' + imageKey).remove();
        }
      }
    });
  </script>
</body>
</html>
