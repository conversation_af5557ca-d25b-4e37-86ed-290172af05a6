<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  <!-- SEO 基础标签 -->
  <title>SVGA - 跨平台高性能动画解决方案 | iOS/Android/Flutter/Web/HarmonyOS动画格式</title>
  <meta name="description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。支持After Effects和Animate导出，动画文件体积小，播放资源占用优，设计师和开发者友好的跨平台动画解决方案。">
  <meta name="keywords" content="SVGA, 直播礼物动画, 高性能动画, 跨平台动画方案, 矢量动画, 透明MP4, After Effects, Animate, iOS动画, Android动画, Flutter动画, Web动画, 动画格式, 开源动画库">
  <meta name="author" content="SVGA Team">
  <meta name="robots" content="index, follow, max-snippet:160, max-image-preview:large, max-video-preview:-1">
  <meta name="publicPath" content="">
  
  <!-- 规范化URL -->
  <link rel="canonical" href="https://svga.dev/">
  
  <!-- 网站图标 -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/favicon.ico">
  
  <!-- Open Graph 标签 (Facebook) -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta property="og:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta property="og:url" content="https://svga.dev/">
  <meta property="og:site_name" content="SVGA">
  <meta property="og:image" content="https://svga.dev/img/index-example.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:image:alt" content="SVGA动画效果展示">
  <meta property="og:locale" content="zh_CN">
  
  <!-- Twitter Card 标签 -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="SVGA - 跨平台高性能动画解决方案">
  <meta name="twitter:description" content="SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。">
  <meta name="twitter:image" content="https://svga.dev/img/index-example.jpg">
  <meta name="twitter:image:alt" content="SVGA动画效果展示">
  
  <!-- 语言和地区 -->
  <meta name="language" content="zh-CN">
  <meta name="geo.region" content="CN">
  
  <!-- 主题颜色 -->
  <meta name="theme-color" content="#4A90E2">
  <meta name="msapplication-TileColor" content="#4A90E2">
  
  <!-- 结构化数据 (JSON-LD) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "SVGA",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式，提供高性能动画播放体验。",
    "url": "https://svga.dev/",
    "author": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://github.com/svga"
    },
    "operatingSystem": ["iOS", "Android", "Web", "Flutter"],
    "applicationCategory": "DeveloperApplication",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "downloadUrl": "https://github.com/svga",
    "screenshot": "https://svga.dev/img/index-example.jpg",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "500"
    },
    "programmingLanguage": ["JavaScript", "Swift", "Kotlin", "Dart"],
    "codeRepository": "https://github.com/svga"
  }
  </script>
  
  <!-- 面包屑导航结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "首页",
        "item": "https://svga.dev/"
      }
    ]
  }
  </script>
  
  <!-- DNS预取 -->
  <link rel="dns-prefetch" href="//github.com">
  <link rel="dns-prefetch" href="//pagead2.googlesyndication.com">
  
  <!-- 样式表 -->
  <!-- <style>canvas, header input { display: none; }</style> -->
  <link rel="stylesheet" href="/assets/font/amplesoft-bold.css">
  <link rel="stylesheet" href="/css/main.css">
  
  <!-- Google AdSense -->
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1514042314776212"
     crossorigin="anonymous"></script>
</head>
<body ontouchstart="">
<header>
  <div class="center">
    <h2><a href="./index.html">SVGA</a></h2>
    <input type="checkbox" name="checkbox" id="checkbox">
    <div class="icon">
      <i></i>
      <i></i>
      <i></i>
      <i></i>
      <i></i>
    </div>
    <nav>
      <div class="tab">
        <div class="item">
          <div class="label">
            <a href="./intro.html"><em data-i18n="header-nav-0">介绍</em></a>
          </div>
        </div>
        <div class="item">
          <div class="label">
            <a href="./integrated.html"><em data-i18n="header-nav-1">集成指南</em></a>
          </div>
        </div>
        <div class="item">
          <div class="list">
            <div class="item">
              <div class="label">
                <a target="_blank" href="https://github.com/svga"><em data-i18n="header-nav-3">开源代码</em></a>
              </div>
            </div>
            <div class="item">
              <div class="label">
                <a href="./designer.html"><em data-i18n="header-nav-5">设计师工具</em></a>
              </div>
            </div>
            <div class="item pc-show mb-hide">
              <div class="label">
                <a href="./svga-preview.html"><em data-i18n="header-nav-6">在线播放器</em></a>
              </div>
            </div>
            <!-- <div class="item svgatools">
              <div class="label">
                <a href="https://www.nangonghan.com/svga/"><em data-i18n="header-nav-8">SVGA Tools</em></a>
              </div>
            </div> -->
            <div class="item">
              <div class="label">
                <a href="./article.html"><em data-i18n="header-nav-7">资源文章</em></a>
              </div>
            </div>
          </div>
          <div class="label"><em data-i18n="header-nav-2">社区生态</em></div>
        </div>
        <div class="item">
          <div class="list">
            <div class="item">
              <div class="label">
                <a href="/en/index.html" data-lang="en"><em>English</em></a>
              </div>
            </div>
            <div class="item">
              <div class="label">
                <a href="/index.html" data-lang="zh"><em>中文</em></a>
              </div>
            </div>
          </div>
          <div class="label"><em data-i18n="header-language">多语言</em></div>
        </div>
      </div>
    </nav>
  </div>
</header>

  <article id="integrated">
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-title-0">集成指南</h2>
            <h3><span data-i18n="integrated-title-1">将播放器集成至 </span><span data-i18n="integrated-title-2">iOS / Android / Flutter / Web / HarmonyOS</span></h3>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-ios-0">iOS</h2>
            <ul data-show="zh">
              <li>
                <p>使用 CocoaPods 集成源码，将以下依赖：</p>
              </li>
              <li>
                <p>
                  <code>pod 'SVGAPlayer'</code>
                </p>
              </li>
              <li>
                <p>添加至 Podfile 文件。</p>
              </li>
              <li>
                <p>使用代码或 IB 添加 SVGAPlayer 至 View 中，具体方法参见：<br><a target="_blank" href="https://github.com/svga/SVGAPlayer-iOS">https://github.com/svga/SVGAPlayer-iOS</a></p>
              </li>
            </ul>
            <ul data-show="en">
              <li>
                <p>Open <a target="_blank" href="https://github.com/svga/SVGAPlayer-iOS">https://github.com/svga/SVGAPlayer-iOS</a> to download the source code.</p>
              </li>
              <li>
                <p>Add the code in <code>Source</code> to your project.</p>
              </li>
              <li>
                <p>Create the SVGAPlayer instance to the UIView just like the example.</p>
              </li>
              <li>
                <p>You can also use CocoaPods, add the code:
                <p>
                  <code>pod 'SVGAPlayer'</code>
                </p>
                <p>to the Podfile of your project.</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-android-0">Android</h2>
            <ul data-show="zh">
              <li>
                <p>使用 Gradle 集成源码，添加 JitPack.io 到 root build.gradle 中 <br>
<pre><code>allprojects {
  repositories {
    ...
    maven { url 'https://jitpack.io' }
  }
}</code></pre></p>
              </li>
              <li>
                <p>添加以下依赖: </p>
                <p>
                  <pre><code>compile 'com.github.svga:SVGAPlayer-Android:2.0.0'</code></pre>
                </p>
              </li>
              <li>
                <p>根据需要修改版本号，要获取最新的版本请点入：<br><a href="https://jitpack.io/#svga/SVGAPlayer-Android">https://jitpack.io/#svga/SVGAPlayer-Android</a></p>
              </li>
              <li>
                <p>使用代码或 XML 添加 SVGAImageView 至 View 中，具体方法参见：<br><a target="_blank" href="https://github.com/svga/SVGAPlayer-Android">https://github.com/svga/SVGAPlayer-Android</a></p>
              </li>
            </ul>
            <ul data-show="en">
              <li>
                <p>Open <a target="_blank" href="https://github.com/svga/SVGAPlayer-Android">https://github.com/svga/SVGAPlayer-Android</a> to download the source code.</p>
              </li>
              <li>
                <p>Add the code in</p>
                <p>
                  <pre><code>library/src/main/java/com/opensource/svgaplayer</code></pre>
                </p>
                <p>to your project.</p>
              </li>
              <li>
                <p>Create the SVGAImageView for SVGAPlayer to the layout.xml just like the example.</p>
              </li>
              <li>
                <p>You can also use Gradle, add JitPack.io to root build.gradle <br><pre><code>allprojects {
  repositories {
    ...
    maven { url 'https://jitpack.io' }
  }
}</code></pre></p>
              </li>
              <li>
                <p>Add the following dependencies: </p>
                <p>
                  <pre><code>compile 'com.github.svga:SVGAPlayer-Android:1.2.4'</code>
<code>compile 'org.jetbrains.kotlin:kotlin-stdlib-jre7:1.1.1'</code></pre>
                </p>
              </li>
              <li>
                <p>Modify the version number as needed, and click in for the latest version <br><a href="https://jitpack.io/#svga/SVGAPlayer-Android">https://jitpack.io/#svga/SVGAPlayer-Android</a></p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-web-0">Web</h2>
            <ul data-show="zh">
              <li>
                <p>直接在：<a target="_blank" href="https://github.com/svga/SVGAPlayer-Web">https://github.com/svga/SVGAPlayer-Web</a>下载 build/svga.min.js，<br>并添加至目标页面。</p>
              </li>
              <li>
                <p>或使用 <code>npm install svgaplayerweb —save</code> 添加依赖，</p>
                <p>并在需要使用的 js 中添加 <code>require('svgaplayerweb')</code> 添加 Div 容器，并加载动画，</p>
                <p>具体方法参见：<a target="_blank" href="https://github.com/svga/SVGAPlayer-Web">https://github.com/svga/SVGAPlayer-Web</a>。</p>
              </li>
              <li>
                <p>我们还提供了体积更小的轻量版：<a target="_blank" href="https://github.com/svga/SVGAPlayer-Web-Lite">SVGA.Lite</a>。</p>
              </li>
            </ul>
            <ul data-show="en">
              <li>
                <p>Download <a target="_blank" href="https://github.com/svga/SVGAPlayer-Web/tree/master/build">build/svga.min.js & build/svga-worker.min.js</a></p>
              </li>
              <li>
                <p>HTML link or JS module require.</p>
              </li>
              <li>
                <p>There is another smaller version: <a target="_blank" href="https://github.com/svga/SVGAPlayer-Web-Lite">SVGA.Lite</a>.</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </article>
<footer>
  <div class="center">
    
    <!-- 友情链接区域 -->
    <section aria-label="友情链接" class="partner-links" style="text-align: center; margin:5px 0 0;">
      <nav aria-label="合作伙伴链接">
        <ul style="display: inline-block; margin: 0; padding: 0; list-style: none;">
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://codefont.dev" title="CodeFont - Font Switching Made Simple for VS Code" target="_blank" rel="noopener" 
               aria-label="Code Font">CodeFont</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://sline.dev" title="Sline - SHOPLINE Custom-Built E-commerce Template Engine" target="_blank" rel="noopener" 
               aria-label="Sline">Sline</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://colorcode.cc" title="ColorCode - 专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。支持 OKLCH、LAB、CMYK 等专业格式，ΔE≤0.5 精度保障" target="_blank" rel="noopener" 
               aria-label="ColorCode">ColorCode</a>
          </li>
          <li style="display: inline-block; margin: 0 8px;">
            <a href="https://zartify.com" title="Zartify - Premium HD Classic Paintings for Prints, Displays &amp; Inspiration" target="_blank" rel="noopener"
               aria-label="Zartify">Zartify</a>
          </li>
        </ul>
      </nav>
    </section>
    
    <p>Copyright &copy; 2016-2025 <a href="https://svga.dev" rel="noopener">SVGA Team</a></p>
  </div>
  
  <!-- 组织机构结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "SVGA Team",
    "url": "https://svga.dev",
    "logo": "https://svga.dev/favicon.ico",
    "description": "SVGA是一个开源的跨平台动画解决方案团队",
    "foundingDate": "2016",
    "sameAs": [
      "https://github.com/svga"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "technical support",
      "url": "https://github.com/svga/docs/issues"
    }
  }
  </script>
  
  <!-- 网站结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "SVGA",
    "url": "https://svga.dev",
    "description": "SVGA是一种同时兼容iOS/Android/Flutter/Web/HarmonyOS多个平台的动画格式",
    "inLanguage": "zh-CN",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://github.com/svga/docs/issues?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "SVGA Team",
      "url": "https://svga.dev"
    }
  }
  </script>
</footer>
<script src="/assets/jszip.min.js"></script>
<script src="/assets/svga.min.js"></script>
<script src="/js/main.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-8307426-22"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-8307426-22');
</script>
<script src="https://analytics.ahrefs.com/analytics.js" data-key="LPY8uaT1uYB8yUgv+xE/Uw" async></script>

</body>
</html>
