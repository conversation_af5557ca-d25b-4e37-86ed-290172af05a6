@mixin title-block {
  text-align: center;
  .container {
    padding: 15px 0 64px;
    @media (min-width: 960px) {
      padding: 96px 0;
    }
  }
  .col {
    width: 100%;
  }
  h2 {
    font-size: 48px;
    font-weight: 500;
    margin-bottom: 10px;
  }
  h3 {
    font-size: 18px;
    font-weight: 300;
    padding: 0 40px;
  }
}

@mixin content-block {
  .container {
    padding-top: 10px;
  }
  .col {
    margin: 0 auto;
    padding: 0 40px;
    min-width: 296px;
    max-width: 590px;
    &:last-child {
      ul {
        margin-bottom: 0;
      }
    }
  }
  h2 {
    font-size: 32px;
    line-height: 48px;
    margin-bottom: 20px;
    font-weight: 400;
  }
  ul {
    margin-bottom: 64px;
    @media (min-width: 960px) {
      margin-bottom: 0;
    }
  }
  li {
    list-style-type: disc;
    font-size: 16px;
    padding: 3px 0;
    color: #9e2fe4;
  }
  p {
    color: #444;
    line-height: 24px;
    @media (min-width: 768px) {
      line-height: 30px;
    }
  }
  a {
    font-weight: 700;
  }
}
