<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<%- include('./template/head.ejs') %>
<body ontouchstart="">
<%- include('./template/header.ejs') %>
  <article id="intro">
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="intro-title-0">介绍</h2>
          </div>
        </div>
      </div>
    </section>
    <section>
        <div class="center">
          <div class="container">
            <div class="col">
              <p><span data-i18n="intro-content-0">SVGA 是一种跨平台的开源动画格式，同时兼容 iOS / Android / Flutter / Web / HarmonyOS。</p>
                <p>SVGA 除了使用简单，性能卓越，同时让动画开发分工明确，各自专注各自的领域，大大减少动画交互的沟通成本，提升开发效率。</p>
                <p>动画设计师专注动画设计，通过工具输出 svga 动画文件，提供给开发工程师在集成 SVGAPlayer 之后直接使用。</p>
                <p>动画开发从未如此简单！</span><a href="./integrated.html" data-i18n="intro-button">&gt;&gt; 立即使用</a></p>
            </div>
          </div>
        </div>
      </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <img src="/img/intro-flow.jpg">
          </div>
        </div>
      </div>
    </section>
  </article>
<%- include('./template/footer.ejs') %>
<%- include('./template/script.ejs') %>
</body>
</html>
