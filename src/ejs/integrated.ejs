<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<%- include('./template/head.ejs') %>
<body ontouchstart="">
<%- include('./template/header.ejs') %>
  <article id="integrated">
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-title-0">集成指南</h2>
            <h3><span data-i18n="integrated-title-1">将播放器集成至 </span><span data-i18n="integrated-title-2">iOS / Android / Flutter / Web / HarmonyOS</span></h3>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-ios-0">iOS</h2>
            <ul data-show="zh">
              <li>
                <p>使用 CocoaPods 集成源码，将以下依赖：</p>
              </li>
              <li>
                <p>
                  <code>pod 'SVGAPlayer'</code>
                </p>
              </li>
              <li>
                <p>添加至 Podfile 文件。</p>
              </li>
              <li>
                <p>使用代码或 IB 添加 SVGAPlayer 至 View 中，具体方法参见：<br><a target="_blank" href="https://github.com/svga/SVGAPlayer-iOS">https://github.com/svga/SVGAPlayer-iOS</a></p>
              </li>
            </ul>
            <ul data-show="en">
              <li>
                <p>Open <a target="_blank" href="https://github.com/svga/SVGAPlayer-iOS">https://github.com/svga/SVGAPlayer-iOS</a> to download the source code.</p>
              </li>
              <li>
                <p>Add the code in <code>Source</code> to your project.</p>
              </li>
              <li>
                <p>Create the SVGAPlayer instance to the UIView just like the example.</p>
              </li>
              <li>
                <p>You can also use CocoaPods, add the code:
                <p>
                  <code>pod 'SVGAPlayer'</code>
                </p>
                <p>to the Podfile of your project.</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-android-0">Android</h2>
            <ul data-show="zh">
              <li>
                <p>使用 Gradle 集成源码，添加 JitPack.io 到 root build.gradle 中 <br>
<pre><code>allprojects {
  repositories {
    ...
    maven { url 'https://jitpack.io' }
  }
}</code></pre></p>
              </li>
              <li>
                <p>添加以下依赖: </p>
                <p>
                  <pre><code>compile 'com.github.svga:SVGAPlayer-Android:2.0.0'</code></pre>
                </p>
              </li>
              <li>
                <p>根据需要修改版本号，要获取最新的版本请点入：<br><a href="https://jitpack.io/#svga/SVGAPlayer-Android">https://jitpack.io/#svga/SVGAPlayer-Android</a></p>
              </li>
              <li>
                <p>使用代码或 XML 添加 SVGAImageView 至 View 中，具体方法参见：<br><a target="_blank" href="https://github.com/svga/SVGAPlayer-Android">https://github.com/svga/SVGAPlayer-Android</a></p>
              </li>
            </ul>
            <ul data-show="en">
              <li>
                <p>Open <a target="_blank" href="https://github.com/svga/SVGAPlayer-Android">https://github.com/svga/SVGAPlayer-Android</a> to download the source code.</p>
              </li>
              <li>
                <p>Add the code in</p>
                <p>
                  <pre><code>library/src/main/java/com/opensource/svgaplayer</code></pre>
                </p>
                <p>to your project.</p>
              </li>
              <li>
                <p>Create the SVGAImageView for SVGAPlayer to the layout.xml just like the example.</p>
              </li>
              <li>
                <p>You can also use Gradle, add JitPack.io to root build.gradle <br><pre><code>allprojects {
  repositories {
    ...
    maven { url 'https://jitpack.io' }
  }
}</code></pre></p>
              </li>
              <li>
                <p>Add the following dependencies: </p>
                <p>
                  <pre><code>compile 'com.github.svga:SVGAPlayer-Android:1.2.4'</code>
<code>compile 'org.jetbrains.kotlin:kotlin-stdlib-jre7:1.1.1'</code></pre>
                </p>
              </li>
              <li>
                <p>Modify the version number as needed, and click in for the latest version <br><a href="https://jitpack.io/#svga/SVGAPlayer-Android">https://jitpack.io/#svga/SVGAPlayer-Android</a></p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="center">
        <div class="container">
          <div class="col">
            <h2 data-i18n="integrated-web-0">Web</h2>
            <ul data-show="zh">
              <li>
                <p>直接在：<a target="_blank" href="https://github.com/svga/SVGAPlayer-Web">https://github.com/svga/SVGAPlayer-Web</a>下载 build/svga.min.js，<br>并添加至目标页面。</p>
              </li>
              <li>
                <p>或使用 <code>npm install svgaplayerweb —save</code> 添加依赖，</p>
                <p>并在需要使用的 js 中添加 <code>require('svgaplayerweb')</code> 添加 Div 容器，并加载动画，</p>
                <p>具体方法参见：<a target="_blank" href="https://github.com/svga/SVGAPlayer-Web">https://github.com/svga/SVGAPlayer-Web</a>。</p>
              </li>
              <li>
                <p>我们还提供了体积更小的轻量版：<a target="_blank" href="https://github.com/svga/SVGAPlayer-Web-Lite">SVGA.Lite</a>。</p>
              </li>
            </ul>
            <ul data-show="en">
              <li>
                <p>Download <a target="_blank" href="https://github.com/svga/SVGAPlayer-Web/tree/master/build">build/svga.min.js & build/svga-worker.min.js</a></p>
              </li>
              <li>
                <p>HTML link or JS module require.</p>
              </li>
              <li>
                <p>There is another smaller version: <a target="_blank" href="https://github.com/svga/SVGAPlayer-Web-Lite">SVGA.Lite</a>.</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </article>
<%- include('./template/footer.ejs') %>
<%- include('./template/script.ejs') %>
</body>
</html>
