@charset "UTF-8";

@import "base/base";

body {
  color: #444;
  font: 18px/1.5 "San Francisco", "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  a {
    color: #A457D4;
    text-decoration: none;
    &:hover {
      color: #A457D4;
      text-decoration: underline;
    }
  }
  * {
    outline: none;
    box-sizing: border-box;
    touch-action: manipulation;
    -webkit-touch-callout: none;
    backface-visibility: hidden;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }
}

@import "mixin";
@import "header";
@import "index";
@import "intro";
@import "mobile";
@import "article";
@import "designer";
@import "integrated";
@import "svga-preview";



html
body {
  background-color: #fff;
}


footer {
  height: 80px;
  border-top: 1px solid #e0e0e0;
  background-color: #fff;
  p {
    color: #999;
    text-align: center;
    line-height: 2;
    padding-bottom: 10px;
  }
}
.partner-links {
  padding: 10px 0;
  a {
    color: #A457D4;
    text-decoration: none;
    &:hover {
      color: #A457D4;
      text-decoration: underline;
    }
  }
}
article {
  padding-top: 54px;
  @media (min-width: 960px) {
    padding-top: 0;
  }
}

.center {
  max-width: 1200px;
  padding: 0 24px;
  margin: 0 auto;
  position: relative;
}

.container {
  padding: 64px 0;
  @media (min-width: 960px) {
    padding: 96px 0;
  }
  overflow: hidden;
  position: relative;
  &::after {
    content: "";
    clear: both;
    display: table;
  }
}

section {
  .center {
    max-width: 1200px;
    padding: 0 12px;
  }
}

.col {
  padding: 0 12px;
  position: relative;
  box-sizing: border-box;
  @media (min-width: 960px) {
    float: left;
  }
}
.col25 {
  @media (min-width: 960px) {
    width: 25%;
  }
}
.col50 {
  @media (min-width: 960px) {
    width: 50%;
  }
}

.clear {
  clear: both;
}

.align {
  text-align: center;
}

.mb-hide.pc-show {
  display: none;
  @media (min-width: 960px) {
    display: block;
  }
}

h1,
h2,
h3 {
  color: #222;
  font-weight: bold;
}

h1 {
  font-size: 48px;
}

h2 {
  font-size: 18px;
}

h3 {
  font-size: 18px;
}

img {
  max-width: 100%;
}

#index {
  @include index;
}

#intro {
  @include intro;
}

#mobile {
  @include mobile;
}

#article {
  @include article;
}

#designer {
  @include designer;
}

#integrated {
  @include integrated;
}

#preview {
  @include preview;
}
header nav .tab .list {
  opacity: 0;
  pointer-events: none;
  transform: translateY(-30px);
}