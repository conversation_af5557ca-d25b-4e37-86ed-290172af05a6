{"name": "svga_homepage", "version": "1.2.0", "author": "UED.lixinliang", "description": "SVGA官网", "license": "UNLICENSED", "scripts": {"dev": "lf dev", "build": "lf build", "i18n:preview": "node ./shell/build.js --publicPath=''", "i18n:uxtest": "node ./shell/build.js --publicPath='//ux-test.yy.com/svga_homepage'", "i18n:ux": "node ./shell/build.js --publicPath='//ux.yy.com/svga_homepage'", "i18n:gpage": "node ./shell/build.js --publicPath='//svga.dev'", "i18n:production": "node ./shell/build.js --publicPath='todo'", "preview": "npm run build && npm run i18n:preview && http-server ./dist -p 4000"}, "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.4", "@types/vuex-i18n": "^1.10.0", "argv": "0.0.2", "axios": "^0.26.0", "jsdom": "^13.2.0", "vue": "^2.5.16", "vue-class-component": "^6.2.0", "vue-property-decorator": "^6.1.0", "vue-router": "^3.0.1", "vuex": "^3.0.1", "vuex-i18n": "^1.11.0", "yypkg": "^2.0.20"}, "devDependencies": {"node-sass": "^7.0.1", "sass-loader": "^12.4.0"}}