/*
 * @file: 重置样式
 */

/* 全局设置 */
*,
*:before,
*:after {
  box-sizing: border-box;
  font-family: "amplesoft-bold", "San Francisco", "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  font-weight: 300;
  appearance: none;

  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* 设置基本html,body样式 */
html,
body {
  padding: 0;
  margin: 0 auto;
}

/* 设置图片最大宽度 */
img {
  max-width: 100%;
}

/* 清除默认按钮表现形式 */
button {
          appearance: none;

  -webkit-appearance: none;
}

/* 设置表格默认样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 文本域禁止拖拉放大 */
textarea {
  resize: none;
}

a {
  color: currentColor;
  text-decoration: none;
}

img {
  vertical-align: top;
}

i,
em {
  font-style: normal;
}

ol,
ul,
li,
menu {
  list-style: none outside none;
}

fieldset,
iframe,
abbr,
acronym {
  border: 0 none;
}

dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
button,
textarea,
p,
blockquote,
th,
td,
hr,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  padding: 0;
  margin: 0;
}

del {
  text-decoration: line-through;
}
