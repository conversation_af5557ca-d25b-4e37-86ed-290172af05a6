import axios from 'axios'
import util from '../public/utils'

declare const window: any
declare const process: any
declare const document: any

export default function preview () {

  const preview = document.querySelector('#preview')

  if (!preview) {
    return
  }

  // Map<File, string> 记录 上传的 svga 对应的 filename
  let supportMap: boolean = false

  if (window.Map) {
    try {
      const map: Map<any, any> = new window.Map
      if (map instanceof window.Map &&
        Object.prototype.toString.call(map).slice(8, -1) === 'Map') {
        supportMap = true
      }
    } catch (error) {
    }
  }  

  let map: Map<File, string>
  if (supportMap) {
    map = new window.Map
  }

  // hack 修正 样式问题
  {
    const matter: HTMLElement = document.querySelector('.matter') as HTMLElement
    const preview: HTMLElement = document.querySelector('.preview') as HTMLElement
    const wrapper: HTMLElement = document.querySelector('.swiper-wrapper') as HTMLElement
    util.loop(() => {
      const height: number = Math.max(
        window.parseFloat(getComputedStyle(matter).height),
        window.parseFloat(getComputedStyle(preview).height)
      )
      wrapper.style['height'] = `${height}px`
    })
  }

  const qrcode: HTMLElement = document.querySelector('#qrcode') as HTMLElement
  const input: HTMLInputElement = document.querySelector('input[type="file"]') as HTMLInputElement
  const displayboard: HTMLElement = document.querySelector('.preview-main') as HTMLElement

  // test
  // qrcode.style['display'] = 'inline-block'

  // 记录 file
  let file: File
  // 记录 active 状态
  let active: boolean = false
  // 记录 action id 递增，多次提交 file 以最后一次为准
  let actionId: number = 0

  // 重置 button 状态
  function reset () {
    active = false
    qrcode.classList.remove('active')
    const img: HTMLElement = qrcode.querySelector('img') as HTMLElement
    const canvas: HTMLElement = qrcode.querySelector('canvas') as HTMLElement
    if (img) {
      img.remove()
    }
    if (canvas) {
      canvas.remove()
    }
  }

  // 监听 drop
  // displayboard.addEventListener('drop', (event: DragEvent) => {
  //   const data: DataTransfer = event.dataTransfer as DataTransfer
  //   if (data) {
  //     const files: FileList = data.files as FileList
  //     file = files[0] || file
  //   }
  //   if (qrcode.classList.contains('active')) {
  //     reset()
  //     return
  //   }
  // }, false)

  // 监听 input change 记录 file
  input.addEventListener('change', () => {
    const files: FileList = input.files as FileList
    file = files[0] || file
    if (qrcode.classList.contains('active')) {
      reset()
      return
    }
  }, false)

  // qrcode.addEventListener('click', async () => {

  //   if (qrcode.classList.contains('active')) {
  //     reset()
  //     return
  //   }

  //   if (!file) {
  //     return
  //   }

  //   actionId++
  //   active = true
  //   qrcode.classList.add('active')

  //   // test
  //   // return

  //   let currentId = actionId

  //   let filename: string = ``

  //   if (supportMap) {
  //     filename = map.get(file) || ''
  //   }

  //   if (!filename) {
  //     const data = new FormData()
  //     data.append('file', file)
  //     const result = await axios.post('//legox.org/service/upload', data, {
  //       headers: {
  //         'Content-Type': 'multipart/form-data',
  //         'Access-Control-Allow-Origin': '*'
  //       }
  //     })
  //     if (supportMap) {
  //       map.set(file, result.data)
  //     }
  //     filename = result.data
  //   }

  //   if (!active) {
  //     return
  //   }
  //   if (currentId !== actionId) {
  //     return
  //   }

  //   if (!filename) {
  //     return
  //   }

  //   const url: string = `https://legox.org/upload/${filename}`
  //   const protocol: string = process.env === 'dev' ? 'http:' : 'https:'
  //   const text: string = `${protocol}${util.publicPath('mobile.html')}?svga=${encodeURIComponent(url)}`

  //   if (process.env === 'dev') {
  //     console.log(text)
  //   }

  //   new window.QRCode(document.querySelector('#qrcode li'), {
  //     text,
  //     width: 140,
  //     height: 140,
  //     colorDark : '#000000',
  //     colorLight : '#ffffff',
  //     correctLevel : window.QRCode.CorrectLevel.H
  //   })

  // },false)

}
