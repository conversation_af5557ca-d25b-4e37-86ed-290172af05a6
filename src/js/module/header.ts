import i18n from '../../i18n'

declare const process: any
declare const document: any

const English: string = 'en'
const Chinese: string = 'zh'

let lang: string = Chinese

type i18nValue = {
  [key: string]: string
}

function changeLanguage () {
  {
    const list: NodeList = document.querySelectorAll('[data-i18n]')
    list.forEach((node: Node) => {
      const element: HTMLElement = node as any as HTMLElement
      const key: string = element.dataset.i18n as string
      if (!key) {
        return
      }
      const value: i18nValue = i18n[key]
      if (!value) {
        return
      }
      const text: string = value[lang]
      if (typeof text !== 'string') {
        return
      }
      element.innerText = text
    })
  }
  {
    const list: NodeList = document.querySelectorAll('[data-show]')
    list.forEach((node: Node) => {
      const element: HTMLElement = node as any as HTMLElement
      element.style['display'] = element.dataset.show === lang ? 'block' : 'none'
    })
  }
}

export default function header () {

  const intro = document.querySelector('#intro')
  if (intro) {
    document.querySelector('nav a[href*="intro.html"]').parentElement.parentElement.classList.add('active')
  }
  const integrated = document.querySelector('#integrated')
  if (integrated) {
    document.querySelector('nav a[href*="integrated.html"]').parentElement.parentElement.classList.add('active')
  }
  const designer = document.querySelector('#designer')
  if (designer) {
    document.querySelector('nav a[href*="designer.html"]').parentElement.parentElement.classList.add('active')
  }
  const preview = document.querySelector('#preview')
  if (preview) {
    document.querySelector('nav a[href*="preview.html"]').parentElement.parentElement.classList.add('active')
  }
  const article = document.querySelector('#article')
  if (article) {
    document.querySelector('nav a[href*="article.html"]').parentElement.parentElement.classList.add('active')
  }

  const nav: HTMLElement = document.querySelector('nav') as HTMLElement

  if (process.env !== 'dev') {
    // 生产环境 多语言切换 跳转地址

    const result: RegExpMatchArray | null = window.location.pathname.match(/[-\w]+?\.html$/)

    if (!result) {
      return
    }

    const base: RegExp = /index\.html/
    const current: string = result[0]

    {
      const anchor: HTMLAnchorElement = nav.querySelector('a[data-lang="en"]') as HTMLAnchorElement
      const href: string = anchor.getAttribute('href') || ''
      anchor.setAttribute('href', href.replace(base, current))
    }
    {
      const anchor: HTMLAnchorElement = nav.querySelector('a[data-lang="zh"]') as HTMLAnchorElement
      const href: string = anchor.getAttribute('href') || ''
      anchor.setAttribute('href', href.replace(base, current))
    }

  }

  if (process.env === 'dev') {
    // 开发环境 多语言切换 执行 changeLanguage 方法

    document.documentElement.classList.add(Chinese)

    nav.addEventListener('click', (event: Event) => {
      const target: HTMLElement = event.target as HTMLElement
      const name: string = target.tagName.toLowerCase()
      let node: HTMLElement | null = null
      if (name === 'a') {
        node = target
      }
      if (name === 'em') {
        node = target.parentElement as HTMLElement
      }
      if (!node) {
        return
      }
      if (node.dataset.lang === English) {
        event.preventDefault()
        lang = English
        changeLanguage()
        document.documentElement.classList.remove(Chinese)
        document.documentElement.classList.add(English)
        return
      }
      if (node.dataset.lang === Chinese) {
        event.preventDefault()
        lang = Chinese
        changeLanguage()
        document.documentElement.classList.remove(English)
        document.documentElement.classList.add(Chinese)
        return
      }
    }, false)
  }

}
