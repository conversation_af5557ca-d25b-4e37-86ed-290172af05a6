
header {
  top: 0;
  left: 0;
  width: 100%;
  height: 54px;
  position: fixed;
  background-color: #fff;
  z-index: 1;
  .ios & {
    background-color: transparentize($color: #fff, $amount: .1);
    backdrop-filter: blur(6px);
  }
  @media (min-width: 960px) {
    height: 72px;
    position: relative;
  }
  h2 {
    font-family: "amplesoft-bold", "San Francisco", "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
    top: 0;
    left: 0;
    height: 54px;
    overflow: hidden;
    cursor: pointer;
    position: absolute;
    font-size: 34px;
    @media (min-width: 960px) {
      height: 72px;
      font-size: 48px;
    }
    a {
      height: 60px;
      line-height: 60px;
      display: block;
      padding: 0 24px;
      color: #a457d4;
      &:hover {
        color: #A457D4;
        text-decoration: none;
      }
      @media (min-width: 960px) {
        height: 78px;
        line-height: 78px;
      }
    }
  }
  input[type="checkbox"] {
    top: 0;
    right: 0;
    width: 54px;
    height: 54px;
    cursor: pointer;
    position: absolute;
    opacity: 0;
    display: block;
    @media (min-width: 960px) {
      display: none;
    }
    &:checked {
      & ~ nav {
        display: block;
      }
      & + .icon {
        i {
          &:nth-child(1) {
            transform: scaleX(0);
          }
          &:nth-child(2) {
            top: -3px;
            left: -7px;
            transform: rotate(45deg);
          }
          &:nth-child(3) {
            top: 3px;
            left: -7px;
            transform: rotate(-45deg);
          }
          &:nth-child(4) {
            top: -3px;
            left: -1px;
            transform: rotate(-45deg);
          }
          &:nth-child(5) {
            top: 3px;
            left: -1px;
            transform: rotate(45deg);
          }
        }
      }
    }
  }
  .icon {
    top: 27px;
    right: 36px;
    width: 0;
    height: 0;
    color: #444;
    position: absolute;
    pointer-events: none;
    @media (min-width: 960px) {
      display: none;
    }
    i {
      transition: all .4s;
      position: absolute;
      background-color: currentColor;
      &:nth-child(1) {
        left: -12px;
        width: 24px;
        height: 1px;
        // transition: all .2s;
      }
      &:nth-child(2) {
        top: -8px;
        left: -12px;
        width: 12px;
        height: 1px;
      }
      &:nth-child(3) {
        top: 8px;
        left: -12px;
        width: 12px;
        height: 1px;
      }
      &:nth-child(4) {
        top: -8px;
        left: 0;
        width: 12px;
        height: 1px;
      }
      &:nth-child(5) {
        top: 8px;
        left: 0;
        width: 12px;
        height: 1px;
      }
    }
  }
}

header nav {
  top: 54px;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  position: fixed;
  overflow: scroll;
  z-index: 1;
  background-color: #fff;
  .ios & {
    background-color: transparentize($color: #fff, $amount: .1);
    backdrop-filter: blur(6px);
  }
  @media (min-width: 960px) {
    top: 0;
    display: block;
    float: right;
    position: relative;
    overflow: visible;
    background: transparent;
  }
  .tab {
    padding: 0 24px;
    @media (min-width: 960px) {
      padding: 0;
      height: 72px;
    }
    & > .item {
      @media (min-width: 960px) {
        float: left;
        top: 11px;
        position: relative;
        height: 50px;
        margin: 0 12px;
        &:hover {
          text-decoration: none;
          .list {
            opacity: 1;
            pointer-events: auto;
            transform: translate3d(0, 0, 0);
          }
        }
      }
    }
  }
  .item {
    position: relative;
    &:hover,
    &.active {
      & > .label {
        em {
          &::after {
            transform: scaleX(1);
          }
        }
        &, a {
          color: #A457D4;
          text-decoration: none;
        }
      }
    }
    &.active {
      pointer-events: none;
      cursor: default;
    }
  }
  .label {
    line-height: 2.7;
    cursor: pointer;
    @media (min-width: 960px) {
      height: 50px;
      line-height: 50px;
      padding: 0 12px;
      position: relative;
    }
  }
  .list {
    opacity: 0;
    pointer-events: none;
    transform: translateY(-30px);
    // padding: 0 0 0 12px;
    @media (max-width: 959px) {
      // opacity: 1;
      // pointer-events: auto;
      // transform: translate3d(0, 0, 0);
    }
    @media (min-width: 960px) {
      // padding: 0 0;
      padding: 10px 0;
      text-align: center;
      position: absolute;
      transition: all .4s;
      width: 140px;
      border-radius: 4px;
      top: 50px;
      left: -20px;
      background-color: #fff;
      box-shadow: 0 0 15px -1px transparentize($color: #000, $amount: .9);
      // opacity: 0;
      // pointer-events: none;
      // transform: translateY(-30px);
      // opacity: 1;
      // pointer-events: auto;
      // transform: translate3d(0, 0, 0);
      &::before {
        top: -4px;
        left: 50%;
        width: 0;
        height: 0;
        margin-left: -5px;
        content: "";
        border-width: 5px;
        border-style: solid;
        border-color: #fff transparent transparent #fff;
        position: absolute;
        transform: rotate(45deg);
        border-radius: 3px;
        pointer-events: none;
        box-shadow: 0 0 15px -1px transparentize($color: #000, $amount: .9);
      }
      &::after {
        top: 0;
        left: 0;
        width: 100%;
        height: 8px;
        content: "";
        background-color: #fff;
        position: absolute;
        pointer-events: none;
        border-radius: 4px;
      }
    }
    em {
      &::after {
        bottom: 10px;
      }
    }
    & + .label {
      display: none;
    }
    @media (min-width: 960px) {
      & + .label {
        display: block;
      }
    }
  }
  a {
    display: block;
    margin: 0 -12px;
    padding: 0 12px;
    color: #444;
  }
  em {
    display: inline-block;
    position: relative;
    &::after {
      left: -3px;
      right: -3px;
      bottom: 10px;
      height: 1px;
      transform: scaleX(0);
      transition: transform .4s .1s;
      background-color: #A457D4;
      position: absolute;
    }
    @media (min-width: 960px) {
      &::after {
        content: "";
      }
    }
  }
}

header {
  input[type="checkbox"] {
    &:checked {
      & ~ nav {
        .list {
          @media (max-width: 959px) {
            opacity: 1;
            pointer-events: auto;
            transform: translate3d(0, 0, 0);
          }
        }
      }
    }
  }
}
