import yypkg from 'yypkg'
import util from '../public/utils'

declare const window: any
declare const document: any

export default function mobile () {

  const mobile = document.querySelector('#mobile')

  if (!mobile) {
    return
  }

  const svga: string = yypkg.getUrlParam('svga') || ''

  const h3: HTMLElement = document.querySelector('h3') as HTMLElement

  if (!svga) {
    h3.style['opacity'] = `1`
    return
  }
  
  const parser = new window.SVGA.Parser()
  const player = new window.SVGA.Player('#canvas')

  window.addEventListener('error', (event: ErrorEvent) => {
    if (/XMLHttpRequest/.test(event.error.stack)) {
      h3.style['opacity'] = `1`
    }
  }, false)

  parser.load(svga, async (item: any) => {
    const canvas: HTMLCanvasElement = document.querySelector('canvas') as HTMLCanvasElement
    const style: CSSStyleDeclaration = window.getComputedStyle(canvas)
    const size: number = window.parseFloat(style.width)
    const { width, height }: { width: number, height: number } = item.videoSize
    if (width > height) {
      canvas.width = size
      canvas.height = size * height / width + 1
    } else {
      canvas.height = size
      canvas.width = size * width / height + 1
    }
    canvas.style['width'] = `${canvas.width}px`
    canvas.style['height'] = `${canvas.height}px`
    await util.sleep(500)
    player.setVideoItem(item)
    player.startAnimation()

  })

}
