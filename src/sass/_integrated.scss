@mixin integrated {
  section {
    &:nth-child(1) {
      @include title-block;
      h2 {
        .en & {
          font-size: 34px;
          margin-left: -20px;
          margin-right: -20px;
          @media (min-width: 375px) {
            font-size: 40px;
          }
          @media (min-width: 425px) {
            font-size: 46px;
          }
          @media (min-width: 450px) {
            font-size: 48px;
          }
        }
      }
      h3 {
        padding: 0;
        span {
          display: block;
          @media (min-width: 375px) {
            display: inline;
          }
        }
      }
    }
    &:nth-child(2),
    &:nth-child(3),
    &:nth-child(4) {
      @include content-block;
      .col {
        float: none;
        padding: 0 30px;
        @media (min-width: 768px) {
          padding: 0 40px;
        }
      }
      a {
        word-break: break-word;
      }
    }
    &:nth-child(2) {
      margin-top: -40px;
      @media (min-width: 960px) {
        margin-top: 0;
      }
    }
    &:nth-child(3),
    &:nth-child(4) {
      margin-top: -50px;
    }
  }
  pre,
  code {
    display: inline-block;
    line-height: 18px;
    padding: 2px 4px;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 4px;
    &:empty {
      display: none;
    }
  }
  pre {
    display: block;
    overflow: scroll;
    width: 100%;
    padding: 6px 4px;
  }
}
